#!/usr/bin/env python3
"""
Test script to check dashboard timeframe consensus data
"""

import requests
import json
import time

def test_dashboard_api():
    """Test the dashboard API to see what timeframe consensus data is being sent."""
    
    try:
        # Make request to dashboard API
        response = requests.get('http://localhost:5000/api/data', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print("=== DASHBOARD API RESPONSE ===")
            print(f"Status Code: {response.status_code}")
            print(f"Response Keys: {list(data.keys())}")
            
            # Check if timeframe_consensus exists
            if 'timeframe_consensus' in data:
                print("\n=== TIMEFRAME CONSENSUS DATA ===")
                timeframe_consensus = data['timeframe_consensus']
                print(f"Timeframe Consensus Keys: {list(timeframe_consensus.keys())}")
                
                for timeframe, consensus in timeframe_consensus.items():
                    print(f"\n{timeframe.upper()}:")
                    print(f"  Signal: {consensus.get('consensus_signal', 'N/A')}")
                    print(f"  Strength: {consensus.get('consensus_strength', 0):.1f}%")
                    print(f"  Confidence: {consensus.get('avg_confidence', 0):.3f}")
                    print(f"  Contributing Models: {consensus.get('contributing_models', 0)}/3")
                    print(f"  Daily Trades: {consensus.get('daily_trades', 0)}")
                    print(f"  Monthly Trades: {consensus.get('monthly_trades', 0)}")
                    
                    strong_signals = consensus.get('strong_signals', [])
                    if strong_signals:
                        print(f"  Strong Signals: {len(strong_signals)}")
                        for signal in strong_signals:
                            print(f"    - {signal.get('model', 'Unknown')}: {signal.get('type', 'N/A')} ({signal.get('confidence', 0):.1%})")
                    else:
                        print(f"  Strong Signals: None")
            else:
                print("\n❌ NO TIMEFRAME_CONSENSUS DATA FOUND!")
                print("Available keys:", list(data.keys()))
                
        else:
            print(f"❌ API Request Failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection Error: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🔍 Testing Dashboard Timeframe Consensus Data...")
    print("📡 Making API request to http://localhost:5000/api/data")
    print("=" * 60)
    
    test_dashboard_api()
    
    print("\n" + "=" * 60)
    print("✅ Test completed!")
