"""
Daily Reset Status Checker
Check if the AI trading system has properly reset for the new day.
"""

import sys
import logging
from datetime import datetime, date
import MetaTrader5 as mt5

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/daily_reset_check.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("DailyResetChecker")

def check_daily_reset_status():
    """Check the current daily reset status of the trading system."""
    print("🔍 CHECKING DAILY RESET STATUS")
    print("=" * 60)
    
    current_date = datetime.now().date()
    current_time = datetime.now().time()
    
    print(f"📅 Current Date: {current_date}")
    print(f"🕐 Current Time: {current_time}")
    print()
    
    try:
        # Import components
        from order_execution_system import OrderExecutionSystem
        from synthetic_data_collector import SyntheticDataCollector
        
        print("🔧 Initializing components...")
        data_collector = SyntheticDataCollector()
        order_executor = OrderExecutionSystem(data_collector)
        
        if not order_executor.mt5_connected:
            print("❌ MT5 not connected!")
            return False
            
        print("✅ MT5 connected")
        print()
        
        # Check reset status
        print("📊 DAILY RESET STATUS:")
        print("-" * 40)
        
        # Check last reset date
        last_reset = order_executor.last_reset_date
        print(f"📅 Last Reset Date: {last_reset}")
        print(f"📅 Current Date: {current_date}")
        
        if last_reset == current_date:
            print("✅ System HAS reset for today")
            reset_status = "RESET"
        else:
            print("❌ System has NOT reset for today")
            reset_status = "NOT_RESET"
            
        print()
        
        # Check daily counters
        print("🔢 DAILY COUNTERS:")
        print("-" * 40)
        print(f"📊 Daily Trade Count: {order_executor.daily_trade_count}")
        print(f"💰 Daily P&L: ${order_executor.daily_pnl:.2f}")
        
        # Check timeframe counters (NO LIMITS - TRACKING ONLY)
        if hasattr(order_executor, 'timeframe_daily_trades'):
            print("\n📊 TIMEFRAME DAILY TRADES (NO LIMITS):")
            print("-" * 40)
            for timeframe, count in order_executor.timeframe_daily_trades.items():
                print(f"   {timeframe}: {count} trades (unlimited)")
        
        # Check timeframe positions
        if hasattr(order_executor, 'timeframe_positions'):
            print("\n🎯 TIMEFRAME POSITIONS:")
            print("-" * 40)
            for timeframe, position_id in order_executor.timeframe_positions.items():
                status = "ACTIVE" if position_id else "AVAILABLE"
                print(f"   {timeframe}: {status} ({position_id})")
        
        # Check active positions
        print(f"\n📈 ACTIVE POSITIONS: {len(order_executor.active_positions)}")
        if order_executor.active_positions:
            for pos_id, position in order_executor.active_positions.items():
                print(f"   Position {pos_id}: {position.profit:.2f}")
        
        # Check MT5 account status
        print("\n💰 MT5 ACCOUNT STATUS:")
        print("-" * 40)
        try:
            account_info = mt5.account_info()
            if account_info:
                print(f"   Balance: ${account_info.balance:.2f}")
                print(f"   Equity: ${account_info.equity:.2f}")
                print(f"   Margin: ${account_info.margin:.2f}")
                print(f"   Free Margin: ${account_info.margin_free:.2f}")
            else:
                print("   Could not get account info")
        except Exception as e:
            print(f"   Error getting account info: {e}")
        
        # Check for open positions in MT5
        print("\n📊 MT5 OPEN POSITIONS:")
        print("-" * 40)
        try:
            positions = mt5.positions_get()
            if positions:
                print(f"   Total positions: {len(positions)}")
                for pos in positions:
                    print(f"   Position {pos.ticket}: {pos.type_str} {pos.volume} lots, P&L: ${pos.profit:.2f}")
            else:
                print("   No open positions")
        except Exception as e:
            print(f"   Error checking positions: {e}")
        
        # Check daily loss limit
        max_daily_loss = 20.0  # $20 limit
        print(f"\n🚨 DAILY LOSS LIMIT CHECK:")
        print("-" * 40)
        print(f"   Current Daily P&L: ${order_executor.daily_pnl:.2f}")
        print(f"   Daily Loss Limit: -${max_daily_loss:.2f}")
        
        if order_executor.daily_pnl < -max_daily_loss:
            print("   🚨 DAILY LOSS LIMIT EXCEEDED - Trading should be blocked")
            loss_limit_status = "EXCEEDED"
        else:
            remaining = max_daily_loss + order_executor.daily_pnl
            print(f"   ✅ Loss limit OK - ${remaining:.2f} remaining")
            loss_limit_status = "OK"
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 SUMMARY:")
        print("=" * 60)
        print(f"🔄 Reset Status: {reset_status}")
        print(f"🔢 Daily Trades: {order_executor.daily_trade_count}/30")
        print(f"💰 Daily P&L: ${order_executor.daily_pnl:.2f}")
        print(f"🚨 Loss Limit: {loss_limit_status}")
        print(f"📊 Active Positions: {len(order_executor.active_positions)}")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS:")
        print("-" * 40)
        
        if reset_status == "NOT_RESET":
            print("❌ System needs manual reset or restart")
            print("   Run: python reset_daily_limits.py")
            print("   Or restart the trading system")
        elif loss_limit_status == "EXCEEDED":
            print("❌ Daily loss limit exceeded")
            print("   System should not trade until tomorrow")
            print("   Or run manual reset if appropriate")
        # REMOVED: Daily trade limit check - No limits on number of trades per day
        # elif order_executor.daily_trade_count >= 30:
        #     print("❌ Daily trade limit reached")
        #     print("   System should not trade until tomorrow")
        elif len(order_executor.active_positions) > 0:
            print("⚠️  Active positions detected")
            print("   System may be waiting for positions to close")
        else:
            print("✅ System appears ready for trading")
            print("   Check if AI models are generating signals")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking reset status: {e}")
        logger.error(f"Reset check failed: {e}")
        return False

def main():
    """Main function."""
    print("🔍 AI TRADING SYSTEM - DAILY RESET STATUS CHECK")
    print("=" * 60)
    print("This script checks if the system has properly reset for the new day")
    print("and identifies any issues preventing trading.")
    print("=" * 60)
    
    success = check_daily_reset_status()
    
    if success:
        print("\n✅ RESET STATUS CHECK COMPLETED!")
    else:
        print("\n❌ RESET STATUS CHECK FAILED!")
        
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
