#!/usr/bin/env python3
"""
Analyze AI Model Decisions History
Shows what decisions different models made throughout the night.
"""

import os
import pickle
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List

def load_prediction_logs() -> Dict:
    """Load prediction logs from disk."""
    try:
        logs_file = "models/performance/prediction_logs.pkl"
        if os.path.exists(logs_file):
            with open(logs_file, 'rb') as f:
                return pickle.load(f)
        else:
            print("❌ No prediction logs found")
            return {}
    except Exception as e:
        print(f"❌ Error loading prediction logs: {e}")
        return {}

def analyze_model_decisions():
    """Analyze model decisions throughout the night."""
    print("🔍 ANALYZING AI MODEL DECISIONS HISTORY")
    print("=" * 60)
    
    # Load prediction logs
    prediction_logs = load_prediction_logs()
    
    if not prediction_logs:
        print("📊 No prediction history available")
        print("💡 This is normal if the system just started")
        return
    
    print(f"📚 Found prediction logs for {len(prediction_logs)} models")
    print()
    
    # Analyze each model's decisions
    for model_name, predictions in prediction_logs.items():
        if not predictions:
            continue
            
        print(f"🧠 MODEL: {model_name}")
        print("-" * 40)
        
        # Get recent predictions (last 24 hours)
        recent_predictions = []
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        for pred in predictions:
            pred_time = pred.get('timestamp')
            if isinstance(pred_time, str):
                pred_time = pd.to_datetime(pred_time)
            elif hasattr(pred_time, 'to_pydatetime'):
                pred_time = pred_time.to_pydatetime()
                
            if pred_time and pred_time > cutoff_time:
                recent_predictions.append(pred)
        
        if not recent_predictions:
            print("   📊 No recent predictions (last 24h)")
            print()
            continue
            
        # Analyze signal distribution
        signals = [p.get('signal', 0) for p in recent_predictions]
        confidences = [p.get('confidence', 0) for p in recent_predictions]
        
        signal_counts = {}
        for signal in signals:
            signal_counts[signal] = signal_counts.get(signal, 0) + 1
            
        print(f"   📊 Total Predictions: {len(recent_predictions)}")
        print(f"   📈 Signal Distribution:")
        for signal, count in sorted(signal_counts.items()):
            signal_name = {-2: "STRONG_SELL", -1: "WEAK_SELL", 0: "HOLD", 1: "WEAK_BUY", 2: "STRONG_BUY"}.get(signal, f"SIGNAL_{signal}")
            percentage = (count / len(recent_predictions)) * 100
            print(f"      • {signal_name}: {count} ({percentage:.1f}%)")
            
        print(f"   🎯 Avg Confidence: {sum(confidences)/len(confidences):.3f}")
        
        # Show recent decisions (last 10)
        print(f"   🕐 Recent Decisions (last 10):")
        for pred in recent_predictions[-10:]:
            timestamp = pred.get('timestamp', 'Unknown')
            if hasattr(timestamp, 'strftime'):
                time_str = timestamp.strftime('%H:%M:%S')
            else:
                time_str = str(timestamp)[-8:] if len(str(timestamp)) > 8 else str(timestamp)
                
            signal = pred.get('signal', 0)
            confidence = pred.get('confidence', 0)
            signal_name = {-2: "STRONG_SELL", -1: "WEAK_SELL", 0: "HOLD", 1: "WEAK_BUY", 2: "STRONG_BUY"}.get(signal, f"S{signal}")
            print(f"      {time_str}: {signal_name} ({confidence:.3f})")
            
        print()

def analyze_timeframe_logic():
    """Analyze the timeframe logic and model purposes."""
    print("🕐 TIMEFRAME LOGIC ANALYSIS")
    print("=" * 60)
    
    try:
        from ai_model_manager import AIModelManager
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        
        # Initialize components to get model configs
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        
        print("📋 MODEL CONFIGURATIONS:")
        print()
        
        for model_name, config in ai_manager.model_configs.items():
            timeframes = config.get('timeframes', [])
            purpose = config.get('purpose', 'Unknown')
            features = config.get('features', [])
            
            print(f"🧠 {model_name}")
            print(f"   🎯 Purpose: {purpose}")
            print(f"   ⏰ Timeframes: {timeframes} minutes")
            print(f"   🔧 Features: {', '.join(features)}")
            
            # Categorize by timeframe
            if any(tf <= 15 for tf in timeframes):
                category = "SHORT-TERM"
            elif any(tf <= 60 for tf in timeframes):
                category = "MEDIUM-TERM"  
            else:
                category = "LONG-TERM"
                
            print(f"   📊 Category: {category}")
            print()
            
    except Exception as e:
        print(f"❌ Error analyzing timeframe logic: {e}")

def check_ensemble_voting():
    """Check how ensemble voting works across timeframes."""
    print("🗳️ ENSEMBLE VOTING ANALYSIS")
    print("=" * 60)
    
    print("📝 CURRENT ENSEMBLE LOGIC:")
    print("• All 9 models vote on EVERY decision")
    print("• Short-term models (1M, 5M, 15M) focus on scalping patterns")
    print("• Medium-term models (15M, 30M, 1H) focus on momentum/volatility")
    print("• Long-term models (1H, 4H, 1D) focus on trends/levels")
    print()
    
    print("🤔 POTENTIAL ISSUES:")
    print("• Long-term models voting on short-term trades")
    print("• All models using same 3-minute cycle")
    print("• No timeframe-specific signal generation")
    print()
    
    print("💡 SUGGESTED IMPROVEMENTS:")
    print("• Separate signal generation by timeframe")
    print("• Weight votes by timeframe relevance")
    print("• Different update cycles for different timeframes")
    print()

def main():
    """Main analysis function."""
    print("🔍 AI MODEL DECISION ANALYSIS")
    print("=" * 80)
    print()
    
    # Analyze historical decisions
    analyze_model_decisions()
    
    # Analyze timeframe logic
    analyze_timeframe_logic()
    
    # Check ensemble voting
    check_ensemble_voting()
    
    print("✅ Analysis complete!")
    print()
    print("💡 RECOMMENDATIONS:")
    print("1. Enable detailed logging to see real-time decisions")
    print("2. Consider timeframe-specific signal generation")
    print("3. Review ensemble voting weights by timeframe")
    print("4. Monitor model decision diversity")

if __name__ == "__main__":
    main()
