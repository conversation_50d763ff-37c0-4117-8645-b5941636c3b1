#!/usr/bin/env python3
"""
Analyze volume contamination in AI models and trading data.
Identify which features are volume-based and their impact on trading decisions.
"""

import pandas as pd
import numpy as np
import sys
from datetime import datetime

def analyze_trading_data():
    """Analyze the trading data Excel file for volume-related patterns."""
    print("📊 ANALYZING TRADING DATA FOR VOLUME PATTERNS")
    print("=" * 60)
    
    try:
        # Read the Excel file
        df = pd.read_excel('files/ReportHistory-5407342.xlsx')
        
        print(f"📋 Trading data loaded: {len(df)} records")
        print(f"📅 Date range: {df.columns.tolist()}")
        
        # Display first few rows to understand structure
        print(f"\n📊 Data structure:")
        print(df.head())
        
        print(f"\n📊 Column information:")
        for i, col in enumerate(df.columns):
            print(f"   {i+1}. {col}")
        
        # Look for volume-related columns
        volume_columns = [col for col in df.columns if 'volume' in col.lower() or 'vol' in col.lower()]
        if volume_columns:
            print(f"\n🔍 Volume-related columns found:")
            for col in volume_columns:
                print(f"   • {col}")
        else:
            print(f"\n✅ No obvious volume columns in trading data")
        
        # Analyze trade outcomes
        if 'Profit' in df.columns or 'P&L' in df.columns or 'Result' in df.columns:
            profit_col = None
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['profit', 'p&l', 'result', 'pnl']):
                    profit_col = col
                    break
            
            if profit_col:
                print(f"\n💰 Trade outcome analysis ({profit_col}):")
                profitable_trades = df[df[profit_col] > 0]
                losing_trades = df[df[profit_col] < 0]
                
                print(f"   Total trades: {len(df)}")
                print(f"   Profitable: {len(profitable_trades)} ({len(profitable_trades)/len(df)*100:.1f}%)")
                print(f"   Losing: {len(losing_trades)} ({len(losing_trades)/len(df)*100:.1f}%)")
                print(f"   Total P&L: {df[profit_col].sum():.2f}")
                print(f"   Average per trade: {df[profit_col].mean():.2f}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error analyzing trading data: {e}")
        return None

def analyze_model_volume_features():
    """Analyze which models use volume features."""
    print(f"\n🧠 ANALYZING MODEL VOLUME CONTAMINATION")
    print("=" * 60)
    
    # From the codebase analysis, identify volume-contaminated features
    volume_contaminated_features = {
        "short_term_pattern_nn": {
            "features": ["price_action", "volume", "micro_patterns"],
            "volume_contamination": "HIGH - Direct volume feature",
            "specific_issues": [
                "Volume feature explicitly listed",
                "_get_volume_features() used in training",
                "Volume trends and ratios calculated"
            ]
        },
        
        "short_term_momentum_rf": {
            "features": ["momentum", "rsi", "macd", "volume_velocity"],
            "volume_contamination": "HIGH - Volume velocity feature",
            "specific_issues": [
                "volume_velocity feature",
                "Volume-based momentum calculations"
            ]
        },
        
        "medium_term_breakout_rf": {
            "features": ["support_resistance", "volume_breakout", "volatility"],
            "volume_contamination": "CRITICAL - Volume breakout feature",
            "specific_issues": [
                "volume_breakout feature - completely useless for synthetic",
                "_get_volume_breakout_features() calculations",
                "Volume-based breakout confirmation"
            ]
        },
        
        "medium_term_trend_lstm": {
            "features": ["trend_indicators", "moving_averages", "volume_trend"],
            "volume_contamination": "HIGH - Volume trend feature",
            "specific_issues": [
                "volume_trend feature",
                "_get_volume_trend_features() used"
            ]
        }
    }
    
    print("🔍 VOLUME CONTAMINATION ANALYSIS:")
    print("-" * 50)
    
    total_contaminated = 0
    critical_contaminated = 0
    
    for model_name, info in volume_contaminated_features.items():
        contamination_level = info["volume_contamination"]
        
        if "HIGH" in contamination_level or "CRITICAL" in contamination_level:
            total_contaminated += 1
            if "CRITICAL" in contamination_level:
                critical_contaminated += 1
        
        print(f"\n🧠 {model_name}:")
        print(f"   Features: {info['features']}")
        print(f"   Contamination: {contamination_level}")
        print(f"   Issues:")
        for issue in info["specific_issues"]:
            print(f"     • {issue}")
    
    print(f"\n📊 CONTAMINATION SUMMARY:")
    print(f"   Total models analyzed: {len(volume_contaminated_features)}")
    print(f"   Volume contaminated: {total_contaminated}")
    print(f"   Critical contamination: {critical_contaminated}")
    print(f"   Clean models: {len(volume_contaminated_features) - total_contaminated}")
    
    return volume_contaminated_features

def identify_volume_feature_functions():
    """Identify specific volume feature functions that need removal."""
    print(f"\n🔧 VOLUME FEATURE FUNCTIONS TO REMOVE/MODIFY")
    print("=" * 60)
    
    volume_functions = {
        "trading_signal_generator.py": [
            "_get_volume_features()",
            "_get_volume_trend_features()",
            "_get_volume_breakout_features()"
        ],
        "ai_model_manager.py": [
            "Volume feature extraction in _prepare_training_data()",
            "Volume-based feature vectors",
            "Volume trend calculations"
        ],
        "dashboard_server.py": [
            "_extract_volume_features()",
            "Volume-price relationship calculations"
        ]
    }
    
    print("📋 Functions requiring modification:")
    for file_name, functions in volume_functions.items():
        print(f"\n📁 {file_name}:")
        for func in functions:
            print(f"   ❌ {func}")
    
    return volume_functions

def analyze_synthetic_algorithm_patterns():
    """Analyze what we know about synthetic algorithm behavior."""
    print(f"\n🤖 SYNTHETIC ALGORITHM PATTERN ANALYSIS")
    print("=" * 60)
    
    algorithm_insights = {
        "confirmed_facts": [
            "Prices generated by RNG + volatility calibration",
            "Volume is completely fake/decorative",
            "No order book - trades don't affect price",
            "Algorithm mimics real market patterns",
            "Technical levels may be 'respected' by design"
        ],
        
        "pattern_opportunities": [
            "Support/Resistance levels that algorithm 'respects'",
            "Fibonacci levels triggering algorithm responses",
            "Moving averages acting as algorithm magnets",
            "Post-volatility trending behavior",
            "Time-based algorithm cycles",
            "Volatility clustering patterns"
        ],
        
        "useless_indicators": [
            "Volume Profile",
            "VWAP (Volume Weighted Average Price)",
            "Volume divergence",
            "Volume breakout confirmation",
            "Volume-based momentum",
            "Volume trend analysis"
        ],
        
        "valuable_indicators": [
            "RSI, MACD, Bollinger Bands (price-only)",
            "Moving averages",
            "Support/Resistance levels",
            "Fibonacci retracements",
            "Candlestick patterns",
            "Price momentum (non-volume)",
            "Volatility measures (price-based)"
        ]
    }
    
    print("✅ CONFIRMED ALGORITHM FACTS:")
    for fact in algorithm_insights["confirmed_facts"]:
        print(f"   • {fact}")
    
    print(f"\n🎯 PATTERN OPPORTUNITIES:")
    for opportunity in algorithm_insights["pattern_opportunities"]:
        print(f"   • {opportunity}")
    
    print(f"\n❌ USELESS INDICATORS (REMOVE):")
    for indicator in algorithm_insights["useless_indicators"]:
        print(f"   • {indicator}")
    
    print(f"\n✅ VALUABLE INDICATORS (ENHANCE):")
    for indicator in algorithm_insights["valuable_indicators"]:
        print(f"   • {indicator}")
    
    return algorithm_insights

def generate_optimization_recommendations():
    """Generate specific recommendations for model optimization."""
    print(f"\n🚀 OPTIMIZATION RECOMMENDATIONS")
    print("=" * 60)
    
    recommendations = {
        "immediate_actions": [
            "Remove all volume-based features from model training",
            "Retrain models with pure price-action features",
            "Enhance support/resistance detection",
            "Add more candlestick pattern recognition",
            "Strengthen Fibonacci level analysis"
        ],
        
        "feature_engineering": [
            "Price momentum without volume weighting",
            "Volatility clustering detection",
            "Algorithm 'mood' pattern recognition",
            "Technical level 'respect' scoring",
            "Time-based pattern cycles",
            "Post-spike behavior patterns"
        ],
        
        "model_architecture": [
            "Focus on pure price action patterns",
            "Add algorithm behavior learning layers",
            "Enhance technical level detection",
            "Implement pattern sequence recognition",
            "Add volatility regime classification"
        ],
        
        "testing_strategy": [
            "A/B test volume vs non-volume models",
            "Measure pure price action signal accuracy",
            "Test algorithm pattern recognition",
            "Validate technical level effectiveness",
            "Monitor post-volatility predictions"
        ]
    }
    
    print("🎯 IMMEDIATE ACTIONS:")
    for action in recommendations["immediate_actions"]:
        print(f"   1. {action}")
    
    print(f"\n🔧 FEATURE ENGINEERING:")
    for feature in recommendations["feature_engineering"]:
        print(f"   • {feature}")
    
    print(f"\n🧠 MODEL ARCHITECTURE:")
    for arch in recommendations["model_architecture"]:
        print(f"   • {arch}")
    
    print(f"\n🧪 TESTING STRATEGY:")
    for test in recommendations["testing_strategy"]:
        print(f"   • {test}")
    
    return recommendations

def main():
    """Main analysis function."""
    print("🔍 COMPREHENSIVE VOLUME CONTAMINATION ANALYSIS")
    print("=" * 80)
    print(f"Analysis time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Analyze trading data
    trading_data = analyze_trading_data()
    
    # Analyze model contamination
    model_contamination = analyze_model_volume_features()
    
    # Identify volume functions
    volume_functions = identify_volume_feature_functions()
    
    # Analyze algorithm patterns
    algorithm_patterns = analyze_synthetic_algorithm_patterns()
    
    # Generate recommendations
    recommendations = generate_optimization_recommendations()
    
    print(f"\n🎯 ANALYSIS COMPLETE")
    print("=" * 50)
    print("✅ Trading data analyzed")
    print("✅ Model contamination identified")
    print("✅ Volume functions catalogued")
    print("✅ Algorithm patterns analyzed")
    print("✅ Optimization recommendations generated")
    
    print(f"\n💡 KEY FINDINGS:")
    print("• Multiple models heavily contaminated with volume features")
    print("• Volume breakout detection is completely useless")
    print("• Pure price action focus will likely improve performance")
    print("• Algorithm pattern recognition is the key opportunity")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
