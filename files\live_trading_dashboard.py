#!/usr/bin/env python3
"""
Live Trading Dashboard Integration
Connects the dashboard to the live trading system for real-time trading monitoring.
"""

import sys
import time
import logging
from datetime import datetime
from threading import Thread, Event
import json

# Import the existing trading engine
from live_trading_engine import LiveTradingEngine
from dashboard_server import DashboardDataManager

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LiveTradingDashboardIntegration:
    """Integrates live trading with the dashboard for real-time monitoring."""
    
    def __init__(self):
        """Initialize the live trading dashboard integration."""
        self.trading_engine = None
        self.dashboard_manager = None
        self.integration_thread = None
        self.stop_event = Event()
        self.running = False
        
        # Trading metrics
        self.live_metrics = {
            "trades_executed": 0,
            "signals_generated": 0,
            "last_signal": None,
            "last_trade": None,
            "active_positions": [],
            "performance_data": {},
            "risk_status": "SAFE"
        }
        
    def initialize(self, risk_per_trade=0.5, max_daily_loss=2.0, position_size=1.5):
        """Initialize the live trading engine and dashboard."""
        try:
            logger.info("🚀 Initializing Live Trading Dashboard Integration...")
            
            # Initialize live trading engine
            self.trading_engine = LiveTradingEngine()
            
            # Configure trading parameters
            self.trading_engine.configure_trading(
                risk_per_trade=risk_per_trade,
                max_daily_loss=max_daily_loss,
                position_size=position_size
            )
            
            # Initialize dashboard data manager
            self.dashboard_manager = DashboardDataManager()
            if not self.dashboard_manager.initialize_trading_system():
                logger.error("Failed to initialize dashboard trading system")
                return False
            
            logger.info("✅ Live Trading Dashboard Integration initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing integration: {e}")
            return False
    
    def start_live_trading_with_dashboard(self):
        """Start live trading with real-time dashboard monitoring."""
        try:
            if not self.trading_engine or not self.dashboard_manager:
                logger.error("Integration not properly initialized")
                return False
            
            logger.info("🎯 Starting Live Trading with Dashboard Monitoring...")
            
            # Start dashboard data updates
            self.dashboard_manager.start_data_updates()
            
            # Start integration monitoring thread
            self.running = True
            self.integration_thread = Thread(target=self._integration_loop, daemon=True)
            self.integration_thread.start()
            
            # Start live trading engine
            self.trading_engine.start_live_trading()
            
            logger.info("✅ Live Trading with Dashboard started successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error starting live trading with dashboard: {e}")
            return False
    
    def stop_live_trading_with_dashboard(self):
        """Stop live trading and dashboard monitoring."""
        try:
            logger.info("⏹️ Stopping Live Trading with Dashboard...")
            
            # Stop integration thread
            self.running = False
            self.stop_event.set()
            
            if self.integration_thread:
                self.integration_thread.join(timeout=5)
            
            # Stop trading engine
            if self.trading_engine:
                self.trading_engine.stop_live_trading()
            
            # Stop dashboard updates
            if self.dashboard_manager:
                self.dashboard_manager.stop_data_updates()
            
            logger.info("✅ Live Trading with Dashboard stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error stopping live trading with dashboard: {e}")
            return False
    
    def _integration_loop(self):
        """Main integration loop to sync trading data with dashboard."""
        logger.info("🔄 Starting integration monitoring loop...")
        
        while self.running and not self.stop_event.is_set():
            try:
                # Update live trading metrics
                self._update_live_metrics()
                
                # Sync trading data with dashboard
                self._sync_trading_data_with_dashboard()
                
                # Monitor trading performance
                self._monitor_trading_performance()
                
                # Wait before next update
                self.stop_event.wait(30)  # Update every 30 seconds (aligned with 3-min cycles)
                
            except Exception as e:
                logger.error(f"Error in integration loop: {e}")
                self.stop_event.wait(30)  # Wait longer on error
    
    def _update_live_metrics(self):
        """Update live trading metrics."""
        try:
            if not self.trading_engine:
                return
            
            # Get latest trading data
            engine_data = self.trading_engine.get_current_status()
            
            if engine_data:
                self.live_metrics.update({
                    "trades_executed": engine_data.get("total_trades", 0),
                    "signals_generated": engine_data.get("total_signals", 0),
                    "last_signal": engine_data.get("last_signal"),
                    "last_trade": engine_data.get("last_trade"),
                    "active_positions": engine_data.get("active_positions", []),
                    "current_pnl": engine_data.get("current_pnl", 0.0),
                    "daily_pnl": engine_data.get("daily_pnl", 0.0),
                    "win_rate": engine_data.get("win_rate", 0.0),
                    "risk_status": engine_data.get("risk_status", "SAFE")
                })
                
        except Exception as e:
            logger.warning(f"Error updating live metrics: {e}")
    
    def _sync_trading_data_with_dashboard(self):
        """Sync live trading data with dashboard."""
        try:
            if not self.dashboard_manager:
                return
            
            # Get current dashboard data
            dashboard_data = self.dashboard_manager.get_dashboard_data()
            
            # Update dashboard with live trading data
            dashboard_data["live_trading"] = {
                "status": "ACTIVE" if self.running else "STOPPED",
                "trades_executed": self.live_metrics["trades_executed"],
                "signals_generated": self.live_metrics["signals_generated"],
                "last_signal": self.live_metrics["last_signal"],
                "last_trade": self.live_metrics["last_trade"],
                "active_positions": len(self.live_metrics["active_positions"]),
                "current_pnl": self.live_metrics.get("current_pnl", 0.0),
                "daily_pnl": self.live_metrics.get("daily_pnl", 0.0),
                "win_rate": self.live_metrics.get("win_rate", 0.0),
                "risk_status": self.live_metrics["risk_status"],
                "last_update": datetime.now().isoformat()
            }
            
            # Update trade metrics with real data
            if "trade_metrics" in dashboard_data:
                dashboard_data["trade_metrics"].update({
                    "total_trades": self.live_metrics["trades_executed"],
                    "total_pnl": self.live_metrics.get("current_pnl", 0.0),
                    "daily_pnl": self.live_metrics.get("daily_pnl", 0.0),
                    "win_rate": self.live_metrics.get("win_rate", 0.0),
                    "active_positions": len(self.live_metrics["active_positions"])
                })
            
            # Update the dashboard manager's data
            with self.dashboard_manager.data_lock:
                self.dashboard_manager.dashboard_data.update(dashboard_data)
                
        except Exception as e:
            logger.warning(f"Error syncing trading data with dashboard: {e}")
    
    def _monitor_trading_performance(self):
        """Monitor trading performance and update dashboard."""
        try:
            if not self.trading_engine:
                return
            
            # Get performance data
            performance = self.trading_engine.get_performance_summary()
            
            if performance:
                self.live_metrics["performance_data"] = {
                    "total_return": performance.get("total_return", 0.0),
                    "sharpe_ratio": performance.get("sharpe_ratio", 0.0),
                    "max_drawdown": performance.get("max_drawdown", 0.0),
                    "win_rate": performance.get("win_rate", 0.0),
                    "profit_factor": performance.get("profit_factor", 0.0),
                    "avg_trade_duration": performance.get("avg_trade_duration", 0),
                    "last_updated": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.warning(f"Error monitoring trading performance: {e}")
    
    def get_integration_status(self):
        """Get current integration status."""
        return {
            "integration_running": self.running,
            "trading_engine_status": "ACTIVE" if self.trading_engine and self.trading_engine.running else "STOPPED",
            "dashboard_status": "ACTIVE" if self.dashboard_manager else "STOPPED",
            "live_metrics": self.live_metrics,
            "last_update": datetime.now().isoformat()
        }

def main():
    """Main function to run live trading with dashboard integration."""
    integration = LiveTradingDashboardIntegration()
    
    try:
        print("🚀 AI TRADING SYSTEM - LIVE DASHBOARD INTEGRATION")
        print("=" * 60)
        print("🎯 Real-time trading with visual monitoring")
        print("📊 Dashboard: http://localhost:5000")
        print("🔄 Live trading execution and analysis")
        print("=" * 60)
        
        # Get trading parameters
        print("\n📋 Configure Trading Parameters:")
        risk_per_trade = float(input("Risk per trade (% of account, default 0.5): ") or "0.5")
        max_daily_loss = float(input("Max daily loss (% of account, default 2.0): ") or "2.0")
        position_size = float(input("Position size (lots, default 1.5): ") or "1.5")
        
        # Initialize integration
        if not integration.initialize(risk_per_trade, max_daily_loss, position_size):
            print("❌ Failed to initialize integration")
            return False
        
        # Start live trading with dashboard
        if not integration.start_live_trading_with_dashboard():
            print("❌ Failed to start live trading with dashboard")
            return False
        
        print("\n✅ Live Trading with Dashboard started successfully!")
        print("🌐 Dashboard available at: http://localhost:5000")
        print("📊 Monitor real-time AI trading decisions and execution")
        print("⚠️ Press Ctrl+C to stop")
        
        # Keep running until interrupted
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ Stopping live trading with dashboard...")
        integration.stop_live_trading_with_dashboard()
        print("✅ Live trading with dashboard stopped successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error in live trading with dashboard: {e}")
        integration.stop_live_trading_with_dashboard()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
