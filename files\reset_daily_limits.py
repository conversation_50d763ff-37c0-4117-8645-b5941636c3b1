"""
Reset Daily Trading Limits Script
This script resets the daily trading counters and P&L tracking.
"""

import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/reset_daily_limits.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("ResetDailyLimits")

def reset_daily_limits():
    """Reset daily trading limits and P&L tracking."""
    print("🔄 RESETTING DAILY TRADING LIMITS")
    print("=" * 50)
    
    try:
        # Import components
        from order_execution_system import OrderExecutionSystem
        from synthetic_data_collector import SyntheticDataCollector
        
        print("🔧 Initializing components...")
        data_collector = SyntheticDataCollector()
        order_executor = OrderExecutionSystem(data_collector)
        
        if not order_executor.mt5_connected:
            print("❌ MT5 not connected!")
            return False
            
        print("✅ MT5 connected")
        
        # Reset daily counters
        print("🔄 Resetting daily counters...")
        order_executor.daily_trade_count = 0
        order_executor.daily_pnl = 0.0
        order_executor.last_reset_date = datetime.now().date()

        # Set fresh start time - only consider trades from this moment forward
        fresh_start_time = datetime.now()
        order_executor.set_fresh_start_time(fresh_start_time)
        print(f"✅ Fresh start time set: {fresh_start_time}")
        print("   Daily P&L will now ignore all previous trades")

        # Reset timeframe-specific daily trade counters
        if hasattr(order_executor, 'timeframe_daily_trades'):
            order_executor.timeframe_daily_trades = {
                'short_term': 0,
                'medium_term': 0,
                'long_term': 0
            }
            print("✅ Timeframe daily trade counters reset")

        # Clear timeframe position tracking
        if hasattr(order_executor, 'timeframe_positions'):
            order_executor.timeframe_positions = {
                'short_term': None,
                'medium_term': None,
                'long_term': None
            }
            print("✅ Timeframe position tracking cleared")

        # Clear any emergency stop flags
        if hasattr(order_executor, 'emergency_stop_triggered'):
            order_executor.emergency_stop_triggered = False
            
        print("✅ Daily counters reset:")
        print(f"   Daily trade count: {order_executor.daily_trade_count}")
        print(f"   Daily P&L: {order_executor.daily_pnl}")
        print(f"   Reset date: {order_executor.last_reset_date}")

        # Show timeframe trade counts (NO LIMITS - TRACKING ONLY)
        if hasattr(order_executor, 'timeframe_daily_trades'):
            print("✅ Timeframe daily trade counts (unlimited):")
            for timeframe, count in order_executor.timeframe_daily_trades.items():
                print(f"   {timeframe}: {count} trades")

        if hasattr(order_executor, 'timeframe_monthly_trades'):
            print("✅ Timeframe monthly trade counts:")
            for timeframe, count in order_executor.timeframe_monthly_trades.items():
                print(f"   {timeframe}: {count} trades this month")
        
        # Check current account status
        print("\n💰 Current account status:")
        try:
            import MetaTrader5 as mt5
            account_info = mt5.account_info()
            
            if account_info:
                print(f"   Balance: ${account_info.balance:.2f}")
                print(f"   Equity: ${account_info.equity:.2f}")
                print(f"   Margin: ${account_info.margin:.2f}")
                print(f"   Free margin: ${account_info.margin_free:.2f}")
            else:
                print("   Could not get account info")
                
        except Exception as e:
            print(f"   Error getting account info: {e}")
            
        # Check for open positions
        print("\n📊 Checking open positions...")
        try:
            positions = mt5.positions_get()
            if positions:
                print(f"   Open positions: {len(positions)}")
                for pos in positions:
                    print(f"   Position {pos.ticket}: {pos.type_str} {pos.volume} lots, P&L: ${pos.profit:.2f}")
            else:
                print("   No open positions")
                
        except Exception as e:
            print(f"   Error checking positions: {e}")
            
        print("\n✅ Daily limits reset successfully!")
        print("🚀 You can now restart the trading system")
        
        return True
        
    except Exception as e:
        print(f"❌ Error resetting daily limits: {e}")
        logger.error(f"Reset failed: {e}")
        return False

def main():
    """Main function."""
    print("🔄 AI TRADING SYSTEM - DAILY LIMITS RESET")
    print("=" * 60)
    print("This script will reset daily trading counters and P&L tracking")
    print("Use this when:")
    print("  - Starting a new trading day")
    print("  - After emergency stops")
    print("  - When daily limits are incorrectly triggered")
    print("=" * 60)
    
    # Confirm with user
    response = input("\nDo you want to reset daily limits? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Reset cancelled by user")
        return False
        
    # Perform reset
    success = reset_daily_limits()
    
    if success:
        print("\n🎉 RESET COMPLETED SUCCESSFULLY!")
        print("=" * 40)
        print("✅ Daily trading limits have been reset")
        print("✅ P&L tracking has been reset")
        print("✅ Emergency stop flags cleared")
        print("\n🚀 You can now restart the AI trading system:")
        print("   start_complete_ai_trading_system.bat")
        print("=" * 40)
    else:
        print("\n❌ RESET FAILED!")
        print("Check the logs for detailed error information")
        
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
