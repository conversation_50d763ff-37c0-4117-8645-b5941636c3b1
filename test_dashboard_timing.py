#!/usr/bin/env python3
"""
Test dashboard validation timing to identify potential race conditions.
"""

import sys
import time
import logging
import requests
import threading
from datetime import datetime

# Add current directory to path
sys.path.append('.')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_dashboard_connection_with_timing():
    """Validate dashboard connectivity with detailed timing information."""
    start_time = datetime.now()
    
    try:
        logger.info("Starting dashboard validation with timing...")

        # Try to connect to dashboard
        for attempt in range(1, 6):  # 1-5 attempts
            attempt_start = datetime.now()
            try:
                logger.info(f"Dashboard connection attempt {attempt}/5...")
                response = requests.get('http://localhost:5000', timeout=10)
                attempt_duration = (datetime.now() - attempt_start).total_seconds()
                
                if response.status_code == 200:
                    total_duration = (datetime.now() - start_time).total_seconds()
                    logger.info(f"SUCCESS: Dashboard connection validated (status: {response.status_code})")
                    logger.info(f"Attempt duration: {attempt_duration:.2f}s")
                    logger.info(f"Total validation time: {total_duration:.2f}s")
                    logger.info(f"Dashboard response length: {len(response.text)} characters")
                    return True, total_duration
                else:
                    logger.warning(f"Dashboard responded with status: {response.status_code} (took {attempt_duration:.2f}s)")
            except requests.exceptions.ConnectionError as e:
                attempt_duration = (datetime.now() - attempt_start).total_seconds()
                logger.warning(f"Connection error on attempt {attempt} (took {attempt_duration:.2f}s): {e}")
            except requests.exceptions.Timeout as e:
                attempt_duration = (datetime.now() - attempt_start).total_seconds()
                logger.warning(f"Timeout error on attempt {attempt} (took {attempt_duration:.2f}s): {e}")
            except Exception as e:
                attempt_duration = (datetime.now() - attempt_start).total_seconds()
                logger.warning(f"Unexpected error on attempt {attempt} (took {attempt_duration:.2f}s): {e}")
            
            if attempt < 5:
                logger.info("Waiting 3 seconds before next attempt...")
                time.sleep(3)

        total_duration = (datetime.now() - start_time).total_seconds()
        logger.error(f"ERROR: Dashboard connection failed after 5 attempts (total time: {total_duration:.2f}s)")
        return False, total_duration

    except Exception as e:
        total_duration = (datetime.now() - start_time).total_seconds()
        logger.error(f"Dashboard connection validation failed (total time: {total_duration:.2f}s): {e}")
        return False, total_duration

def test_concurrent_validations():
    """Test multiple concurrent dashboard validations."""
    logger.info("Testing concurrent dashboard validations...")
    
    results = []
    threads = []
    
    def validation_worker(worker_id):
        logger.info(f"Worker {worker_id} starting validation...")
        success, duration = validate_dashboard_connection_with_timing()
        results.append((worker_id, success, duration))
        logger.info(f"Worker {worker_id} completed: {'SUCCESS' if success else 'FAILED'} in {duration:.2f}s")
    
    # Start 3 concurrent validation threads
    for i in range(3):
        thread = threading.Thread(target=validation_worker, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Analyze results
    successful = sum(1 for _, success, _ in results if success)
    avg_duration = sum(duration for _, _, duration in results) / len(results)
    
    logger.info(f"Concurrent validation results: {successful}/{len(results)} successful")
    logger.info(f"Average validation time: {avg_duration:.2f}s")
    
    return successful == len(results)

def test_rapid_validations():
    """Test rapid successive dashboard validations."""
    logger.info("Testing rapid successive dashboard validations...")
    
    results = []
    
    for i in range(5):
        logger.info(f"Rapid validation {i+1}/5...")
        success, duration = validate_dashboard_connection_with_timing()
        results.append((success, duration))
        
        if not success:
            logger.error(f"Rapid validation {i+1} failed!")
            break
        
        # Small delay between validations
        time.sleep(1)
    
    successful = sum(1 for success, _ in results if success)
    avg_duration = sum(duration for _, duration in results) / len(results) if results else 0
    
    logger.info(f"Rapid validation results: {successful}/{len(results)} successful")
    logger.info(f"Average validation time: {avg_duration:.2f}s")
    
    return successful == len(results)

def test_system_orchestrator_timing():
    """Test the actual SystemOrchestrator validation timing."""
    try:
        from system_orchestrator import AITradingSystemOrchestrator
        
        logger.info("Testing SystemOrchestrator validation timing...")
        
        orchestrator = AITradingSystemOrchestrator()
        
        start_time = datetime.now()
        result = orchestrator.validate_dashboard_connection()
        duration = (datetime.now() - start_time).total_seconds()
        
        logger.info(f"SystemOrchestrator validation: {'SUCCESS' if result else 'FAILED'} in {duration:.2f}s")
        
        return result, duration
        
    except Exception as e:
        logger.error(f"Error testing SystemOrchestrator validation: {e}")
        return False, 0

def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("DASHBOARD VALIDATION TIMING TEST")
    logger.info("=" * 60)
    
    # Test 1: Single validation with timing
    logger.info("\n1. Testing single validation with timing...")
    single_success, single_duration = validate_dashboard_connection_with_timing()
    
    # Test 2: Concurrent validations
    logger.info("\n2. Testing concurrent validations...")
    concurrent_success = test_concurrent_validations()
    
    # Test 3: Rapid successive validations
    logger.info("\n3. Testing rapid successive validations...")
    rapid_success = test_rapid_validations()
    
    # Test 4: SystemOrchestrator validation
    logger.info("\n4. Testing SystemOrchestrator validation...")
    orchestrator_success, orchestrator_duration = test_system_orchestrator_timing()
    
    # Results
    logger.info("\n" + "=" * 40)
    logger.info("TIMING TEST RESULTS")
    logger.info("=" * 40)
    
    logger.info(f"Single validation: {'✅ PASSED' if single_success else '❌ FAILED'} ({single_duration:.2f}s)")
    logger.info(f"Concurrent validations: {'✅ PASSED' if concurrent_success else '❌ FAILED'}")
    logger.info(f"Rapid validations: {'✅ PASSED' if rapid_success else '❌ FAILED'}")
    logger.info(f"SystemOrchestrator: {'✅ PASSED' if orchestrator_success else '❌ FAILED'} ({orchestrator_duration:.2f}s)")
    
    all_passed = all([single_success, concurrent_success, rapid_success, orchestrator_success])
    
    if all_passed:
        logger.info("\n🎉 All dashboard validation timing tests PASSED!")
        logger.info("Dashboard validation should work reliably during system startup.")
        return True
    else:
        logger.error("\n💥 Some dashboard validation timing tests FAILED!")
        logger.error("There may be timing or concurrency issues.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
