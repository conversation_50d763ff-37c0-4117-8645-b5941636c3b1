#!/usr/bin/env python3
"""
Diagnose why MEDIUM TERM trades are being blocked when SHORT TERM trades are active.
Check timeframe position tracking and MT5 position states.
"""

import sys
import MetaTrader5 as mt5
from datetime import datetime

def connect_to_mt5():
    """Connect to MT5."""
    print("🔌 Connecting to MT5...")
    
    if not mt5.initialize():
        print("❌ MT5 initialization failed")
        return False
    
    account_info = mt5.account_info()
    if account_info is None:
        print("❌ Could not get account information")
        return False
    
    print(f"✅ Connected to MT5 Account: {account_info.login}")
    return True

def get_current_positions():
    """Get current positions from MT5."""
    print("\n📊 CURRENT MT5 POSITIONS:")
    print("=" * 60)
    
    positions = mt5.positions_get(symbol="DEX 900 DOWN Index")
    
    if not positions:
        print("❌ No positions found")
        return []
    
    ai_positions = []
    manual_positions = []
    
    for pos in positions:
        comment = getattr(pos, 'comment', '')
        
        # Check if it's an AI bot position
        if any(pattern in comment for pattern in ['AI_BOT_SHORT_', 'AI_BOT_MEDIUM_', 'AI_BOT_LONG_']):
            ai_positions.append(pos)
            print(f"🤖 AI BOT Position:")
        else:
            manual_positions.append(pos)
            print(f"👤 MANUAL Position:")
        
        print(f"   ID: {pos.ticket}")
        print(f"   Type: {'BUY' if pos.type == 0 else 'SELL'}")
        print(f"   Volume: {pos.volume}")
        print(f"   Open Price: {pos.price_open}")
        print(f"   Current Price: {pos.price_current}")
        print(f"   Profit: ${pos.profit:.2f}")
        print(f"   Comment: '{comment}'")
        
        # Determine timeframe
        timeframe = "UNKNOWN"
        if 'AI_BOT_SHORT_' in comment:
            timeframe = "SHORT_TERM"
        elif 'AI_BOT_MEDIUM_' in comment:
            timeframe = "MEDIUM_TERM"
        elif 'AI_BOT_LONG_' in comment:
            timeframe = "LONG_TERM"
        
        print(f"   Timeframe: {timeframe}")
        print()
    
    print(f"📋 SUMMARY:")
    print(f"   🤖 AI Bot Positions: {len(ai_positions)}")
    print(f"   👤 Manual Positions: {len(manual_positions)}")
    print(f"   📊 Total Positions: {len(positions)}")
    
    return ai_positions

def check_order_execution_system():
    """Check the current state of the order execution system."""
    print("\n🔧 CHECKING ORDER EXECUTION SYSTEM STATE:")
    print("=" * 60)
    
    try:
        # Import the order execution system
        from synthetic_data_collector import SyntheticDataCollector
        from order_execution_system import OrderExecutionSystem
        
        # Initialize
        data_collector = SyntheticDataCollector()
        order_executor = OrderExecutionSystem(data_collector)
        
        print(f"✅ Order Execution System initialized")
        print(f"✅ MT5 Connected: {order_executor.mt5_connected}")
        
        # Check timeframe positions tracking
        print(f"\n📊 TIMEFRAME POSITIONS TRACKING:")
        for timeframe, position_id in order_executor.timeframe_positions.items():
            status = f"OCCUPIED by {position_id}" if position_id else "AVAILABLE"
            print(f"   {timeframe.upper()}: {status}")
        
        # Check active positions
        print(f"\n📋 ACTIVE POSITIONS IN SYSTEM:")
        print(f"   Count: {len(order_executor.active_positions)}")
        for pos_id, position in order_executor.active_positions.items():
            print(f"   Position {pos_id}: {position.symbol} - {position.volume}")
        
        # Check concurrent position limit
        print(f"\n🛡️ LIMITS:")
        print(f"   Max Concurrent Positions: {order_executor.max_concurrent_positions}")
        print(f"   Current Active Positions: {len(order_executor.active_positions)}")
        print(f"   Daily P&L: ${order_executor.daily_pnl:.2f}")
        
        return order_executor
        
    except Exception as e:
        print(f"❌ Error checking order execution system: {e}")
        import traceback
        traceback.print_exc()
        return None

def simulate_medium_term_signal(order_executor):
    """Simulate a MEDIUM TERM signal and check if it would be blocked."""
    print("\n🧪 SIMULATING MEDIUM TERM SIGNAL:")
    print("=" * 60)
    
    try:
        from trading_signal import TradingSignal, SignalType
        
        # Create a test MEDIUM TERM signal
        test_signal = TradingSignal(
            signal_type=SignalType.STRONG_SELL,
            confidence=0.873,
            entry_price=55000.0,
            stop_loss=55150.0,
            take_profit=55100.0,
            position_size=0.01,
            reasoning="Strong signal (-2) from medium_term_breakout_rf (medium_term) with 0.873 confidence. Pattern: Breakout detection"
        )
        
        print(f"📡 Test Signal Created:")
        print(f"   Type: {test_signal.signal_type}")
        print(f"   Confidence: {test_signal.confidence}")
        print(f"   Reasoning: {test_signal.reasoning}")
        
        # Check timeframe detection
        timeframe = order_executor._get_signal_timeframe(test_signal)
        print(f"   Detected Timeframe: {timeframe}")
        
        # Check if execution would be allowed
        can_execute = order_executor._can_execute_order(test_signal)
        print(f"   Can Execute: {can_execute}")
        
        if not can_execute:
            print(f"\n❌ SIGNAL WOULD BE BLOCKED!")
            print(f"   Reason: Check the logs above for blocking reason")
            
            # Check specific blocking reasons
            if timeframe and order_executor.timeframe_positions.get(timeframe) is not None:
                blocking_position = order_executor.timeframe_positions[timeframe]
                print(f"   🚫 BLOCKING REASON: {timeframe} already has position {blocking_position}")
            
            if len(order_executor.active_positions) >= order_executor.max_concurrent_positions:
                print(f"   🚫 BLOCKING REASON: Max concurrent positions reached ({len(order_executor.active_positions)}/{order_executor.max_concurrent_positions})")
        else:
            print(f"\n✅ SIGNAL WOULD BE ALLOWED!")
        
        return can_execute
        
    except Exception as e:
        print(f"❌ Error simulating signal: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main diagnostic function."""
    print("🔍 TIMEFRAME BLOCKING DIAGNOSTIC")
    print("=" * 70)
    print("Checking why MEDIUM TERM trades are blocked when SHORT TERM trades are active...")
    
    if not connect_to_mt5():
        return False
    
    try:
        # Step 1: Check current MT5 positions
        ai_positions = get_current_positions()
        
        # Step 2: Check order execution system state
        order_executor = check_order_execution_system()
        
        if not order_executor:
            return False
        
        # Step 3: Simulate MEDIUM TERM signal
        can_execute = simulate_medium_term_signal(order_executor)
        
        # Step 4: Analysis and recommendations
        print(f"\n📋 DIAGNOSTIC SUMMARY:")
        print("=" * 50)
        
        if ai_positions:
            short_positions = [p for p in ai_positions if 'AI_BOT_SHORT_' in getattr(p, 'comment', '')]
            medium_positions = [p for p in ai_positions if 'AI_BOT_MEDIUM_' in getattr(p, 'comment', '')]
            
            print(f"🔥 SHORT TERM positions: {len(short_positions)}")
            print(f"⚡ MEDIUM TERM positions: {len(medium_positions)}")
            
            if short_positions and not medium_positions and not can_execute:
                print(f"\n🎯 ISSUE CONFIRMED:")
                print(f"   • SHORT TERM position exists")
                print(f"   • No MEDIUM TERM position")
                print(f"   • But MEDIUM TERM signal would be blocked")
                print(f"   • This indicates a bug in timeframe independence")
            elif short_positions and medium_positions:
                print(f"\n✅ WORKING CORRECTLY:")
                print(f"   • Both SHORT and MEDIUM positions exist")
                print(f"   • Independent timeframe trading is working")
            else:
                print(f"\n💡 ANALYSIS:")
                print(f"   • Current state appears normal")
                print(f"   • May need to wait for actual signal to test")
        
        return True
        
    except Exception as e:
        print(f"❌ Diagnostic failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
