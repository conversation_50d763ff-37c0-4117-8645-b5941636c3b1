#!/usr/bin/env python3
"""
Quick test to verify trading engine starts without daily limit errors.
"""

import sys
import os

def test_engine_startup():
    """Test that trading engine starts without config errors."""
    print("🚀 TESTING TRADING ENGINE STARTUP...")
    print("=" * 50)
    
    try:
        # Import trading engine
        from trading_engine import TradingEngine
        print("✅ TradingEngine imported")
        
        # Create instance
        engine = TradingEngine()
        print("✅ TradingEngine instance created")
        
        # Try to initialize components (this will test config access)
        print("🔧 Testing component initialization...")
        
        # This should not fail with config.MAX_DAILY_TRADES error
        try:
            success = engine.initialize_components()
            if success:
                print("✅ Components initialized successfully")
                print("✅ No config.MAX_DAILY_TRADES errors!")
                
                # Clean shutdown
                if engine.data_collector:
                    engine.data_collector.cleanup()
                if engine.order_executor:
                    engine.order_executor.cleanup()
                    
                return True
            else:
                print("⚠️  Component initialization failed (but no config errors)")
                return True  # Still success if no config errors
                
        except AttributeError as e:
            if "MAX_DAILY_TRADES" in str(e):
                print(f"❌ Config error still exists: {e}")
                return False
            else:
                print(f"⚠️  Other initialization error: {e}")
                return True  # Not a config error
                
    except Exception as e:
        print(f"❌ Engine startup failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 QUICK TRADING ENGINE STARTUP TEST")
    print("=" * 40)
    
    success = test_engine_startup()
    
    if success:
        print("\n✅ SUCCESS!")
        print("🎉 Trading engine can start without daily limit errors")
        print("💡 You can now start your trading system normally")
    else:
        print("\n❌ FAILED!")
        print("⚠️  There are still config errors to fix")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
