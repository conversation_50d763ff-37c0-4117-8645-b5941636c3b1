#!/usr/bin/env python3
"""
Diagnose why 1-minute data collection is failing for DEX 900 DOWN Index.
"""

import sys
from datetime import datetime, timedelta

def test_mt5_timeframe_availability():
    """Test which timeframes are available for DEX 900 DOWN Index."""
    print("🔍 TESTING MT5 TIMEFRAME AVAILABILITY")
    print("=" * 60)
    
    try:
        import MetaTrader5 as mt5
        
        # Initialize MT5
        if not mt5.initialize():
            print("❌ Failed to initialize MT5")
            return False
        
        symbol = "DEX 900 DOWN Index"
        
        # Check if symbol exists and is selected
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"❌ Symbol {symbol} not found")
            return False
        
        # Select symbol
        if not mt5.symbol_select(symbol, True):
            print(f"❌ Failed to select symbol {symbol}")
            return False
        
        print(f"✅ Symbol {symbol} selected successfully")
        print(f"   Description: {symbol_info.description}")
        print(f"   Point: {symbol_info.point}")
        print(f"   Digits: {symbol_info.digits}")
        
        # Test different timeframes
        timeframes_to_test = {
            "1min": mt5.TIMEFRAME_M1,
            "5min": mt5.TIMEFRAME_M5,
            "15min": mt5.TIMEFRAME_M15,
            "30min": mt5.TIMEFRAME_M30,
            "1hour": mt5.TIMEFRAME_H1,
            "4hour": mt5.TIMEFRAME_H4,
            "1day": mt5.TIMEFRAME_D1
        }
        
        # Test with recent data (last 24 hours)
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=24)
        
        print(f"\n📊 TESTING TIMEFRAMES (Last 24 hours):")
        print(f"   From: {start_date}")
        print(f"   To: {end_date}")
        print("-" * 50)
        
        results = {}
        
        for name, timeframe in timeframes_to_test.items():
            try:
                rates = mt5.copy_rates_range(symbol, timeframe, start_date, end_date)
                
                if rates is None:
                    print(f"❌ {name:8}: No data (rates = None)")
                    results[name] = "No data"
                elif len(rates) == 0:
                    print(f"❌ {name:8}: Empty data (len = 0)")
                    results[name] = "Empty"
                else:
                    print(f"✅ {name:8}: {len(rates):4} records")
                    results[name] = len(rates)
                    
                    # Show sample data for successful timeframes
                    if len(rates) > 0:
                        latest = rates[-1]
                        timestamp = datetime.fromtimestamp(latest['time'])
                        print(f"           Latest: {timestamp} | Close: {latest['close']:.2f}")
                        
            except Exception as e:
                print(f"❌ {name:8}: Error - {e}")
                results[name] = f"Error: {e}"
        
        # Summary
        print(f"\n📋 SUMMARY:")
        print("-" * 30)
        working_timeframes = [name for name, result in results.items() if isinstance(result, int)]
        failing_timeframes = [name for name, result in results.items() if not isinstance(result, int)]
        
        print(f"✅ Working timeframes: {', '.join(working_timeframes)}")
        print(f"❌ Failing timeframes: {', '.join(failing_timeframes)}")
        
        return len(working_timeframes) > 0
        
    except Exception as e:
        print(f"❌ Error testing timeframes: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            mt5.shutdown()
        except:
            pass

def test_1min_data_specifically():
    """Test 1-minute data collection with different approaches."""
    print(f"\n🔬 DETAILED 1-MINUTE DATA TESTING")
    print("=" * 50)
    
    try:
        import MetaTrader5 as mt5
        
        # Initialize MT5
        if not mt5.initialize():
            print("❌ Failed to initialize MT5")
            return False
        
        symbol = "DEX 900 DOWN Index"
        
        # Select symbol
        if not mt5.symbol_select(symbol, True):
            print(f"❌ Failed to select symbol {symbol}")
            return False
        
        # Test different time ranges for 1-minute data
        test_ranges = [
            ("Last 1 hour", timedelta(hours=1)),
            ("Last 6 hours", timedelta(hours=6)),
            ("Last 24 hours", timedelta(hours=24)),
            ("Last 3 days", timedelta(days=3)),
            ("Last 7 days", timedelta(days=7))
        ]
        
        print("🕐 Testing 1-minute data with different time ranges:")
        print("-" * 50)
        
        for name, delta in test_ranges:
            end_date = datetime.now()
            start_date = end_date - delta
            
            try:
                rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M1, start_date, end_date)
                
                if rates is None:
                    print(f"❌ {name:15}: No data returned")
                elif len(rates) == 0:
                    print(f"❌ {name:15}: Empty data")
                else:
                    print(f"✅ {name:15}: {len(rates):4} records")
                    
                    # Check data quality
                    if len(rates) > 0:
                        first_time = datetime.fromtimestamp(rates[0]['time'])
                        last_time = datetime.fromtimestamp(rates[-1]['time'])
                        print(f"                   From: {first_time}")
                        print(f"                   To:   {last_time}")
                        
                        # Check for gaps
                        expected_records = int(delta.total_seconds() / 60)  # 1 record per minute
                        coverage = (len(rates) / expected_records) * 100 if expected_records > 0 else 0
                        print(f"                   Coverage: {coverage:.1f}% ({len(rates)}/{expected_records})")
                        
            except Exception as e:
                print(f"❌ {name:15}: Error - {e}")
        
        # Test with copy_rates_from instead of copy_rates_range
        print(f"\n🔄 Testing alternative MT5 functions:")
        print("-" * 40)
        
        try:
            # Test copy_rates_from (last N bars)
            rates_from = mt5.copy_rates_from(symbol, mt5.TIMEFRAME_M1, datetime.now(), 100)
            
            if rates_from is None:
                print("❌ copy_rates_from: No data")
            elif len(rates_from) == 0:
                print("❌ copy_rates_from: Empty data")
            else:
                print(f"✅ copy_rates_from: {len(rates_from)} records")
                
        except Exception as e:
            print(f"❌ copy_rates_from: Error - {e}")
        
        try:
            # Test copy_rates_from_pos (from position)
            rates_pos = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M1, 0, 100)
            
            if rates_pos is None:
                print("❌ copy_rates_from_pos: No data")
            elif len(rates_pos) == 0:
                print("❌ copy_rates_from_pos: Empty data")
            else:
                print(f"✅ copy_rates_from_pos: {len(rates_pos)} records")
                
        except Exception as e:
            print(f"❌ copy_rates_from_pos: Error - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in detailed testing: {e}")
        return False
    finally:
        try:
            mt5.shutdown()
        except:
            pass

def check_synthetic_instrument_limitations():
    """Check if synthetic instruments have specific limitations."""
    print(f"\n🤖 CHECKING SYNTHETIC INSTRUMENT LIMITATIONS")
    print("=" * 60)
    
    try:
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ Failed to initialize MT5")
            return False
        
        symbol = "DEX 900 DOWN Index"
        symbol_info = mt5.symbol_info(symbol)
        
        if symbol_info is None:
            print(f"❌ Symbol {symbol} not found")
            return False
        
        print(f"📊 SYMBOL INFORMATION:")
        print(f"   Name: {symbol_info.name}")
        print(f"   Description: {symbol_info.description}")
        print(f"   Path: {symbol_info.path}")
        print(f"   Currency Base: {symbol_info.currency_base}")
        print(f"   Currency Profit: {symbol_info.currency_profit}")
        print(f"   Currency Margin: {symbol_info.currency_margin}")
        print(f"   Digits: {symbol_info.digits}")
        print(f"   Point: {symbol_info.point}")
        print(f"   Trade Mode: {symbol_info.trade_mode}")
        print(f"   Trade Execution: {symbol_info.trade_execution}")
        print(f"   Trade Calc Mode: {symbol_info.trade_calc_mode}")
        print(f"   Visible: {symbol_info.visible}")
        print(f"   Select: {symbol_info.select}")
        
        # Check if it's a synthetic instrument
        if "synthetic" in symbol_info.description.lower() or "artificial" in symbol_info.description.lower():
            print(f"\n⚠️  SYNTHETIC INSTRUMENT DETECTED")
            print(f"   Synthetic instruments may have limited historical data")
            print(f"   1-minute data might not be available for older periods")
            print(f"   Consider using 5-minute or 15-minute data instead")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking symbol info: {e}")
        return False
    finally:
        try:
            mt5.shutdown()
        except:
            pass

def main():
    """Main diagnostic function."""
    print("🔍 1-MINUTE DATA COLLECTION DIAGNOSIS")
    print("=" * 70)
    
    # Test timeframe availability
    timeframe_test = test_mt5_timeframe_availability()
    
    # Detailed 1-minute testing
    detailed_test = test_1min_data_specifically()
    
    # Check synthetic limitations
    synthetic_check = check_synthetic_instrument_limitations()
    
    print(f"\n🎯 DIAGNOSIS RESULTS:")
    print("=" * 40)
    
    if not timeframe_test:
        print("❌ Basic timeframe testing failed")
        print("💡 Check MT5 connection and symbol availability")
    elif detailed_test:
        print("✅ 1-minute data testing completed")
        print("💡 LIKELY CAUSES:")
        print("   • Synthetic instruments may have limited 1-min historical data")
        print("   • 1-minute data might only be available for recent periods")
        print("   • Deriv may not provide full 1-minute history for synthetic indices")
        print("\n🔧 RECOMMENDATIONS:")
        print("   • Use 5-minute or 15-minute data for historical analysis")
        print("   • 1-minute data may work for real-time collection")
        print("   • Consider this normal behavior for synthetic instruments")
    else:
        print("❌ 1-minute data testing failed")
    
    return timeframe_test and detailed_test

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
