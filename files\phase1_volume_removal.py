#!/usr/bin/env python3
"""
Phase 1: Volume Removal Implementation
Remove all volume contamination from AI models and replace with clean features.
"""

import sys
import os
from datetime import datetime

def update_model_configurations():
    """Update model configurations to remove volume features."""
    print("🔧 UPDATING MODEL CONFIGURATIONS")
    print("=" * 60)
    
    # Model configuration updates
    config_updates = {
        "short_term_pattern_nn": {
            "old_features": ["price_action", "volume", "micro_patterns"],
            "new_features": ["price_action", "micro_patterns", "volatility_regime"],
            "contamination_removed": "volume"
        },
        
        "short_term_momentum_rf": {
            "old_features": ["momentum", "rsi", "macd", "volume_velocity"],
            "new_features": ["momentum", "rsi", "macd", "volatility_adjusted_momentum"],
            "contamination_removed": "volume_velocity"
        },
        
        "medium_term_trend_lstm": {
            "old_features": ["trend_strength", "moving_averages", "volume_trend"],
            "new_features": ["trend_strength", "moving_averages", "trend_sustainability"],
            "contamination_removed": "volume_trend"
        },
        
        "medium_term_breakout_rf": {
            "old_features": ["support_resistance", "volume_breakout", "volatility"],
            "new_features": ["support_resistance", "false_breakout_filter", "volatility"],
            "contamination_removed": "volume_breakout"
        }
    }
    
    print("📋 Configuration updates planned:")
    for model_name, updates in config_updates.items():
        print(f"\n🧠 {model_name}:")
        print(f"   ❌ Removing: {updates['contamination_removed']}")
        print(f"   ✅ Adding: {updates['new_features'][-1]}")
        print(f"   📊 New features: {updates['new_features']}")
    
    return config_updates

def create_clean_feature_extraction():
    """Create clean feature extraction functions without volume contamination."""
    print(f"\n🧹 CREATING CLEAN FEATURE EXTRACTION")
    print("=" * 60)
    
    clean_features = {
        "volatility_regime": {
            "description": "ATR-based volatility regime classification",
            "replaces": "volume",
            "features": ["atr_normalized", "bollinger_bandwidth", "volatility_percentile"]
        },
        
        "volatility_adjusted_momentum": {
            "description": "Momentum signals filtered by volatility threshold",
            "replaces": "volume_velocity", 
            "features": ["momentum_when_atr_high", "rsi_volatility_adjusted", "macd_atr_filtered"]
        },
        
        "trend_sustainability": {
            "description": "ADX + moving average slope for trend strength",
            "replaces": "volume_trend",
            "features": ["adx_strength", "ma_slope", "trend_consistency"]
        },
        
        "false_breakout_filter": {
            "description": "Volatility-based breakout validation",
            "replaces": "volume_breakout",
            "features": ["atr_breakout_threshold", "pre_breakout_compression", "post_breakout_expansion"]
        }
    }
    
    print("✅ Clean feature functions to implement:")
    for feature_name, details in clean_features.items():
        print(f"\n📊 {feature_name}:")
        print(f"   Purpose: {details['description']}")
        print(f"   Replaces: {details['replaces']}")
        print(f"   Components: {details['features']}")
    
    return clean_features

def backup_contaminated_files():
    """Backup files before modification."""
    print(f"\n💾 BACKING UP CONTAMINATED FILES")
    print("=" * 60)
    
    files_to_backup = [
        "ai_model_manager.py",
        "trading_signal_generator.py", 
        "dashboard_server.py"
    ]
    
    backup_dir = f"backups/phase1_volume_removal_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        for file_name in files_to_backup:
            if os.path.exists(file_name):
                backup_path = os.path.join(backup_dir, file_name)
                
                # Read original file
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Write backup
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"   ✅ Backed up: {file_name} -> {backup_path}")
            else:
                print(f"   ⚠️  File not found: {file_name}")
        
        print(f"\n📁 Backup directory: {backup_dir}")
        return backup_dir
        
    except Exception as e:
        print(f"   ❌ Backup failed: {e}")
        return None

def identify_volume_functions_to_remove():
    """Identify specific volume functions that need removal."""
    print(f"\n🔍 IDENTIFYING VOLUME FUNCTIONS TO REMOVE")
    print("=" * 60)
    
    volume_functions = {
        "trading_signal_generator.py": [
            "_get_volume_features",
            "_get_volume_trend_features", 
            "_get_volume_breakout_features",
            "_get_volume_velocity_features"
        ],
        
        "ai_model_manager.py": [
            "_extract_volume_features",
            "volume feature extraction in _generate_features",
            "volume_velocity feature handling",
            "volume_trend feature handling",
            "volume_breakout feature handling"
        ],
        
        "dashboard_server.py": [
            "_extract_volume_features",
            "volume feature extraction in model analysis"
        ]
    }
    
    print("❌ Volume functions to remove/replace:")
    for file_name, functions in volume_functions.items():
        print(f"\n📁 {file_name}:")
        for func in functions:
            print(f"   • {func}")
    
    return volume_functions

def create_implementation_plan():
    """Create detailed implementation plan."""
    print(f"\n📋 PHASE 1 IMPLEMENTATION PLAN")
    print("=" * 60)
    
    implementation_steps = [
        {
            "step": 1,
            "title": "Update Model Configurations",
            "tasks": [
                "Modify ai_model_manager.py model configs",
                "Remove volume features from feature lists",
                "Add clean replacement features"
            ],
            "files": ["ai_model_manager.py"]
        },
        
        {
            "step": 2, 
            "title": "Remove Volume Functions",
            "tasks": [
                "Remove _get_volume_features()",
                "Remove _get_volume_trend_features()",
                "Remove _get_volume_breakout_features()",
                "Remove _get_volume_velocity_features()"
            ],
            "files": ["trading_signal_generator.py"]
        },
        
        {
            "step": 3,
            "title": "Add Clean Feature Functions", 
            "tasks": [
                "Add _get_volatility_regime_features()",
                "Add _get_volatility_adjusted_momentum_features()",
                "Add _get_trend_sustainability_features()",
                "Add _get_false_breakout_filter_features()"
            ],
            "files": ["trading_signal_generator.py"]
        },
        
        {
            "step": 4,
            "title": "Update Feature Extraction Logic",
            "tasks": [
                "Update _generate_features() in ai_model_manager.py",
                "Update feature extraction in dashboard_server.py",
                "Remove volume feature calls",
                "Add clean feature calls"
            ],
            "files": ["ai_model_manager.py", "dashboard_server.py"]
        },
        
        {
            "step": 5,
            "title": "Test and Validate",
            "tasks": [
                "Test feature extraction without errors",
                "Validate feature vector sizes",
                "Ensure no volume dependencies remain",
                "Prepare for model retraining"
            ],
            "files": ["All modified files"]
        }
    ]
    
    print("🚀 Implementation steps:")
    for step in implementation_steps:
        print(f"\n{step['step']}. {step['title']}:")
        for task in step['tasks']:
            print(f"   • {task}")
        print(f"   📁 Files: {', '.join(step['files'])}")
    
    return implementation_steps

def generate_clean_feature_templates():
    """Generate templates for clean feature functions."""
    print(f"\n📝 GENERATING CLEAN FEATURE TEMPLATES")
    print("=" * 60)
    
    templates = {
        "volatility_regime_features": '''
def _get_volatility_regime_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
    """Extract volatility regime features (replaces volume features)."""
    features = []
    
    if len(df_indicators) >= 20:
        # ATR normalized by price
        atr = df_indicators['high'].rolling(14).max() - df_indicators['low'].rolling(14).min()
        atr_normalized = atr.iloc[-1] / latest_row['close']
        features.append(atr_normalized)
        
        # Bollinger Bandwidth
        bb_upper = df_indicators['close'].rolling(20).mean() + 2 * df_indicators['close'].rolling(20).std()
        bb_lower = df_indicators['close'].rolling(20).mean() - 2 * df_indicators['close'].rolling(20).std()
        bb_bandwidth = (bb_upper.iloc[-1] - bb_lower.iloc[-1]) / df_indicators['close'].rolling(20).mean().iloc[-1]
        features.append(bb_bandwidth)
        
        # Volatility percentile (current vs historical)
        volatility = df_indicators['close'].rolling(5).std()
        vol_percentile = (volatility.iloc[-1] - volatility.quantile(0.2)) / (volatility.quantile(0.8) - volatility.quantile(0.2))
        features.append(max(0, min(1, vol_percentile)))
    else:
        features.extend([0.0, 0.0, 0.5])
    
    return features
        ''',
        
        "volatility_adjusted_momentum_features": '''
def _get_volatility_adjusted_momentum_features(self, df_indicators: pd.DataFrame, latest_row: pd.Series) -> List[float]:
    """Extract volatility-adjusted momentum features (replaces volume_velocity)."""
    features = []
    
    if len(df_indicators) >= 20:
        # Calculate ATR threshold
        atr = df_indicators['high'].rolling(14).max() - df_indicators['low'].rolling(14).min()
        atr_threshold = atr.rolling(20).median().iloc[-1]
        current_atr = atr.iloc[-1]
        
        # Momentum only when ATR is above threshold
        if current_atr > atr_threshold:
            momentum_5 = latest_row['close'] / df_indicators['close'].iloc[-5] - 1
            features.append(momentum_5)
        else:
            features.append(0.0)  # No momentum signal in low volatility
        
        # RSI adjusted for volatility
        rsi = self._calculate_rsi(df_indicators['close'])
        vol_adjustment = min(2.0, current_atr / atr_threshold)
        rsi_adjusted = rsi * vol_adjustment
        features.append(rsi_adjusted / 100)
        
        # MACD filtered by ATR
        ema12 = df_indicators['close'].ewm(span=12).mean().iloc[-1]
        ema26 = df_indicators['close'].ewm(span=26).mean().iloc[-1]
        macd = (ema12 - ema26) / latest_row['close']
        
        if current_atr > atr_threshold * 0.8:  # Only use MACD in sufficient volatility
            features.append(macd)
        else:
            features.append(0.0)
    else:
        features.extend([0.0, 0.5, 0.0])
    
    return features
        '''
    }
    
    print("📝 Feature function templates created:")
    for template_name in templates.keys():
        print(f"   ✅ {template_name}")
    
    return templates

def main():
    """Main Phase 1 implementation function."""
    print("🔥 PHASE 1: VOLUME REMOVAL IMPLEMENTATION")
    print("=" * 80)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Update model configurations
    config_updates = update_model_configurations()
    
    # Step 2: Create clean feature extraction plan
    clean_features = create_clean_feature_extraction()
    
    # Step 3: Backup contaminated files
    backup_dir = backup_contaminated_files()
    
    if not backup_dir:
        print("❌ Backup failed - aborting implementation")
        return False
    
    # Step 4: Identify volume functions to remove
    volume_functions = identify_volume_functions_to_remove()
    
    # Step 5: Create implementation plan
    implementation_steps = create_implementation_plan()
    
    # Step 6: Generate clean feature templates
    templates = generate_clean_feature_templates()
    
    print(f"\n🎯 PHASE 1 PREPARATION COMPLETE")
    print("=" * 50)
    print("✅ Model configurations analyzed")
    print("✅ Clean features designed")
    print("✅ Files backed up")
    print("✅ Volume functions identified")
    print("✅ Implementation plan created")
    print("✅ Feature templates generated")
    
    print(f"\n📋 NEXT STEPS:")
    print("1. Execute model configuration updates")
    print("2. Remove volume functions from trading_signal_generator.py")
    print("3. Add clean feature functions")
    print("4. Update feature extraction logic")
    print("5. Test and validate changes")
    
    print(f"\n💾 Backup location: {backup_dir}")
    print(f"🚀 Ready to proceed with implementation!")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to continue with implementation...")
    sys.exit(0 if success else 1)
