#!/usr/bin/env python3
"""
Test script to verify the enhanced risk management dashboard display.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_risk_dashboard():
    """Test the enhanced risk management dashboard functionality."""
    
    print("🧪 TESTING ENHANCED RISK MANAGEMENT DASHBOARD")
    print("=" * 60)
    
    try:
        # Import the dashboard server
        from dashboard_server import DashboardDataManager

        print("✅ Dashboard server imported successfully")

        # Initialize dashboard server
        dashboard = DashboardDataManager()
        
        print("✅ Dashboard server initialized")
        
        # Test the risk metrics update function
        print("\n🔍 TESTING RISK METRICS UPDATE:")
        print("-" * 40)
        
        # Call the risk metrics update function
        dashboard._update_risk_metrics()
        
        # Get the risk metrics data
        risk_data = dashboard.dashboard_data.get("risk_metrics", {})
        
        print("📊 RISK METRICS DATA:")
        print(f"   Daily P&L: ${risk_data.get('daily_pnl', 0):.2f}")
        print(f"   Current Drawdown: ${risk_data.get('current_drawdown', 0):.2f}")
        print(f"   Active Positions: {risk_data.get('active_positions', 0)}/{risk_data.get('max_positions', 3)}")
        print(f"   Daily Trades: {risk_data.get('daily_trades', 0)}/30")
        print(f"   Monthly Trades: {risk_data.get('monthly_trades', 0)}")
        print(f"   Risk Level: {risk_data.get('risk_level', 'LOW')}")
        
        print("\n📋 TIMEFRAME LIMITS:")
        timeframe_limits = risk_data.get('timeframe_limits', {})
        for timeframe, data in timeframe_limits.items():
            print(f"   {timeframe.upper()}: Daily {data.get('daily', '0/10')}, Monthly {data.get('monthly', 0)}, Active: {data.get('active', 'No')}")
        
        print("\n🎯 SL/TP SETTINGS:")
        sl_tp_settings = risk_data.get('sl_tp_settings', {})
        for timeframe, settings in sl_tp_settings.items():
            print(f"   {timeframe.upper()}: SL {settings.get('sl_points', 0)} pts (${settings.get('sl_dollars', 0):.2f}), TP {settings.get('tp_points', 0)} pts (${settings.get('tp_dollars', 0):.2f})")
        
        print("\n🛡️ CIRCUIT BREAKERS:")
        circuit_breakers = risk_data.get('circuit_breakers', {})
        for key, value in circuit_breakers.items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
        
        # Verify all required fields are present
        required_fields = [
            'daily_pnl', 'current_drawdown', 'active_positions', 'daily_trades',
            'monthly_trades', 'risk_level', 'timeframe_limits', 'sl_tp_settings', 'circuit_breakers'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in risk_data:
                missing_fields.append(field)
        
        print(f"\n📊 FIELD VALIDATION:")
        if missing_fields:
            print(f"   ❌ Missing fields: {', '.join(missing_fields)}")
            return False
        else:
            print("   ✅ All required fields present")
        
        # Test data structure validation
        print(f"\n🔍 DATA STRUCTURE VALIDATION:")
        
        # Check timeframe limits structure
        if isinstance(timeframe_limits, dict) and len(timeframe_limits) == 3:
            print("   ✅ Timeframe limits structure valid")
        else:
            print("   ❌ Timeframe limits structure invalid")
            return False
        
        # Check SL/TP settings structure
        if isinstance(sl_tp_settings, dict) and len(sl_tp_settings) == 3:
            print("   ✅ SL/TP settings structure valid")
        else:
            print("   ❌ SL/TP settings structure invalid")
            return False
        
        # Check circuit breakers structure
        if isinstance(circuit_breakers, dict) and len(circuit_breakers) >= 5:
            print("   ✅ Circuit breakers structure valid")
        else:
            print("   ❌ Circuit breakers structure invalid")
            return False
        
        print(f"\n🎉 ENHANCED RISK DASHBOARD TEST PASSED!")
        print("✅ Risk metrics are being populated with real data")
        print("✅ Timeframe-specific limits are displayed")
        print("✅ SL/TP settings are shown for each timeframe")
        print("✅ Circuit breakers and limits are configured")
        print("✅ Dashboard will show comprehensive risk management info")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 AI TRADING SYSTEM - ENHANCED RISK DASHBOARD TEST")
    print("=" * 70)
    print("This will test the enhanced risk management dashboard display")
    print("=" * 70)
    
    success = test_enhanced_risk_dashboard()
    
    if success:
        print("\n🎉 TEST COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ Enhanced risk management dashboard is working")
        print("✅ Real-time risk data will be displayed")
        print("✅ Comprehensive risk information available")
        print("✅ Timeframe-specific risk metrics shown")
        print("=" * 50)
        print("\n📋 WHAT YOU'LL SEE IN DASHBOARD:")
        print("1. 📊 Current Status: Daily P&L, Drawdown, Active Positions")
        print("2. ⏰ Timeframe Limits: Daily/Monthly trade counts per timeframe")
        print("3. 🎯 SL/TP Settings: Stop loss and take profit for each timeframe")
        print("4. 🛡️ Circuit Breakers: All risk management limits and safeguards")
    else:
        print("\n❌ TEST FAILED!")
        print("Check the error messages above for details")
        
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
