"""
Test script for the Synthetic DEX 900 DOWN AI Trading System.
Verifies basic functionality and data collection.
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from synthetic_data_collector import SyntheticDataCollector
from synthetic_pattern_detector import SyntheticPatternDetector

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("SystemTest")

def test_directories():
    """Test that all required directories can be created."""
    print("Testing directory creation...")
    try:
        for directory in config.DIRECTORIES:
            os.makedirs(directory, exist_ok=True)
            if os.path.exists(directory):
                print(f"✓ Directory created: {directory}")
            else:
                print(f"✗ Failed to create: {directory}")
                return False
        return True
    except Exception as e:
        print(f"✗ Directory creation failed: {e}")
        return False

def test_mt5_connection():
    """Test MetaTrader 5 connection."""
    print("\nTesting MT5 connection...")
    try:
        data_collector = SyntheticDataCollector()
        if data_collector.mt5_connected:
            print("✓ MT5 connection successful")
            
            # Test symbol availability
            import MetaTrader5 as mt5
            symbol_info = mt5.symbol_info(config.SYMBOL)
            if symbol_info:
                print(f"✓ Symbol {config.SYMBOL} found")
                print(f"  Description: {symbol_info.description}")
                print(f"  Point: {symbol_info.point}")
                print(f"  Digits: {symbol_info.digits}")
            else:
                print(f"✗ Symbol {config.SYMBOL} not found")
                # Show available symbols
                symbols = mt5.symbols_get()
                if symbols:
                    print("Available symbols (first 10):")
                    for i, symbol in enumerate(symbols[:10]):
                        print(f"  {i+1}. {symbol.name}")
                        
            return data_collector.mt5_connected
        else:
            print("✗ MT5 connection failed")
            return False
    except Exception as e:
        print(f"✗ MT5 connection test failed: {e}")
        return False

def test_data_collection():
    """Test basic data collection functionality."""
    print("\nTesting data collection...")
    try:
        data_collector = SyntheticDataCollector()
        
        if not data_collector.mt5_connected:
            print("✗ Cannot test data collection - MT5 not connected")
            return False
            
        # Test getting latest data
        df = data_collector.get_latest_data(timeframe=1, count=10)
        if not df.empty:
            print(f"✓ Retrieved {len(df)} records of 1-minute data")
            print(f"  Latest timestamp: {df['timestamp'].iloc[-1]}")
            print(f"  Price range: {df['close'].min():.4f} - {df['close'].max():.4f}")
        else:
            print("✗ No data retrieved")
            
        # Test historical data collection (small sample)
        print("Testing small historical data collection...")
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=1)  # Just 1 hour for testing
        
        success = data_collector.collect_timeframe_data(1, start_date, end_date)
        if success is not False:  # None or True is acceptable
            print("✓ Historical data collection test passed")
        else:
            print("✗ Historical data collection test failed")
            
        return True
        
    except Exception as e:
        print(f"✗ Data collection test failed: {e}")
        return False

def test_pattern_detection():
    """Test pattern detection functionality."""
    print("\nTesting pattern detection...")
    try:
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        
        if not data_collector.mt5_connected:
            print("✗ Cannot test pattern detection - MT5 not connected")
            return False
            
        # Get some test data
        df = data_collector.get_latest_data(timeframe=1, count=100)
        if df.empty:
            print("✗ No data available for pattern testing")
            return False
            
        print(f"Testing pattern detection on {len(df)} data points...")
        
        # Test indicator calculation
        df_with_indicators = pattern_detector.calculate_synthetic_indicators(df)
        if not df_with_indicators.empty:
            print("✓ Synthetic indicators calculated successfully")
            
            # Show some indicator values
            latest = df_with_indicators.iloc[-1]
            print(f"  Latest regime state: {pattern_detector.regimes.get(latest.get('regime_state', 0), 'UNKNOWN')}")
            print(f"  Jumpiness score: {latest.get('jumpiness_score', 0):.4f}")
            print(f"  Volatility compression: {latest.get('volatility_compression', 0):.4f}")
            print(f"  Price acceleration: {latest.get('price_acceleration', 0):.4f}")
        else:
            print("✗ Failed to calculate synthetic indicators")
            return False
            
        # Test jump event detection
        jump_events = pattern_detector.detect_jump_events(df_with_indicators)
        print(f"✓ Detected {len(jump_events)} jump events")
        
        if jump_events:
            latest_event = jump_events[-1]
            print(f"  Latest event: {latest_event['event_type']} with magnitude {latest_event['magnitude']:.4f}")
            
        return True
        
    except Exception as e:
        print(f"✗ Pattern detection test failed: {e}")
        return False

def test_database_operations():
    """Test database operations."""
    print("\nTesting database operations...")
    try:
        data_collector = SyntheticDataCollector()
        
        # Test database connection
        cursor = data_collector.conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"✓ Database connected with {len(tables)} tables:")
        for table in tables:
            print(f"  - {table[0]}")
            
        # Test inserting test data
        test_timestamp = datetime.now().timestamp()
        cursor.execute("""
            INSERT INTO synthetic_indicators 
            (timestamp, timeframe, jumpiness_score, volatility_compression, 
             price_acceleration, tick_velocity, regime_state, pattern_similarity)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (test_timestamp, 1, 0.5, 0.3, 0.1, 0.8, 0, 0.7))
        
        data_collector.conn.commit()
        print("✓ Test data insertion successful")
        
        # Test data retrieval
        cursor.execute("SELECT COUNT(*) FROM synthetic_indicators")
        count = cursor.fetchone()[0]
        print(f"✓ Database contains {count} indicator records")
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_configuration():
    """Test configuration settings."""
    print("\nTesting configuration...")
    try:
        print(f"✓ System: {config.SYSTEM_NAME} v{config.VERSION}")
        print(f"✓ Symbol: {config.SYMBOL}")
        print(f"✓ Data source: {config.DATA_SOURCE}")
        print(f"✓ Timeframes configured: {len(config.SYNTHETIC_TIMEFRAMES)}")
        
        # Test risk rules
        risk_rules = config.SYNTHETIC_RISK_RULES
        print(f"✓ Base risk per trade: {risk_rules['position_sizing']['base_risk_per_trade']*100}%")
        print(f"✓ Max daily drawdown: {risk_rules['circuit_breakers']['max_daily_drawdown']*100}%")
        
        # Test execution settings
        exec_settings = config.EXECUTION_SETTINGS
        print(f"✓ Configured latency: {exec_settings['latency_ms']}ms")
        print(f"✓ Order timeout: {exec_settings['order_timeout_ms']}ms")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def run_all_tests():
    """Run all system tests."""
    print("=" * 60)
    print("SYNTHETIC DEX 900 DOWN AI TRADING SYSTEM - SYSTEM TESTS")
    print("=" * 60)
    
    tests = [
        ("Configuration", test_configuration),
        ("Directories", test_directories),
        ("MT5 Connection", test_mt5_connection),
        ("Data Collection", test_data_collection),
        ("Pattern Detection", test_pattern_detection),
        ("Database Operations", test_database_operations)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running {test_name} Test")
        print(f"{'-' * 40}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'=' * 60}")
    print("TEST SUMMARY")
    print(f"{'=' * 60}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for operation.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
