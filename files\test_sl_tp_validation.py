#!/usr/bin/env python3
"""
Test script to verify that the AI Trading System properly validates SL/TP minimum distances.
This ensures orders won't be rejected due to insufficient SL/TP distances.
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_sl_tp_validation():
    """Test that the system properly validates and corrects SL/TP distances."""
    try:
        print("🧪 TESTING SL/TP MINIMUM DISTANCE VALIDATION")
        print("=" * 60)
        
        # Import required modules
        from trading_signal_generator import TradingSignalGenerator, SignalType
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        
        print("📊 Initializing trading components...")
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        
        print("✅ Components initialized")
        
        # Get current price
        print("💰 Getting current market price...")
        import MetaTrader5 as mt5
        tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
        if tick is None:
            print("❌ Could not get current price!")
            return False
        current_price = (tick.bid + tick.ask) / 2
            
        print(f"✅ Current price: {current_price:.2f}")
        
        # Test SL/TP calculation with minimum distance validation
        print("\n🎯 TESTING SL/TP CALCULATION WITH VALIDATION:")
        
        # Create test regime analysis
        test_regime = {
            'regime_name': 'QUIET',
            'volatility': 0.01,
            'jump_probability': 0.3,
            'volatility_expansion': False,
            'jumpiness': 0.4
        }
        
        # Create dummy market data
        import pandas as pd
        dummy_data = pd.DataFrame({'close': [current_price]})

        # Test BUY signal
        print(f"\n📈 Testing BUY Signal SL/TP Calculation:")
        buy_sl, buy_tp = signal_generator._calculate_stop_loss_take_profit(
            SignalType.WEAK_BUY, current_price, test_regime, dummy_data
        )
        
        print(f"   Current Price: {current_price:.2f}")
        print(f"   Calculated Stop Loss: {buy_sl:.2f}")
        print(f"   Calculated Take Profit: {buy_tp:.2f}")
        
        # Check distances
        sl_distance = abs(current_price - buy_sl)
        tp_distance = abs(buy_tp - current_price)
        min_required = 10.05
        
        print(f"   SL Distance: {sl_distance:.2f} (min required: {min_required:.2f})")
        print(f"   TP Distance: {tp_distance:.2f} (min required: {min_required:.2f})")
        
        if sl_distance >= min_required:
            print(f"   ✅ BUY Stop Loss distance VALID")
        else:
            print(f"   ❌ BUY Stop Loss distance TOO SMALL")
            
        if tp_distance >= min_required:
            print(f"   ✅ BUY Take Profit distance VALID")
        else:
            print(f"   ❌ BUY Take Profit distance TOO SMALL")
        
        # Test SELL signal
        print(f"\n📉 Testing SELL Signal SL/TP Calculation:")
        sell_sl, sell_tp = signal_generator._calculate_stop_loss_take_profit(
            SignalType.WEAK_SELL, current_price, test_regime, dummy_data
        )
        
        print(f"   Current Price: {current_price:.2f}")
        print(f"   Calculated Stop Loss: {sell_sl:.2f}")
        print(f"   Calculated Take Profit: {sell_tp:.2f}")
        
        # Check distances
        sl_distance = abs(sell_sl - current_price)
        tp_distance = abs(current_price - sell_tp)
        
        print(f"   SL Distance: {sl_distance:.2f} (min required: {min_required:.2f})")
        print(f"   TP Distance: {tp_distance:.2f} (min required: {min_required:.2f})")
        
        if sl_distance >= min_required:
            print(f"   ✅ SELL Stop Loss distance VALID")
        else:
            print(f"   ❌ SELL Stop Loss distance TOO SMALL")
            
        if tp_distance >= min_required:
            print(f"   ✅ SELL Take Profit distance VALID")
        else:
            print(f"   ❌ SELL Take Profit distance TOO SMALL")
        
        # Test Order Execution System validation
        print(f"\n🔧 TESTING ORDER EXECUTION SYSTEM VALIDATION:")
        
        from order_execution_system import OrderExecutionSystem, OrderType
        
        order_executor = OrderExecutionSystem(data_collector)
        
        # Test validation method directly
        print(f"   Testing validation method...")
        
        # Test with distance too small (should be corrected)
        test_sl_too_close = current_price - 5.0  # Only 5 points (should be 10.05)
        corrected_sl = order_executor._validate_sl_tp_distance(
            test_sl_too_close, current_price, OrderType.BUY, "SL"
        )
        
        if corrected_sl and abs(current_price - corrected_sl) >= min_required:
            print(f"   ✅ SL auto-correction WORKING: {test_sl_too_close:.2f} → {corrected_sl:.2f}")
        else:
            print(f"   ❌ SL auto-correction FAILED")
            
        # Test with distance acceptable (should be unchanged)
        test_sl_good = current_price - 15.0  # 15 points (above minimum)
        unchanged_sl = order_executor._validate_sl_tp_distance(
            test_sl_good, current_price, OrderType.BUY, "SL"
        )
        
        if unchanged_sl == test_sl_good:
            print(f"   ✅ SL validation PASSED: {test_sl_good:.2f} unchanged")
        else:
            print(f"   ❌ SL validation FAILED: {test_sl_good:.2f} → {unchanged_sl:.2f}")
        
        # Summary
        print(f"\n📋 VALIDATION SUMMARY:")
        
        all_valid = True
        
        # Check all distances
        buy_sl_valid = abs(current_price - buy_sl) >= min_required
        buy_tp_valid = abs(buy_tp - current_price) >= min_required
        sell_sl_valid = abs(sell_sl - current_price) >= min_required
        sell_tp_valid = abs(current_price - sell_tp) >= min_required
        
        if buy_sl_valid and buy_tp_valid and sell_sl_valid and sell_tp_valid:
            print(f"   ✅ ALL SL/TP distances meet minimum requirements")
        else:
            print(f"   ❌ Some SL/TP distances below minimum")
            all_valid = False
            
        if corrected_sl and unchanged_sl == test_sl_good:
            print(f"   ✅ Order execution validation working correctly")
        else:
            print(f"   ❌ Order execution validation has issues")
            all_valid = False
        
        return all_valid
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 AI TRADING SYSTEM - SL/TP VALIDATION TEST")
    print("=" * 60)
    print("⚠️  This test verifies SL/TP minimum distance validation")
    print("=" * 60)
    
    success = test_sl_tp_validation()
    
    if success:
        print("\n🎉 SL/TP VALIDATION TEST PASSED!")
        print("✅ The system properly enforces minimum SL/TP distances!")
        print("✅ Orders will not be rejected due to insufficient distances!")
    else:
        print("\n❌ SL/TP VALIDATION TEST FAILED!")
        print("🔧 The system may have issues with SL/TP distance validation.")
        
    sys.exit(0 if success else 1)
