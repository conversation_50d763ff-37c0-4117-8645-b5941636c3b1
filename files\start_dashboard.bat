@echo off
echo ========================================
echo AI TRADING DASHBOARD LAUNCHER
echo ========================================
echo DEX 900 DOWN Index - 9 Model Ensemble
echo Real-time monitoring and analysis
echo Auto-refresh every 3 minutes
echo ========================================
echo.

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Check if activation was successful
if errorlevel 1 (
    echo ERROR: Could not activate virtual environment
    echo Please ensure venv exists and is properly configured
    pause
    exit /b 1
)

echo Virtual environment activated successfully
echo.

REM Start the dashboard
echo Starting AI Trading Dashboard...
echo Dashboard will be available at: http://localhost:5000
echo Browser will open automatically in 3 seconds
echo.
echo Press Ctrl+C to stop the dashboard
echo ========================================
echo.

python dashboard_server.py

echo.
echo Dashboard stopped.
pause
