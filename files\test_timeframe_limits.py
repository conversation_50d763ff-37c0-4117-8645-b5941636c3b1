"""
Test Timeframe Position Limits - Verify one trade per timeframe logic
"""

import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/test_timeframe_limits.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("TestTimeframeLimits")

def test_timeframe_limits():
    """Test the one-trade-per-timeframe logic."""
    print("🧪 TESTING TIMEFRAME POSITION LIMITS")
    print("=" * 60)
    
    try:
        # Import components
        from synthetic_data_collector import SyntheticDataCollector
        from order_execution_system import OrderExecutionSystem
        from trading_signal import TradingSignal, SignalType
        
        print("✅ Components imported")
        
        # Initialize components
        print("🔧 Initializing components...")
        data_collector = SyntheticDataCollector()
        order_executor = OrderExecutionSystem(data_collector)
        
        if not order_executor.mt5_connected:
            print("❌ MT5 not connected!")
            return False
            
        print("✅ MT5 connected")
        
        # Check initial timeframe positions
        print("\n📊 INITIAL TIMEFRAME POSITIONS:")
        for timeframe, position_id in order_executor.timeframe_positions.items():
            print(f"   {timeframe}: {position_id}")
            
        # Create test signals for different timeframes
        print("\n🎯 CREATING TEST SIGNALS:")
        
        # Short term signal
        short_signal = TradingSignal(
            signal_type=SignalType.STRONG_BUY,
            confidence=0.85,
            entry_price=52299.48,
            stop_loss=52249.48,
            take_profit=52349.48,
            position_size=0.01,
            reasoning="Short term momentum pattern detected on 5M timeframe"
        )
        
        # Medium term signal
        medium_signal = TradingSignal(
            signal_type=SignalType.STRONG_BUY,
            confidence=0.78,
            entry_price=52299.48,
            stop_loss=52249.48,
            take_profit=52349.48,
            position_size=0.01,
            reasoning="Medium term breakout pattern detected on 1H timeframe"
        )
        
        # Another short term signal (should be blocked)
        short_signal_2 = TradingSignal(
            signal_type=SignalType.STRONG_SELL,
            confidence=0.92,
            entry_price=52299.48,
            stop_loss=52349.48,
            take_profit=52249.48,
            position_size=0.01,
            reasoning="Short term reversal pattern on 15M timeframe"
        )
        
        print("✅ Test signals created")
        
        # Test signal execution
        print("\n🚀 TESTING SIGNAL EXECUTION:")
        
        # Test 1: Execute short term signal
        print("\n1️⃣ Testing SHORT TERM signal execution:")
        timeframe = order_executor._get_signal_timeframe(short_signal)
        print(f"   Signal timeframe: {timeframe}")
        can_execute = order_executor._can_execute_order(short_signal)
        print(f"   Can execute: {can_execute}")
        
        if can_execute:
            print("   ✅ SHORT TERM signal should execute")
        else:
            print("   ❌ SHORT TERM signal blocked")
            
        # Test 2: Execute medium term signal
        print("\n2️⃣ Testing MEDIUM TERM signal execution:")
        timeframe = order_executor._get_signal_timeframe(medium_signal)
        print(f"   Signal timeframe: {timeframe}")
        can_execute = order_executor._can_execute_order(medium_signal)
        print(f"   Can execute: {can_execute}")
        
        if can_execute:
            print("   ✅ MEDIUM TERM signal should execute")
        else:
            print("   ❌ MEDIUM TERM signal blocked")
            
        # Simulate first short term position
        print("\n🔄 SIMULATING SHORT TERM POSITION:")
        order_executor.timeframe_positions['short_term'] = 12345
        print(f"   Registered short_term position: 12345")
        
        # Test 3: Try another short term signal (should be blocked)
        print("\n3️⃣ Testing SECOND SHORT TERM signal (should be blocked):")
        timeframe = order_executor._get_signal_timeframe(short_signal_2)
        print(f"   Signal timeframe: {timeframe}")
        can_execute = order_executor._can_execute_order(short_signal_2)
        print(f"   Can execute: {can_execute}")
        
        if not can_execute:
            print("   ✅ SECOND SHORT TERM signal correctly blocked!")
        else:
            print("   ❌ SECOND SHORT TERM signal should have been blocked!")
            
        # Test 4: Medium term should still work
        print("\n4️⃣ Testing MEDIUM TERM signal (should still work):")
        can_execute = order_executor._can_execute_order(medium_signal)
        print(f"   Can execute: {can_execute}")
        
        if can_execute:
            print("   ✅ MEDIUM TERM signal still works correctly!")
        else:
            print("   ❌ MEDIUM TERM signal should not be blocked!")
            
        # Show final timeframe positions
        print("\n📊 FINAL TIMEFRAME POSITIONS:")
        for timeframe, position_id in order_executor.timeframe_positions.items():
            print(f"   {timeframe}: {position_id}")
            
        print("\n✅ TIMEFRAME LIMIT TESTING COMPLETED!")
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 AI TRADING SYSTEM - TIMEFRAME LIMITS TEST")
    print("=" * 70)
    print("This will test the one-trade-per-timeframe logic")
    print("=" * 70)
    
    success = test_timeframe_limits()
    
    if success:
        print("\n🎉 TEST COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ Timeframe position limits are working correctly")
        print("✅ One trade per timeframe logic implemented")
        print("✅ Multiple trades in same timeframe are blocked")
        print("✅ Different timeframes can trade simultaneously")
        print("=" * 50)
    else:
        print("\n❌ TEST FAILED!")
        print("Check the logs for detailed error information")
        
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
