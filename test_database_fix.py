#!/usr/bin/env python3
"""
Test script to verify the database connection fix for the "database is locked" error.
"""

import os
import sys
import sqlite3
import threading
import time
from datetime import datetime
import logging

# Add current directory to path
sys.path.append('.')

import config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_connection():
    """Test basic database connection."""
    try:
        db_path = os.path.join(config.DATA_CACHE_DIR, "synthetic_data.db")
        
        # Ensure directory exists
        os.makedirs(config.DATA_CACHE_DIR, exist_ok=True)
        
        # Test connection with proper settings
        conn = sqlite3.connect(db_path, timeout=30.0, check_same_thread=False)
        conn.execute("PRAGMA journal_mode=WAL;")
        conn.execute("PRAGMA busy_timeout=30000;")
        
        # Test basic operations
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        logger.info(f"✅ Database connection successful. Found {len(tables)} tables.")
        
        # Test a simple write operation
        cursor.execute("CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, timestamp REAL);")
        cursor.execute("INSERT OR REPLACE INTO test_table (id, timestamp) VALUES (1, ?);", (time.time(),))
        conn.commit()
        
        # Test read operation
        cursor.execute("SELECT * FROM test_table WHERE id = 1;")
        result = cursor.fetchone()
        
        if result:
            logger.info(f"✅ Database read/write test successful. Result: {result}")
        else:
            logger.error("❌ Database read test failed")
            
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connection test failed: {e}")
        return False

def test_concurrent_access():
    """Test concurrent database access to simulate the original problem."""
    
    def worker(worker_id, num_operations=10):
        """Worker function that performs database operations."""
        try:
            db_path = os.path.join(config.DATA_CACHE_DIR, "synthetic_data.db")
            
            for i in range(num_operations):
                conn = sqlite3.connect(db_path, timeout=30.0, check_same_thread=False)
                conn.execute("PRAGMA journal_mode=WAL;")
                conn.execute("PRAGMA busy_timeout=30000;")
                
                cursor = conn.cursor()
                
                # Simulate OHLCV data insertion (similar to the original error)
                timestamp = time.time() + i
                cursor.execute(
                    "INSERT OR REPLACE INTO ohlcv_data (timestamp, timeframe, open, high, low, close, volume, spread) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                    (timestamp, 1, 100.0, 101.0, 99.0, 100.5, 1000, 0)
                )
                conn.commit()
                conn.close()
                
                logger.info(f"Worker {worker_id}: Operation {i+1}/{num_operations} completed")
                time.sleep(0.1)  # Small delay to simulate real conditions
                
        except Exception as e:
            logger.error(f"Worker {worker_id} failed: {e}")
    
    logger.info("🔄 Testing concurrent database access...")
    
    # Create multiple threads to simulate concurrent access
    threads = []
    for i in range(5):  # 5 concurrent workers
        thread = threading.Thread(target=worker, args=(i+1, 5))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    logger.info("✅ Concurrent access test completed")

def test_synthetic_data_collector():
    """Test the SyntheticDataCollector with the new connection manager."""
    try:
        from synthetic_data_collector import SyntheticDataCollector
        
        logger.info("🔄 Testing SyntheticDataCollector...")
        
        # Create collector instance
        collector = SyntheticDataCollector()
        
        # Test database operations
        latest_data = collector.get_latest_data(timeframe=1, count=5)
        logger.info(f"✅ SyntheticDataCollector test successful. Retrieved {len(latest_data)} records")
        
        # Clean up
        collector.stop_all_threads()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ SyntheticDataCollector test failed: {e}")
        return False

def main():
    """Run all database tests."""
    logger.info("=" * 60)
    logger.info("DATABASE CONNECTION FIX VERIFICATION")
    logger.info("=" * 60)
    
    # Test 1: Basic connection
    logger.info("\n1. Testing basic database connection...")
    if not test_database_connection():
        logger.error("❌ Basic connection test failed. Stopping tests.")
        return False
    
    # Test 2: Concurrent access
    logger.info("\n2. Testing concurrent database access...")
    test_concurrent_access()
    
    # Test 3: SyntheticDataCollector
    logger.info("\n3. Testing SyntheticDataCollector...")
    if not test_synthetic_data_collector():
        logger.error("❌ SyntheticDataCollector test failed.")
        return False
    
    logger.info("\n" + "=" * 60)
    logger.info("✅ ALL TESTS PASSED - Database connection fix verified!")
    logger.info("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
