"""
Synthetic Pattern Detector for DEX 900 DOWN Index AI Trading System.
Specialized for detecting algorithmic patterns in Deriv's synthetic data.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional, Union
from scipy import stats
from scipy.signal import find_peaks, argrelextrema
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import sqlite3
import json

import config

# Set up logging
logger = logging.getLogger("SyntheticPatternDetector")

class SyntheticPatternDetector:
    """
    Detects patterns specific to Deriv's synthetic algorithmic behavior.
    Focuses on jump/drop events, volatility regimes, and mean reversion patterns.
    """
    
    def __init__(self, data_collector):
        """Initialize the pattern detector."""
        self.data_collector = data_collector
        self.scaler = StandardScaler()
        
        # Pattern detection parameters
        self.jump_threshold = 0.002  # 0.2% price movement threshold
        self.volatility_window = 50  # Window for volatility calculation
        self.pattern_length = 20     # Length of patterns to analyze
        
        # Regime states
        self.regimes = {
            0: "QUIET",
            1: "PRE_JUMP", 
            2: "JUMPING",
            3: "POST_JUMP",
            4: "REVERTING"
        }
        
        # Initialize pattern cache
        self.pattern_cache = {}
        self.regime_history = []
        
    def calculate_synthetic_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate indicators specific to synthetic algorithmic trading.
        These indicators are designed to detect Deriv's algorithm patterns.
        """
        if df.empty or len(df) < self.volatility_window:
            return df
            
        df = df.copy()
        
        # Basic price features
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['price_range'] = (df['high'] - df['low']) / df['close']
        
        # Volatility measures
        df['volatility'] = df['returns'].rolling(window=20).std()
        df['realized_vol'] = np.sqrt(252) * df['returns'].rolling(window=20).std()
        df['vol_ratio'] = df['volatility'] / df['volatility'].rolling(window=50).mean()
        
        # Jump detection indicators
        df['price_acceleration'] = self.calculate_price_acceleration(df)
        df['jumpiness_score'] = self.calculate_jumpiness_score(df)
        df['volatility_compression'] = self.calculate_volatility_compression(df)
        
        # Pattern recognition features
        df['tick_velocity'] = self.calculate_tick_velocity(df)
        df['momentum_burst'] = self.detect_momentum_bursts(df)
        df['mean_reversion_signal'] = self.calculate_mean_reversion_signal(df)
        
        # Regime detection
        df['regime_state'] = self.detect_volatility_regime(df)
        df['regime_transition'] = df['regime_state'].diff().fillna(0)
        
        # Threshold proximity (potential trigger levels)
        df['support_resistance'] = self.identify_support_resistance(df)
        df['threshold_proximity'] = self.calculate_threshold_proximity(df)
        
        return df
        
    def calculate_price_acceleration(self, df: pd.DataFrame) -> pd.Series:
        """Calculate rate of price change acceleration."""
        returns = df['returns'].fillna(0)
        velocity = returns.rolling(window=5).mean()
        acceleration = velocity.diff()
        
        # Normalize acceleration
        acceleration_norm = (acceleration - acceleration.rolling(window=20).mean()) / acceleration.rolling(window=20).std()
        return acceleration_norm.fillna(0)
        
    def calculate_jumpiness_score(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate a score indicating likelihood of imminent jump.
        Based on volatility compression and price action patterns.
        """
        # Volatility compression component
        vol_ratio = df['vol_ratio'].fillna(1)
        vol_compression = np.where(vol_ratio < 0.5, 1 - vol_ratio, 0)
        
        # Price clustering component (prices staying in tight range)
        price_std = df['close'].rolling(window=10).std()
        price_clustering = np.where(price_std < price_std.rolling(window=50).quantile(0.2), 1, 0)
        
        # Time since last jump component
        large_moves = np.abs(df['returns']) > self.jump_threshold
        time_since_jump = self._calculate_time_since_event(large_moves)
        time_component = np.minimum(time_since_jump / 100, 1)  # Normalize to 0-1
        
        # Combine components
        jumpiness = (vol_compression * 0.4 + price_clustering * 0.3 + time_component * 0.3)
        return pd.Series(jumpiness, index=df.index)
        
    def calculate_volatility_compression(self, df: pd.DataFrame) -> pd.Series:
        """Detect periods of volatility compression before jumps."""
        vol = df['volatility'].fillna(0)
        vol_ma = vol.rolling(window=50).mean()
        compression_ratio = vol / vol_ma
        
        # Compression occurs when current vol is much lower than average
        compression = np.where(compression_ratio < 0.3, 1 - compression_ratio, 0)
        return pd.Series(compression, index=df.index)
        
    def calculate_tick_velocity(self, df: pd.DataFrame) -> pd.Series:
        """Calculate speed of price movements."""
        price_changes = df['close'].diff().abs()
        velocity = price_changes.rolling(window=5).sum()
        
        # Normalize by recent average
        velocity_norm = velocity / velocity.rolling(window=20).mean()
        return velocity_norm.fillna(0)
        
    def detect_momentum_bursts(self, df: pd.DataFrame) -> pd.Series:
        """Detect sudden momentum bursts that might indicate jumps."""
        returns = df['returns'].fillna(0)
        
        # Calculate rolling momentum
        momentum = returns.rolling(window=3).sum()
        momentum_threshold = momentum.rolling(window=50).std() * 2
        
        # Detect bursts
        bursts = np.abs(momentum) > momentum_threshold
        return bursts.astype(int)
        
    def calculate_mean_reversion_signal(self, df: pd.DataFrame) -> pd.Series:
        """Calculate mean reversion signals after jumps."""
        returns = df['returns'].fillna(0)
        
        # Identify large moves (potential jumps)
        large_moves = np.abs(returns) > self.jump_threshold
        
        # Calculate deviation from recent mean
        price_ma = df['close'].rolling(window=20).mean()
        deviation = (df['close'] - price_ma) / price_ma
        
        # Mean reversion signal is stronger after large moves
        reversion_signal = np.where(large_moves.shift(1), np.abs(deviation), 0)
        return pd.Series(reversion_signal, index=df.index)
        
    def detect_volatility_regime(self, df: pd.DataFrame) -> pd.Series:
        """
        Detect current volatility regime using clustering.
        Returns regime state: QUIET, PRE_JUMP, JUMPING, POST_JUMP, REVERTING
        """
        if len(df) < 100:
            return pd.Series([0] * len(df), index=df.index)
            
        # Features for regime detection
        features = []
        
        # Volatility features
        vol = df['volatility'].fillna(0)
        features.append(vol)
        features.append(vol.rolling(window=5).mean())
        features.append(df['vol_ratio'].fillna(1))
        
        # Price movement features
        features.append(np.abs(df['returns'].fillna(0)))
        features.append(df['price_range'].fillna(0))
        
        # Momentum features
        features.append(df['price_acceleration'].fillna(0))
        features.append(df['tick_velocity'].fillna(0))
        
        # Combine features
        feature_matrix = np.column_stack(features)
        
        # Handle NaN values
        feature_matrix = np.nan_to_num(feature_matrix)
        
        # Normalize features
        if hasattr(self, '_regime_scaler'):
            feature_matrix_scaled = self._regime_scaler.transform(feature_matrix)
        else:
            self._regime_scaler = StandardScaler()
            feature_matrix_scaled = self._regime_scaler.fit_transform(feature_matrix)
        
        # Cluster into regimes
        n_regimes = 5
        kmeans = KMeans(n_clusters=n_regimes, random_state=42, n_init=10)
        regimes = kmeans.fit_predict(feature_matrix_scaled)
        
        # Map clusters to meaningful regime names based on characteristics
        regime_mapping = self._map_clusters_to_regimes(feature_matrix, regimes)
        mapped_regimes = [regime_mapping.get(r, 0) for r in regimes]
        
        return pd.Series(mapped_regimes, index=df.index)
        
    def _map_clusters_to_regimes(self, features: np.ndarray, clusters: np.ndarray) -> Dict[int, int]:
        """Map cluster numbers to meaningful regime states."""
        mapping = {}
        
        for cluster_id in np.unique(clusters):
            cluster_mask = clusters == cluster_id
            cluster_features = features[cluster_mask]
            
            # Calculate cluster characteristics
            avg_vol = np.mean(cluster_features[:, 0])  # Volatility
            avg_returns = np.mean(np.abs(cluster_features[:, 3]))  # Absolute returns
            avg_acceleration = np.mean(np.abs(cluster_features[:, 5]))  # Price acceleration
            
            # Map based on characteristics
            if avg_vol < 0.001 and avg_returns < 0.001:
                mapping[cluster_id] = 0  # QUIET
            elif avg_vol < 0.002 and avg_acceleration > 0.5:
                mapping[cluster_id] = 1  # PRE_JUMP
            elif avg_vol > 0.005 or avg_returns > 0.01:
                mapping[cluster_id] = 2  # JUMPING
            elif avg_vol > 0.002 and avg_acceleration < 0:
                mapping[cluster_id] = 4  # REVERTING
            else:
                mapping[cluster_id] = 3  # POST_JUMP
                
        return mapping
        
    def identify_support_resistance(self, df: pd.DataFrame) -> pd.Series:
        """Identify potential support and resistance levels."""
        if len(df) < 50:
            return pd.Series([0] * len(df), index=df.index)
            
        # Find local maxima and minima
        highs = argrelextrema(df['high'].values, np.greater, order=5)[0]
        lows = argrelextrema(df['low'].values, np.less, order=5)[0]
        
        # Create support/resistance indicator
        sr_levels = np.zeros(len(df))
        sr_levels[highs] = 1  # Resistance
        sr_levels[lows] = -1  # Support
        
        return pd.Series(sr_levels, index=df.index)
        
    def calculate_threshold_proximity(self, df: pd.DataFrame) -> pd.Series:
        """Calculate proximity to potential algorithmic trigger levels."""
        # Round numbers often act as psychological/algorithmic levels
        current_price = df['close']
        
        # Find nearest round numbers (e.g., 1000, 1050, 1100)
        round_levels = []
        for price in current_price:
            if pd.isna(price):
                continue
            # Find nearest 50-point levels
            lower_level = int(price // 50) * 50
            upper_level = lower_level + 50
            round_levels.append([lower_level, upper_level])
        
        # Calculate distance to nearest level
        proximity = []
        for i, price in enumerate(current_price):
            if pd.isna(price) or i >= len(round_levels):
                proximity.append(0)
                continue
                
            lower, upper = round_levels[i]
            dist_lower = abs(price - lower)
            dist_upper = abs(price - upper)
            min_dist = min(dist_lower, dist_upper)
            
            # Normalize by price (percentage distance)
            proximity.append(min_dist / price if price > 0 else 0)
        
        return pd.Series(proximity, index=df.index)
        
    def _calculate_time_since_event(self, event_series: pd.Series) -> np.ndarray:
        """Calculate time periods since last event occurrence."""
        time_since = np.zeros(len(event_series))
        last_event_idx = -1
        
        # Convert to numpy array to avoid timestamp issues
        events_array = event_series.values
        
        for i, event in enumerate(events_array):
            if event:
                last_event_idx = i
                time_since[i] = 0
            else:
                if last_event_idx >= 0:
                    time_since[i] = i - last_event_idx
                else:
                    time_since[i] = i
                    
        return time_since
        
    def detect_jump_events(self, df: pd.DataFrame) -> List[Dict]:
        """
        Detect and classify jump/drop events in the synthetic data.
        Returns list of events with metadata.
        """
        events = []
        
        if len(df) < 10:
            return events
            
        returns = df['returns'].fillna(0)
        
        # Detect significant price movements
        jump_threshold = self.jump_threshold
        large_moves = np.abs(returns) > jump_threshold
        
        # Group consecutive large moves into events
        event_groups = self._group_consecutive_events(large_moves)
        
        for start_idx, end_idx in event_groups:
            if end_idx - start_idx < 1:
                continue
                
            # Calculate event characteristics
            event_returns = returns.iloc[start_idx:end_idx+1]
            total_return = event_returns.sum()
            max_return = event_returns.max()
            min_return = event_returns.min()
            duration = end_idx - start_idx + 1
            
            # Classify event type
            if total_return > jump_threshold:
                event_type = "JUMP_UP"
            elif total_return < -jump_threshold:
                event_type = "JUMP_DOWN"
            else:
                event_type = "VOLATILITY_SPIKE"
                
            # Get pre and post patterns
            pre_pattern = self._extract_pattern(df, start_idx - self.pattern_length, start_idx)
            post_pattern = self._extract_pattern(df, end_idx, end_idx + self.pattern_length)
            
            event = {
                'timestamp': df.index[start_idx],
                'event_type': event_type,
                'magnitude': abs(total_return),
                'direction': 1 if total_return > 0 else -1,
                'duration': duration,
                'max_return': max_return,
                'min_return': min_return,
                'pre_pattern': pre_pattern,
                'post_pattern': post_pattern,
                'start_idx': start_idx,
                'end_idx': end_idx
            }
            
            events.append(event)
            
        return events
        
    def _group_consecutive_events(self, event_series: pd.Series) -> List[Tuple[int, int]]:
        """Group consecutive True values in event series."""
        groups = []
        start_idx = None
        
        for i, event in enumerate(event_series):
            if event and start_idx is None:
                start_idx = i
            elif not event and start_idx is not None:
                groups.append((start_idx, i - 1))
                start_idx = None
                
        # Handle case where series ends with an event
        if start_idx is not None:
            groups.append((start_idx, len(event_series) - 1))
            
        return groups
        
    def _extract_pattern(self, df: pd.DataFrame, start_idx: int, end_idx: int) -> List[float]:
        """Extract price pattern from specified range."""
        if start_idx < 0 or end_idx >= len(df) or start_idx >= end_idx:
            return []
            
        pattern_data = df['close'].iloc[start_idx:end_idx]
        if len(pattern_data) == 0:
            return []
            
        # Normalize pattern (percentage changes from first value)
        first_price = pattern_data.iloc[0]
        if first_price == 0:
            return []
            
        normalized_pattern = ((pattern_data / first_price) - 1).tolist()
        return normalized_pattern
        
    def analyze_pattern_similarity(self, current_pattern: List[float], 
                                 historical_patterns: List[List[float]], 
                                 threshold: float = 0.8) -> Dict:
        """
        Analyze similarity between current pattern and historical patterns.
        Returns best matches and confidence scores.
        """
        if not current_pattern or not historical_patterns:
            return {'matches': [], 'confidence': 0.0}
            
        similarities = []
        
        for hist_pattern in historical_patterns:
            if len(hist_pattern) != len(current_pattern):
                continue
                
            # Calculate correlation coefficient
            try:
                correlation = np.corrcoef(current_pattern, hist_pattern)[0, 1]
                if not np.isnan(correlation):
                    similarities.append(correlation)
            except:
                continue
                
        if not similarities:
            return {'matches': [], 'confidence': 0.0}
            
        # Find best matches
        similarities = np.array(similarities)
        best_matches = similarities[similarities > threshold]
        avg_confidence = np.mean(similarities) if len(similarities) > 0 else 0.0
        
        return {
            'matches': best_matches.tolist(),
            'confidence': avg_confidence,
            'best_match': np.max(similarities),
            'num_matches': len(best_matches)
        }
