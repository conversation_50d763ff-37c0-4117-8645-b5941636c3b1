#!/usr/bin/env python3
"""
Test script to verify exact SL/TP values for all timeframe groups.
This will show the actual point values and dollar equivalents.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trading_signal_generator import TradingSignalGenerator, SignalType
from synthetic_data_collector import SyntheticDataCollector
from ai_model_manager import AIModelManager
from synthetic_pattern_detector import SyntheticPatternDetector
import pandas as pd

def test_timeframe_sl_tp_values():
    """Test SL/TP values for all timeframe groups."""
    
    print("🧪 TESTING TIMEFRAME-SPECIFIC SL/TP VALUES")
    print("=" * 60)
    
    # Point to dollar conversion (based on your calculation)
    POINTS_TO_DOLLAR = 0.0001  # 1 point = $0.0001, so 5000 points = $0.50
    
    print(f"📊 CONVERSION RATE: 1 point = ${POINTS_TO_DOLLAR:.4f}")
    print(f"📊 VERIFICATION: 5000 points = ${5000 * POINTS_TO_DOLLAR:.2f}")
    print(f"📊 CURRENT MEDIUM: 40000 points = ${40000 * POINTS_TO_DOLLAR:.2f}")
    print()
    
    # Initialize components (minimal setup)
    try:
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector()
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(data_collector, ai_manager, pattern_detector)
        
        print("✅ Components initialized")
    except Exception as e:
        print(f"❌ Component initialization failed: {e}")
        return
    
    # Test current price (example)
    current_price = 54300.0  # Example DEX 900 DOWN price
    print(f"📈 Test Price: {current_price:.2f}")
    print()
    
    # Create test regime analysis
    test_regime = {
        'regime_name': 'NORMAL',
        'volatility': 0.01,  # Normal volatility
        'jump_probability': 0.3,
        'volatility_expansion': False,
        'jumpiness': 0.4
    }
    
    # Create dummy market data
    dummy_data = pd.DataFrame({'close': [current_price]})
    
    # Test all timeframe groups
    timeframe_groups = ['short_term', 'medium_term', 'long_term']
    signal_types = [SignalType.WEAK_BUY, SignalType.WEAK_SELL]
    
    for timeframe_group in timeframe_groups:
        print(f"🎯 TESTING {timeframe_group.upper()} TIMEFRAME")
        print("-" * 40)
        
        for signal_type in signal_types:
            try:
                # Calculate SL/TP for this timeframe group
                stop_loss, take_profit = signal_generator._calculate_stop_loss_take_profit(
                    signal_type, current_price, test_regime, dummy_data, timeframe_group
                )
                
                # Calculate distances in points
                if signal_type == SignalType.WEAK_BUY:
                    sl_distance = current_price - stop_loss
                    tp_distance = take_profit - current_price
                else:  # SELL
                    sl_distance = stop_loss - current_price
                    tp_distance = current_price - take_profit
                
                # Convert to dollars
                sl_dollars = sl_distance * POINTS_TO_DOLLAR
                tp_dollars = tp_distance * POINTS_TO_DOLLAR
                
                # Calculate risk/reward ratio
                risk_reward = tp_distance / sl_distance if sl_distance > 0 else 0
                
                print(f"📊 {signal_type.name}:")
                print(f"   Current Price: {current_price:.2f}")
                print(f"   Stop Loss:     {stop_loss:.2f} ({sl_distance:.1f} points = ${sl_dollars:.2f})")
                print(f"   Take Profit:   {take_profit:.2f} ({tp_distance:.1f} points = ${tp_dollars:.2f})")
                print(f"   Risk/Reward:   1:{risk_reward:.2f}")
                print()
                
            except Exception as e:
                print(f"❌ Error testing {signal_type.name}: {e}")
                print()
        
        print()
    
    # Summary comparison
    print("📋 SUMMARY COMPARISON")
    print("=" * 50)
    
    # Test with normal volatility and regime
    try:
        # Get base distances for comparison
        short_sl, short_tp = signal_generator._calculate_stop_loss_take_profit(
            SignalType.WEAK_BUY, current_price, test_regime, dummy_data, 'short_term'
        )
        medium_sl, medium_tp = signal_generator._calculate_stop_loss_take_profit(
            SignalType.WEAK_BUY, current_price, test_regime, dummy_data, 'medium_term'
        )
        long_sl, long_tp = signal_generator._calculate_stop_loss_take_profit(
            SignalType.WEAK_BUY, current_price, test_regime, dummy_data, 'long_term'
        )
        
        # Calculate distances
        short_sl_dist = current_price - short_sl
        short_tp_dist = short_tp - current_price
        medium_sl_dist = current_price - medium_sl
        medium_tp_dist = medium_tp - current_price
        long_sl_dist = current_price - long_sl
        long_tp_dist = long_tp - current_price
        
        print("BUY Signal Comparison:")
        print(f"SHORT TERM:  SL={short_sl_dist:.1f}pts (${short_sl_dist * POINTS_TO_DOLLAR:.2f}), TP={short_tp_dist:.1f}pts (${short_tp_dist * POINTS_TO_DOLLAR:.2f})")
        print(f"MEDIUM TERM: SL={medium_sl_dist:.1f}pts (${medium_sl_dist * POINTS_TO_DOLLAR:.2f}), TP={medium_tp_dist:.1f}pts (${medium_tp_dist * POINTS_TO_DOLLAR:.2f})")
        print(f"LONG TERM:   SL={long_sl_dist:.1f}pts (${long_sl_dist * POINTS_TO_DOLLAR:.2f}), TP={long_tp_dist:.1f}pts (${long_tp_dist * POINTS_TO_DOLLAR:.2f})")
        
        print()
        print("💰 DOLLAR VALUE COMPARISON:")
        print(f"SHORT TERM:  Risk=${short_sl_dist * POINTS_TO_DOLLAR:.2f}, Reward=${short_tp_dist * POINTS_TO_DOLLAR:.2f}")
        print(f"MEDIUM TERM: Risk=${medium_sl_dist * POINTS_TO_DOLLAR:.2f}, Reward=${medium_tp_dist * POINTS_TO_DOLLAR:.2f}")
        print(f"LONG TERM:   Risk=${long_sl_dist * POINTS_TO_DOLLAR:.2f}, Reward=${long_tp_dist * POINTS_TO_DOLLAR:.2f}")
        
    except Exception as e:
        print(f"❌ Error in summary: {e}")

if __name__ == "__main__":
    try:
        test_timeframe_sl_tp_values()
        print("\n✅ SL/TP VALUE TEST COMPLETED")
        
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
