"""
Synthetic Data Collector for DEX 900 DOWN Index AI Trading System.
Specialized for collecting and processing Deriv's synthetic algorithmic data.
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import MetaTrader5 as mt5
import sqlite3
import logging
from typing import Dict, List, Tuple, Optional, Union
import time
import threading
from queue import Queue, Empty
import json

import config

# Set up logging
logging.basicConfig(
    level=getattr(logging, config.LOGGING_CONFIG["level"]),
    format=config.LOGGING_CONFIG["format"],
    handlers=[
        logging.FileHandler("logs/synthetic_data_collector.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SyntheticDataCollector")

class DatabaseConnectionManager:
    """Manages database connections to prevent locking issues."""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, db_path):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self, db_path):
        if self._initialized:
            return
        self.db_path = db_path
        self.connection_pool = Queue(maxsize=10)  # Pool of connections
        self._initialized = True
        self._create_initial_connections()

    def _create_initial_connections(self):
        """Create initial pool of database connections."""
        for _ in range(5):  # Start with 5 connections
            conn = self._create_connection()
            if conn:
                self.connection_pool.put(conn)

    def _create_connection(self):
        """Create a new database connection with optimal settings."""
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=30.0,
                check_same_thread=False,
                isolation_level=None  # Autocommit mode
            )
            # Configure for concurrent access
            conn.execute("PRAGMA journal_mode=WAL;")
            conn.execute("PRAGMA busy_timeout=30000;")  # 30 seconds
            conn.execute("PRAGMA synchronous=NORMAL;")
            conn.execute("PRAGMA cache_size=10000;")
            conn.execute("PRAGMA temp_store=MEMORY;")
            conn.execute("PRAGMA foreign_keys=ON;")
            return conn
        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            return None

    def get_connection(self):
        """Get a connection from the pool."""
        try:
            # Try to get from pool with timeout
            conn = self.connection_pool.get(timeout=5.0)
            # Test if connection is still valid
            try:
                conn.execute("SELECT 1")
                return conn
            except:
                # Connection is stale, create new one
                conn.close()
                return self._create_connection()
        except Empty:
            # Pool is empty, create new connection
            return self._create_connection()

    def return_connection(self, conn):
        """Return a connection to the pool."""
        if conn:
            try:
                # Only return if pool isn't full
                self.connection_pool.put_nowait(conn)
            except:
                # Pool is full, close the connection
                conn.close()

class SyntheticDataCollector:
    """
    Specialized data collector for Deriv's synthetic DEX 900 DOWN Index.
    Focuses on pattern recognition and algorithmic behavior analysis.
    """
    
    def __init__(self):
        """Initialize the Synthetic Data Collector."""
        self.symbol = config.SYMBOL
        self.db_path = os.path.join(config.DATA_CACHE_DIR, "synthetic_data.db")
        self.db_writer_queue = Queue()
        self.db_writer_running = False
        self.is_collecting = False
        self.mt5_connected = False
        self.real_time_queue = Queue()

        # Initialize database connection manager
        self.db_manager = DatabaseConnectionManager(self.db_path)

        self.ensure_directories()
        self.init_database()
        self.connect_mt5()
        self.start_db_writer()
        
    def ensure_directories(self):
        """Ensure all necessary directories exist."""
        for directory in config.DIRECTORIES:
            os.makedirs(directory, exist_ok=True)
            
    def init_database(self):
        """Initialize SQLite database and create tables."""
        self.create_tables()

    def _get_db_connection(self):
        """Get a database connection from the connection manager."""
        return self.db_manager.get_connection()

    def _return_db_connection(self, conn):
        """Return a database connection to the connection manager."""
        self.db_manager.return_connection(conn)

    def create_tables(self):
        """Create database tables if they don't exist."""
        conn = None
        try:
            conn = self._get_db_connection()
            if conn is None:
                logger.error("Failed to get database connection for table creation")
                return

            cursor = conn.cursor()
            # Tick data table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tick_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT, timestamp REAL, bid REAL, ask REAL, last REAL,
                    volume INTEGER, time_msc INTEGER, flags INTEGER, volume_real REAL
                )
            """)
            # OHLCV data table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS ohlcv_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT, timestamp REAL, timeframe INTEGER, open REAL, high REAL,
                    low REAL, close REAL, volume INTEGER, spread INTEGER
                )
            """)
            # Pattern events table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS pattern_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT, timestamp REAL, event_type TEXT, magnitude REAL,
                    duration INTEGER, pre_pattern TEXT, post_pattern TEXT, confidence REAL
                )
            """)
            # Synthetic indicators table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS synthetic_indicators (
                    id INTEGER PRIMARY KEY AUTOINCREMENT, timestamp REAL, timeframe INTEGER, jumpiness_score REAL,
                    volatility_compression REAL, price_acceleration REAL, tick_velocity REAL,
                    regime_state TEXT, pattern_similarity REAL
                )
            """)
            conn.commit()
            logger.info("Database tables verified successfully")
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
        finally:
            if conn:
                self._return_db_connection(conn)

    def connect_mt5(self):
        """Establish connection to MetaTrader 5."""
        try:
            if not mt5.initialize():
                logger.error("MetaTrader5 initialization failed: %s", mt5.last_error())
                return
            if not mt5.terminal_info().connected:
                logger.warning("MetaTrader5 is not connected to a broker")
                return
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                logger.error(f"Symbol {self.symbol} not found")
                return
            if not mt5.symbol_select(self.symbol, True):
                logger.error(f"Failed to select symbol {self.symbol}")
                return
            self.mt5_connected = True
            logger.info(f"Successfully connected to MT5 and selected {self.symbol}")
        except Exception as e:
            logger.error(f"Error connecting to MT5: {e}")

    def collect_historical_data(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None):
        """
        Collect complete historical data for pattern analysis.
        Uses timeframe-specific historical ranges for optimal data availability.
        """
        if not self.mt5_connected:
            logger.error("MT5 not connected. Cannot collect historical data.")
            return False

        end_date = end_date or datetime.now()
        default_start_date = start_date or config.START_DATE

        logger.info(f"Queueing historical data collection with timeframe-specific ranges ending at {end_date.strftime('%Y-%m-%d')}")

        for timeframe_name, tf_config in config.SYNTHETIC_TIMEFRAMES.items():
            if "intervals" in tf_config:
                for interval in tf_config["intervals"]:
                    optimal_start_date = self._get_optimal_start_date(interval, end_date, default_start_date)
                    logger.info(f"Queueing {interval}min data for {timeframe_name} from {optimal_start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
                    self.collect_timeframe_data(interval, optimal_start_date, end_date)
        
        logger.info("Historical data collection has been fully queued.")
        return True

    def wait_for_queue_completion(self):
        """Blocks until the database writer queue is empty."""
        logger.info(f"Waiting for {self.db_writer_queue.qsize()} items to be written to the database...")
        self.db_writer_queue.join()
        logger.info("Database queue is empty. All data stored.")

    def _get_optimal_start_date(self, interval: int, end_date: datetime, default_start_date: datetime) -> datetime:
        """
        Calculate optimal start date for historical data collection based on timeframe.
        Synthetic instruments have limited historical depth for high-frequency data.
        """
        days_map = {1: 30, 5: 90, 15: 180, 30: 180} # Mapping of interval to optimal days
        optimal_days = days_map.get(interval)

        if optimal_days:
            logger.debug(f"Using {optimal_days} days for {interval}-minute data (synthetic limitation)")
            optimal_start = end_date - timedelta(days=optimal_days)
            return max(optimal_start, default_start_date)
        else:
            logger.debug(f"Using full historical range for {interval}-minute data")
            return default_start_date

    def get_mt5_timeframe(self, interval: int) -> int:
        """Convert interval in minutes to MT5 timeframe constant."""
        timeframe_map = {
            1: mt5.TIMEFRAME_M1, 5: mt5.TIMEFRAME_M5, 15: mt5.TIMEFRAME_M15,
            30: mt5.TIMEFRAME_M30, 60: mt5.TIMEFRAME_H1, 240: mt5.TIMEFRAME_H4,
            1440: mt5.TIMEFRAME_D1
        }
        return timeframe_map.get(interval, mt5.TIMEFRAME_M1)

    def collect_timeframe_data(self, interval, start_date, end_date):
        """Collect and store data for a specific timeframe."""
        try:
            mt5_timeframe = self.get_mt5_timeframe(interval)
            rates = mt5.copy_rates_range(self.symbol, mt5_timeframe, start_date, end_date)
            if rates is None or len(rates) == 0:
                logger.warning(f"No data received for {interval}min timeframe from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
                return
            df = pd.DataFrame(rates)
            df['timestamp'] = pd.to_datetime(df['time'], unit='s')
            self.store_ohlcv_data(df, interval)
            logger.info(f"Collected {len(df)} records for {interval}min timeframe")
        except Exception as e:
            logger.error(f"Error collecting {interval}min data: {e}")

    def store_ohlcv_data(self, df, timeframe):
        """Queue OHLCV data for database storage."""
        try:
            for _, row in df.iterrows():
                query = "INSERT OR REPLACE INTO ohlcv_data (timestamp, timeframe, open, high, low, close, volume, spread) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
                params = (row['time'], timeframe, row['open'], row['high'], row['low'], row['close'], row['tick_volume'], row.get('spread', 0))
                self.db_writer_queue.put((query, params))
        except Exception as e:
            logger.error(f"Error queuing OHLCV data: {e}")

    def start_real_time_collection(self):
        """Start real-time data collection in a separate thread."""
        if self.is_collecting:
            logger.warning("Real-time collection already running")
            return
        self.is_collecting = True
        self.real_time_thread = threading.Thread(target=self._real_time_collector)
        self.real_time_thread.daemon = True
        self.real_time_thread.start()
        logger.info("Real-time data collection started")

    def _real_time_collector(self):
        """Worker for collecting real-time data and performing in-memory OHLCV aggregation."""
        current_bars = {}

        while self.is_collecting:
            try:
                tick = mt5.symbol_info_tick(self.symbol)
                if not tick:
                    time.sleep(0.1)
                    continue

                self.store_tick_data(tick)
                self.real_time_queue.put({'timestamp': tick.time, 'bid': tick.bid, 'ask': tick.ask, 'last': tick.last, 'volume': tick.volume})

                price = (tick.bid + tick.ask) / 2
                tick_time = datetime.fromtimestamp(tick.time)

                for interval in [1, 5, 15, 30, 60, 240, 1440]:
                    bar_start_time = self._get_bar_start_time(tick_time, interval)

                    if interval not in current_bars:
                        current_bars[interval] = self._new_bar(bar_start_time, price, tick.volume)
                        continue

                    if bar_start_time > current_bars[interval]['start_time']:
                        # Finalize and queue the old bar
                        self._queue_bar_for_writing(current_bars[interval], interval)
                        # Start a new bar
                        current_bars[interval] = self._new_bar(bar_start_time, price, tick.volume)
                    else:
                        # Update the current bar
                        bar = current_bars[interval]
                        bar['high'] = max(bar['high'], price)
                        bar['low'] = min(bar['low'], price)
                        bar['close'] = price
                        bar['volume'] += tick.volume

            except Exception as e:
                logger.error(f"Error in real-time collector: {e}")
                time.sleep(1)

    def _new_bar(self, start_time, price, volume):
        """Creates a new bar dictionary."""
        return {
            'start_time': start_time,
            'open': price,
            'high': price,
            'low': price,
            'close': price,
            'volume': volume
        }

    def _queue_bar_for_writing(self, bar, timeframe):
        """Queues a completed OHLCV bar for writing to the database."""
        query = "INSERT OR REPLACE INTO ohlcv_data (timestamp, timeframe, open, high, low, close, volume, spread) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        params = (
            int(bar['start_time'].timestamp()),
            timeframe,
            bar['open'],
            bar['high'],
            bar['low'],
            bar['close'],
            bar['volume'],
            0
        )
        self.db_writer_queue.put((query, params))
        logger.debug(f"Queued {timeframe}min OHLCV bar for writing.")

    def _get_bar_start_time(self, dt: datetime, timeframe_minutes: int) -> datetime:
        """Calculate the start time of the current bar for a given timeframe."""
        if timeframe_minutes < 60:
            return dt.replace(minute=(dt.minute // timeframe_minutes) * timeframe_minutes, second=0, microsecond=0)
        elif timeframe_minutes < 1440:
            return dt.replace(hour=(dt.hour // (timeframe_minutes // 60)) * (timeframe_minutes // 60), minute=0, second=0, microsecond=0)
        else:
            return dt.replace(hour=0, minute=0, second=0, microsecond=0)

    def store_tick_data(self, tick):
        """Queue individual tick data for storage."""
        try:
            query = "INSERT INTO tick_data (timestamp, bid, ask, last, volume, time_msc, flags, volume_real) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
            params = (tick.time, tick.bid, tick.ask, tick.last, tick.volume, tick.time_msc, tick.flags, tick.volume_real)
            self.db_writer_queue.put((query, params))
        except Exception as e:
            logger.error(f"Error queuing tick data: {e}")

    def start_db_writer(self):
        """Start the dedicated database writer thread."""
        if self.db_writer_running:
            return
        self.db_writer_running = True
        self.db_writer_thread = threading.Thread(target=self._db_writer_worker)
        self.db_writer_thread.daemon = True
        self.db_writer_thread.start()
        logger.info("Dedicated database writer thread started")

    def _db_writer_worker(self):
        """Worker that writes data to the database from a queue, ensuring task completion."""
        conn = None
        try:
            conn = self._get_db_connection()
            if conn is None:
                logger.error("Failed to get database connection for writer worker")
                return

            cursor = conn.cursor()
            while self.db_writer_running or not self.db_writer_queue.empty():
                try:
                    query, params = self.db_writer_queue.get(timeout=0.1)
                    try:
                        cursor.execute(query, params)
                        conn.commit()
                    except Exception as e:
                        logger.error(f"DB write failed for query '{query[:50]}...': {e}")
                        # Try to reconnect on database lock errors
                        if "database is locked" in str(e).lower():
                            logger.info("Attempting to reconnect to database...")
                            self._return_db_connection(conn)
                            conn = self._get_db_connection()
                            if conn:
                                cursor = conn.cursor()
                            else:
                                logger.error("Failed to reconnect to database")
                                break
                    finally:
                        self.db_writer_queue.task_done() # Ensures join() doesn't block
                except Empty:
                    if not self.db_writer_running:
                        break
                    continue
        except Exception as e:
            logger.error(f"Database writer worker error: {e}")
        finally:
            if conn:
                self._return_db_connection(conn)
            logger.info("Database writer thread finished.")

    def stop_all_threads(self):
        """Stop all running threads gracefully."""
        # Stop the real-time collector
        self.is_collecting = False
        if hasattr(self, 'real_time_thread') and self.real_time_thread.is_alive():
            self.real_time_thread.join(timeout=5)
        logger.info("Real-time data collection stopped")

        # Wait for the DB queue to be empty before stopping the writer
        logger.info(f"Waiting for {self.db_writer_queue.qsize()} items to be written to the database...")
        self.db_writer_queue.join()
        logger.info("Database queue empty.")

        # Stop the DB writer thread
        self.db_writer_running = False
        if hasattr(self, 'db_writer_thread') and self.db_writer_thread.is_alive():
            self.db_writer_thread.join(timeout=10)
        logger.info("Database writer thread stopped")

    def get_latest_data(self, timeframe, count=100):
        """Get latest OHLCV data for a specific timeframe."""
        conn = None
        try:
            conn = self._get_db_connection()
            if conn is None:
                logger.error("Failed to get database connection for latest data")
                return pd.DataFrame()

            df = pd.read_sql_query(f"SELECT * FROM ohlcv_data WHERE timeframe = ? ORDER BY timestamp DESC LIMIT ?", conn, params=(timeframe, count))
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                return df.sort_values('timestamp').reset_index(drop=True)
        except Exception as e:
            logger.error(f"Error getting latest data: {e}")
        finally:
            if conn:
                self._return_db_connection(conn)
        return pd.DataFrame()

    def __del__(self):
        """Destructor to ensure all threads are stopped."""
        logger.info("SyntheticDataCollector shutting down...")
        self.stop_all_threads()
        if self.mt5_connected:
            mt5.shutdown()
        logger.info("SyntheticDataCollector cleanup completed")
