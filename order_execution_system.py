"""
Order Execution System for Synthetic DEX 900 DOWN Index Trading System.
Handles real-time order placement, management, and execution with MT5.
"""

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import MetaTrader5 as mt5

import config
from trading_signal_generator import TradingSignal, SignalType

# Set up logging
logger = logging.getLogger("OrderExecutionSystem")

class OrderType(Enum):
    """Order types for MT5."""
    BUY = mt5.ORDER_TYPE_BUY
    SELL = mt5.ORDER_TYPE_SELL
    BUY_STOP = mt5.ORDER_TYPE_BUY_STOP
    SELL_STOP = mt5.ORDER_TYPE_SELL_STOP
    BUY_LIMIT = mt5.ORDER_TYPE_BUY_LIMIT
    SELL_LIMIT = mt5.ORDER_TYPE_SELL_LIMIT

class OrderStatus(Enum):
    """Order status tracking."""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    PARTIAL = "partial"

@dataclass
class Order:
    """Order information."""
    order_id: Optional[int]
    signal: TradingSignal
    order_type: OrderType
    volume: float
    price: float
    stop_loss: float
    take_profit: float
    status: OrderStatus
    timestamp: datetime
    fill_price: Optional[float] = None
    fill_time: Optional[datetime] = None
    commission: float = 0.0
    swap: float = 0.0
    profit: float = 0.0

@dataclass
class Position:
    """Position information."""
    position_id: int
    symbol: str
    volume: float
    price_open: float
    price_current: float
    stop_loss: float
    take_profit: float
    profit: float
    swap: float
    commission: float
    time_open: datetime
    order: Order
    # Position scaling fields
    original_volume: float = 0.0  # Original intended volume
    remaining_volume: float = 0.0  # Volume remaining to be scaled in
    scale_in_levels: Optional[List[float]] = None  # Price levels for scaling in
    scale_out_levels: Optional[List[float]] = None  # Profit levels for scaling out
    scaled_entries: Optional[List[Dict]] = None  # Track scaled entries
    scaled_exits: Optional[List[Dict]] = None  # Track scaled exits
    is_scaling_position: bool = False  # Flag for scaling positions

class OrderExecutionSystem:
    """
    Handles order execution and position management for synthetic trading.
    Optimized for DEX 900 DOWN Index with ultra-low latency requirements.
    """
    
    def __init__(self, data_collector, trading_engine=None):
        """Initialize the Order Execution System."""
        self.data_collector = data_collector
        self.trading_engine = trading_engine  # Reference to trading engine for daily reset
        
        # MT5 connection
        self.mt5_connected = False
        self.symbol = config.SYMBOL
        self.magic_number = config.EXECUTION_SETTINGS["mt5_magic_number"]
        self.deviation = config.EXECUTION_SETTINGS["mt5_deviation"]
        
        # Order tracking
        self.active_orders: Dict[int, Order] = {}
        self.active_positions: Dict[int, Position] = {}
        self.order_history: List[Order] = []

        # Position scaling tracking
        self.scaling_positions: Dict[int, Dict] = {}  # Track positions with scaling logic

        # Timeframe position tracking (ONE TRADE PER TIMEFRAME)
        self.timeframe_positions: Dict[str, Optional[int]] = {
            'short_term': None,    # Track active short-term position ID
            'medium_term': None,   # Track active medium-term position ID
            'long_term': None      # Track active long-term position ID
        }

        # Risk management
        self.max_concurrent_positions = config.MAX_CONCURRENT_POSITIONS
        self.daily_trade_count = 0
        self.daily_pnl = 0.0
        self.last_reset_date = datetime.now().date()

        # Fresh start tracking - only consider trades from this timestamp forward
        self.fresh_start_time = None  # Will be set when fresh start is initiated

        # Auto fresh start check will be done after MT5 connection

        # Timeframe-specific trade counters
        self.timeframe_daily_trades = {
            'short_term': 0,
            'medium_term': 0,
            'long_term': 0
        }
        self.timeframe_monthly_trades = {
            'short_term': 0,
            'medium_term': 0,
            'long_term': 0
        }
        self.last_monthly_reset = datetime.now().replace(day=1).date()  # First day of current month

        # Shared counter file for cross-process communication
        self.counter_file = "data/timeframe_counters.json"
        self._load_shared_counters()

        # Performance tracking
        self.execution_times = []
        self.slippage_data = []
        self.fill_rates = {"successful": 0, "failed": 0}
        
        # Threading for position monitoring
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Initialize MT5 connection
        self._initialize_mt5()

        # Start position monitoring immediately to detect existing positions
        if self.mt5_connected:
            self._start_position_monitoring()

            # Check if we should auto-set fresh start time (after MT5 is connected)
            self._check_and_set_auto_fresh_start()

        logger.info("Order Execution System initialized")

    def set_fresh_start_time(self, start_time: Optional[datetime] = None):
        """Set fresh start time for P&L calculation - only consider trades from this point forward."""
        if start_time is None:
            start_time = datetime.now()

        self.fresh_start_time = start_time
        logger.info(f"Fresh start time set: {start_time}")
        logger.info("Daily P&L will now only consider trades from this point forward")

    def _check_and_set_auto_fresh_start(self):
        """Check if we need to auto-set fresh start time to avoid emergency stop (ONE-TIME ONLY)."""
        try:
            import config
            from datetime import datetime, date

            # ONE-TIME ONLY: Only activate on 2025-06-18 (today)
            today = date.today()
            target_date = date(2025, 6, 18)

            if today != target_date:
                logger.debug(f"Auto fresh start disabled - only active on {target_date}, today is {today}")
                return

            # Get the current daily P&L (using old calculation)
            temp_fresh_start = self.fresh_start_time  # Save current state
            self.fresh_start_time = None  # Temporarily disable fresh start

            old_daily_pnl = self._get_daily_closed_trades_pnl()

            # Restore fresh start state
            self.fresh_start_time = temp_fresh_start

            # Check if old P&L would trigger emergency stop
            max_drawdown = config.SYNTHETIC_RISK_RULES["circuit_breakers"]["max_daily_drawdown"]

            if old_daily_pnl < -max_drawdown:
                # Auto-set fresh start time to avoid emergency stop (ONE-TIME ONLY)
                self.set_fresh_start_time()
                logger.warning(f"ONE-TIME AUTO FRESH START: Activated to avoid emergency stop")
                logger.warning(f"Old daily P&L: ${old_daily_pnl:.2f} would exceed limit: -${max_drawdown:.2f}")
                logger.info("System will now start with fresh daily P&L calculation")
                logger.info("NOTE: This is a one-time feature for testing today only")
            else:
                logger.debug(f"No auto fresh start needed. Daily P&L: ${old_daily_pnl:.2f}")

        except Exception as e:
            logger.warning(f"Error checking auto fresh start: {e}")
            # Only set fresh start as safety measure if it's the target date
            from datetime import date
            if date.today() == date(2025, 6, 18):
                self.set_fresh_start_time()
                logger.info("Set fresh start time as safety measure due to error (one-time only)")
            else:
                logger.debug("Auto fresh start safety measure disabled - not target date")

    def _load_shared_counters(self):
        """Load timeframe counters from shared file."""
        try:
            import json
            import os

            # Ensure data directory exists
            os.makedirs("data", exist_ok=True)

            if os.path.exists(self.counter_file):
                with open(self.counter_file, 'r') as f:
                    data = json.load(f)

                # Load counters if they exist
                if 'daily' in data:
                    self.timeframe_daily_trades.update(data['daily'])
                if 'monthly' in data:
                    self.timeframe_monthly_trades.update(data['monthly'])

                logger.debug(f"Loaded shared counters: Daily={self.timeframe_daily_trades}, Monthly={self.timeframe_monthly_trades}")
            else:
                # Create initial file
                self._save_shared_counters()
                logger.debug("Created initial shared counter file")

        except Exception as e:
            logger.warning(f"Error loading shared counters: {e}")

    def _save_shared_counters(self):
        """Save timeframe counters to shared file."""
        try:
            import json
            import os

            # Ensure data directory exists
            os.makedirs("data", exist_ok=True)

            data = {
                'daily': self.timeframe_daily_trades.copy(),
                'monthly': self.timeframe_monthly_trades.copy(),
                'last_update': datetime.now().isoformat(),
                'last_daily_reset': self.last_reset_date.isoformat() if self.last_reset_date else None,
                'last_monthly_reset': self.last_monthly_reset.isoformat() if self.last_monthly_reset else None
            }

            with open(self.counter_file, 'w') as f:
                json.dump(data, f, indent=2)

            logger.debug(f"Saved shared counters: Daily={self.timeframe_daily_trades}, Monthly={self.timeframe_monthly_trades}")

        except Exception as e:
            logger.warning(f"Error saving shared counters: {e}")

    def _initialize_mt5(self) -> bool:
        """Initialize MT5 connection."""
        try:
            if not mt5.initialize():
                logger.error("MT5 initialization failed")
                return False
                
            # Check symbol availability
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                logger.error(f"Symbol {self.symbol} not found")
                return False
                
            # Enable symbol for trading
            if not mt5.symbol_select(self.symbol, True):
                logger.error(f"Failed to select symbol {self.symbol}")
                return False
                
            self.mt5_connected = True
            logger.info(f"MT5 connected successfully to {self.symbol}")
            return True
            
        except Exception as e:
            logger.error(f"MT5 initialization error: {e}")
            return False
            
    def execute_signal(self, signal: TradingSignal) -> Optional[Order]:
        """Execute a trading signal by placing an order."""
        try:
            # Reset daily counters if new day
            self._reset_daily_counters()
            
            # Check if we can execute
            if not self._can_execute_order(signal):
                return None
                
            # Determine order type
            order_type = self._signal_to_order_type(signal.signal_type)
            
            # Calculate volume
            full_volume = self._calculate_volume(signal)

            # Check if position scaling should be used
            use_scaling = self._should_use_position_scaling(signal)
            if use_scaling:
                scaling_params = self._calculate_scaling_parameters(signal, full_volume)
                if scaling_params:
                    volume = scaling_params['initial_volume']
                    logger.info(f"Using position scaling: initial volume {volume:.2f} of total {full_volume:.2f}")
                else:
                    volume = full_volume
                    use_scaling = False
            else:
                volume = full_volume
            
            # Get current market price
            current_price = self._get_current_price()
            if current_price is None:
                logger.error("Could not get current price")
                return None
                
            # Adjust prices for market execution
            execution_price = self._adjust_execution_price(current_price, order_type)
            
            # Create order object
            order = Order(
                order_id=None,
                signal=signal,
                order_type=order_type,
                volume=volume,
                price=execution_price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                status=OrderStatus.PENDING,
                timestamp=datetime.now()
            )
            
            # Execute the order
            start_time = time.time()
            result = self._place_mt5_order(order)
            execution_time = (time.time() - start_time) * 1000  # Convert to ms
            
            if result:
                order.order_id = result.order
                order.status = OrderStatus.FILLED
                order.fill_price = result.price
                order.fill_time = datetime.now()
                
                # Track execution performance
                self.execution_times.append(execution_time)
                self.fill_rates["successful"] += 1
                
                # Calculate slippage
                expected_price = execution_price
                actual_price = result.price
                slippage = abs(actual_price - expected_price) / expected_price
                self.slippage_data.append(slippage)
                
                # Store order
                self.active_orders[order.order_id] = order
                self.daily_trade_count += 1

                # Set up position scaling if enabled
                if use_scaling and scaling_params:
                    self.scaling_positions[order.order_id] = {
                        'scaling_params': scaling_params,
                        'original_volume': full_volume,
                        'remaining_volume': scaling_params['remaining_volume']
                    }

                # Register timeframe position and increment counters
                timeframe = self._get_signal_timeframe(order.signal)
                if timeframe:
                    self.timeframe_positions[timeframe] = order.order_id

                    # Increment timeframe-specific trade counters
                    self.timeframe_daily_trades[timeframe] += 1
                    self.timeframe_monthly_trades[timeframe] += 1

                    # Save counters to shared file for dashboard access
                    self._save_shared_counters()

                    logger.info(f"Registered {timeframe} position: {order.order_id}")
                    logger.info(f"{timeframe} trades - Daily: {self.timeframe_daily_trades[timeframe]}, Monthly: {self.timeframe_monthly_trades[timeframe]}")
                    logger.info(f"Active positions: {len(self.active_positions)} (max 3: 1 per timeframe)")

                logger.info(f"Order executed successfully: {order.order_id}, "
                          f"Price: {result.price}, Time: {execution_time:.1f}ms")

                # Start monitoring if not already active
                if not self.monitoring_active:
                    self._start_position_monitoring()
                    
                return order
            else:
                order.status = OrderStatus.REJECTED
                self.fill_rates["failed"] += 1
                logger.warning(f"Order execution failed for signal: {signal.signal_type.name}")
                return order
                
        except Exception as e:
            logger.error(f"Error executing signal: {e}")
            return None
            
    def _signal_to_order_type(self, signal_type: SignalType) -> OrderType:
        """Convert trading signal to MT5 order type."""
        if signal_type in [SignalType.WEAK_BUY, SignalType.STRONG_BUY]:
            return OrderType.BUY
        elif signal_type in [SignalType.WEAK_SELL, SignalType.STRONG_SELL]:
            return OrderType.SELL
        else:
            raise ValueError(f"Invalid signal type: {signal_type}")
            
    def _calculate_volume(self, signal: TradingSignal) -> float:
        """Calculate order volume based on signal and risk management."""
        try:
            # Get symbol info first to understand volume requirements
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                logger.warning("Could not get symbol info, using default volume")
                return 0.01

            # Log symbol volume requirements for debugging
            logger.debug(f"Symbol volume info - Min: {symbol_info.volume_min}, Max: {symbol_info.volume_max}, Step: {symbol_info.volume_step}")

            # Get account balance
            account_info = mt5.account_info()
            if account_info is None:
                logger.warning("Could not get account info, using minimum volume")
                return symbol_info.volume_min

            balance = account_info.balance
            logger.debug(f"Account balance: {balance}")

            # Calculate volume based on position size percentage
            position_value = balance * signal.position_size
            logger.debug(f"Position value: {position_value} (balance: {balance} * position_size: {signal.position_size})")

            # For DEX 900 DOWN Index, use a simplified volume calculation
            # Start with a base volume and adjust based on position size
            base_volume = 0.01  # Base volume for synthetic indices

            # Scale volume based on position size (but keep it reasonable)
            if signal.position_size > 0.01:  # If position size > 1%
                volume_multiplier = min(signal.position_size * 100, 10)  # Cap at 10x
                calculated_volume = base_volume * volume_multiplier
            else:
                calculated_volume = base_volume

            # Round to symbol's volume step
            volume_step = symbol_info.volume_step
            if volume_step > 0:
                calculated_volume = round(calculated_volume / volume_step) * volume_step

            # Ensure minimum and maximum volume constraints
            min_volume = symbol_info.volume_min
            max_volume = symbol_info.volume_max

            final_volume = max(min_volume, min(calculated_volume, max_volume))

            logger.info(f"Volume calculation: calculated={calculated_volume:.4f}, final={final_volume:.4f} (min={min_volume}, max={max_volume}, step={volume_step})")

            return final_volume

        except Exception as e:
            logger.error(f"Error calculating volume: {e}")
            # Return a safe default volume
            return 0.01
        
    def _get_current_price(self) -> Optional[float]:
        """Get current market price."""
        try:
            tick = mt5.symbol_info_tick(self.symbol)
            if tick is None:
                return None
            return (tick.bid + tick.ask) / 2  # Mid price
        except Exception as e:
            logger.error(f"Error getting current price: {e}")
            return None

    def _validate_sl_tp_distance(self, level: float, current_price: float, order_type: OrderType, level_type: str, timeframe_group: str = 'medium_term') -> Optional[float]:
        """Validate SL/TP distance meets fixed timeframe-specific requirements for DEX 900 DOWN Index."""

        # FIXED TIMEFRAME-SPECIFIC DISTANCES (in points) - matching trading signal generator
        # Updated per user request: Fixed values without dynamic adjustments
        expected_distances = {
            'short_term': {
                'sl_buy': 50.0, 'tp_buy': 100.0,
                'sl_sell': 50.0, 'tp_sell': 100.0
            },
            'medium_term': {
                'sl_buy': 125.0, 'tp_buy': 250.0,
                'sl_sell': 150.0, 'tp_sell': 100.0
            },
            'long_term': {
                'sl_buy': 250.0, 'tp_buy': 500.0,
                'sl_sell': 300.0, 'tp_sell': 100.0
            }
        }

        try:
            # Calculate distance from current price
            distance = abs(level - current_price)

            # Get expected distance for validation
            timeframe_data = expected_distances.get(timeframe_group, expected_distances['medium_term'])
            
            if order_type == OrderType.BUY:
                expected_distance = timeframe_data['sl_buy'] if level_type == "SL" else timeframe_data['tp_buy']
            else:  # SELL
                expected_distance = timeframe_data['sl_sell'] if level_type == "SL" else timeframe_data['tp_sell']

            # Allow small tolerance (±5 points) for rounding differences
            tolerance = 5.0
            if abs(distance - expected_distance) > tolerance:
                logger.warning(f"{level_type} distance {distance:.2f} differs from expected {expected_distance:.2f} (tolerance: ±{tolerance})")

            # Return the level as-is since we're using fixed values from signal generator
            return level

        except Exception as e:
            logger.error(f"Error validating {level_type} distance: {e}")
            return None

    def _adjust_execution_price(self, current_price: float, order_type: OrderType) -> float:
        """Adjust execution price for market conditions."""
        # For market orders, use current price
        # For synthetic indices, we typically use market execution
        return current_price
        
    def _place_mt5_order(self, order: Order):
        """Place order with MT5."""
        try:
            # Get current price for SL/TP validation
            current_price = self._get_current_price()
            if current_price is None:
                logger.error("Could not get current price for SL/TP validation")
                return None

            # Get timeframe group from signal for validation and comment
            timeframe_group = self._get_signal_timeframe(order.signal) or 'medium_term'

            # Create timeframe abbreviation for comment
            timeframe_abbrev = {
                'short_term': 'SHORT',
                'medium_term': 'MEDIUM',
                'long_term': 'LONG'
            }.get(timeframe_group, 'MEDIUM')

            # For synthetic indices like DEX 900 DOWN Index, use market execution
            # without specifying filling mode (let MT5 use default)
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.symbol,
                "volume": order.volume,
                "type": order.order_type.value,
                "deviation": self.deviation,
                "magic": self.magic_number,
                "comment": f"AI_BOT_{timeframe_abbrev}_{order.signal.signal_type.name}",
                "type_time": mt5.ORDER_TIME_GTC,  # Good Till Cancelled
                "type_filling": mt5.ORDER_FILLING_FOK,  # Fill or Kill (most compatible)
            }

            # Validate and add stop loss and take profit if specified
            if order.stop_loss and order.stop_loss > 0:
                validated_sl = self._validate_sl_tp_distance(order.stop_loss, current_price, order.order_type, "SL", timeframe_group)
                if validated_sl:
                    request["sl"] = validated_sl
                else:
                    logger.warning(f"Stop loss {order.stop_loss:.2f} too close to price {current_price:.2f}, removing SL")

            if order.take_profit and order.take_profit > 0:
                validated_tp = self._validate_sl_tp_distance(order.take_profit, current_price, order.order_type, "TP", timeframe_group)
                if validated_tp:
                    request["tp"] = validated_tp
                else:
                    logger.warning(f"Take profit {order.take_profit:.2f} too close to price {current_price:.2f}, removing TP")
            
            # Send order
            result = mt5.order_send(request)
            
            if result is None:
                logger.error("Order send failed - no result")
                return None
                
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f"Order failed: {result.retcode} - {result.comment}"

                # Specific handling for volume errors
                if result.retcode == 10014:  # Invalid volume
                    logger.error(f"{error_msg} | Volume used: {request.get('volume', 'unknown')}")
                    logger.error("Volume error details:")
                    symbol_info = mt5.symbol_info(self.symbol)
                    if symbol_info:
                        logger.error(f"  Symbol min volume: {symbol_info.volume_min}")
                        logger.error(f"  Symbol max volume: {symbol_info.volume_max}")
                        logger.error(f"  Symbol volume step: {symbol_info.volume_step}")
                    logger.error(f"  Request volume: {request.get('volume', 'unknown')}")
                elif result.retcode == 10016:  # Market closed
                    logger.error(f"{error_msg} | Market may be closed")
                elif result.retcode == 10018:  # Not enough money
                    logger.error(f"{error_msg} | Insufficient funds")
                else:
                    logger.error(error_msg)

                return None
                
            return result
            
        except Exception as e:
            logger.error(f"Error placing MT5 order: {e}")
            return None
            
    def _can_execute_order(self, signal: TradingSignal) -> bool:
        """Check if order can be executed based on risk limits."""
        # REMOVED: Daily trade limit checks - No limits on number of trades per day
        # REMOVED: Overall daily trade limit check
        # REMOVED: Timeframe-specific daily trade limit check

        # Check timeframe position limit (ONE TRADE PER TIMEFRAME) - KEPT
        timeframe = self._get_signal_timeframe(signal)
        if timeframe and self.timeframe_positions.get(timeframe) is not None:
            logger.warning(f"BLOCKED: Timeframe {timeframe} already has active position: {self.timeframe_positions[timeframe]}")
            return False

        # REMOVED: Old restriction that prevented ANY new trades if ANY positions existed
        # NEW BEHAVIOR: Each timeframe can have its own trade (up to 3 total: 1 per timeframe)

        # Check concurrent positions limit - KEPT
        if len(self.active_positions) >= self.max_concurrent_positions:
            logger.debug(f"Max concurrent positions reached: {len(self.active_positions)}")
            return False

        # Check daily drawdown limit
        max_daily_drawdown = config.SYNTHETIC_RISK_RULES["circuit_breakers"]["max_daily_drawdown"]
        if self.daily_pnl < -max_daily_drawdown:
            logger.warning(f"Daily drawdown limit reached: {self.daily_pnl:.3f}")
            return False

        # Check daily profit limit
        max_daily_profit = config.SYNTHETIC_RISK_RULES["circuit_breakers"]["max_daily_profit"]
        if self.daily_pnl > max_daily_profit:
            logger.warning(f"Daily profit limit reached: {self.daily_pnl:.3f}")
            return False

        # Check MT5 connection
        if not self.mt5_connected:
            logger.error("MT5 not connected")
            return False

        return True

    def _get_signal_timeframe(self, signal: TradingSignal) -> Optional[str]:
        """Determine which timeframe group this signal belongs to based on reasoning."""
        if not signal.reasoning:
            return None

        reasoning_lower = signal.reasoning.lower()

        # PRIORITY 1: Check for exact timeframe category mentions first (most reliable)
        if 'short_term' in reasoning_lower:
            return 'short_term'
        elif 'medium_term' in reasoning_lower:
            return 'medium_term'
        elif 'long_term' in reasoning_lower:
            return 'long_term'

        # PRIORITY 2: Check for specific model name patterns
        if any(term in reasoning_lower for term in ['short_term_pattern', 'short_term_momentum', 'short_term_reversion']):
            return 'short_term'
        elif any(term in reasoning_lower for term in ['medium_term_trend', 'medium_term_breakout', 'medium_term_volatility']):
            return 'medium_term'
        elif any(term in reasoning_lower for term in ['long_term_portfolio', 'long_term_regime', 'long_term_macro']):
            return 'long_term'

        # PRIORITY 3: Check for general keywords (less reliable)
        if any(term in reasoning_lower for term in ['scalping', 'pattern recognition', 'momentum detection']):
            return 'short_term'
        elif any(term in reasoning_lower for term in ['trend continuation', 'breakout detection', 'swing trading']):
            return 'medium_term'
        elif any(term in reasoning_lower for term in ['portfolio optimization', 'regime analysis', 'macro trends']):
            return 'long_term'

        # Default to short term if unclear
        return 'short_term'

    def _get_timeframe_from_comment(self, comment: str) -> Optional[str]:
        """Extract timeframe from trade comment (NEW COMMENT-BASED DETECTION)."""
        if not comment:
            return None

        comment_upper = comment.upper()

        # Check for timeframe-specific comment patterns (ONLY the timeframe part)
        if 'AI_BOT_SHORT' in comment_upper:
            return 'short_term'
        elif 'AI_BOT_MEDIUM' in comment_upper:
            return 'medium_term'
        elif 'AI_BOT_LONG' in comment_upper:
            return 'long_term'

        # No timeframe found in comment
        return None

    def _is_ai_bot_position(self, mt5_position) -> bool:
        """Check if position was created by AI bot (not manual trade)."""
        try:
            # Check if position has our magic number
            if hasattr(mt5_position, 'magic') and mt5_position.magic == self.magic_number:
                return True

            # Check if comment contains AI bot identifier
            if hasattr(mt5_position, 'comment'):
                comment = str(mt5_position.comment).upper()
                if 'AI_BOT' in comment or 'AI_SIGNAL' in comment:
                    return True

            # Check if position ID is in our active orders
            if hasattr(mt5_position, 'ticket') and mt5_position.ticket in self.active_orders:
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking if AI bot position: {e}")
            return False

    def _start_position_monitoring(self):
        """Start position monitoring thread."""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(target=self._monitor_positions, daemon=True)
            self.monitor_thread.start()
            logger.info("Position monitoring started")
            


    def _monitor_positions(self):
        """Monitor active AI bot positions and update status."""
        while self.monitoring_active:
            try:
                # Get current positions from MT5 (only for our symbol)
                positions = mt5.positions_get(symbol=self.symbol)

                if positions is not None:
                    current_ai_position_ids = set()

                    for pos in positions:
                        position_id = pos.ticket

                        # ONLY track AI bot positions (ignore manual trades)
                        if self._is_ai_bot_position(pos):
                            current_ai_position_ids.add(position_id)

                            # Update existing position or create new one
                            if position_id in self.active_positions:
                                self._update_position(position_id, pos)
                                # HANDLE POSITION SCALING
                                self._handle_position_scaling(self.active_positions[position_id])
                                # HANDLE TIME-BASED EXITS
                                self._handle_time_based_exits(self.active_positions[position_id])
                            else:
                                self._create_position_from_mt5(pos)

                    # Remove closed AI bot positions only
                    closed_positions = set(self.active_positions.keys()) - current_ai_position_ids
                    for pos_id in closed_positions:
                        self._handle_position_close(pos_id)
                        
                # Update daily P&L
                self._update_daily_pnl()
                
                # Sleep for monitoring interval
                time.sleep(1)  # Monitor every second
                
            except Exception as e:
                logger.error(f"Error in position monitoring: {e}")
                time.sleep(5)  # Wait longer on error
                
    def _update_position(self, position_id: int, mt5_position):
        """Update existing position with current MT5 data."""
        if position_id in self.active_positions:
            position = self.active_positions[position_id]
            position.price_current = mt5_position.price_current
            position.profit = mt5_position.profit
            position.swap = mt5_position.swap
            
    def _create_position_from_mt5(self, mt5_position):
        """Create position object from MT5 position data."""
        # This handles positions that might have been opened outside our system
        # or positions we missed during monitoring
        position_id = mt5_position.ticket
        
        # Create a dummy order for this position
        dummy_order = Order(
            order_id=position_id,
            signal=None,  # No signal for external positions
            order_type=OrderType.BUY if mt5_position.type == 0 else OrderType.SELL,
            volume=mt5_position.volume,
            price=mt5_position.price_open,
            stop_loss=mt5_position.sl,
            take_profit=mt5_position.tp,
            status=OrderStatus.FILLED,
            timestamp=datetime.fromtimestamp(mt5_position.time),
            fill_price=mt5_position.price_open,
            fill_time=datetime.fromtimestamp(mt5_position.time)
        )
        
        # Check if this position has scaling data
        scaling_data = self.scaling_positions.get(position_id, {})
        scaling_params = scaling_data.get('scaling_params', {})

        position = Position(
            position_id=position_id,
            symbol=mt5_position.symbol,
            volume=mt5_position.volume,
            price_open=mt5_position.price_open,
            price_current=mt5_position.price_current,
            stop_loss=mt5_position.sl,
            take_profit=mt5_position.tp,
            profit=mt5_position.profit,
            swap=mt5_position.swap,
            commission=0.0,  # Commission not available in position info
            time_open=datetime.fromtimestamp(mt5_position.time),
            order=dummy_order,
            # Scaling fields
            original_volume=scaling_data.get('original_volume', mt5_position.volume),
            remaining_volume=scaling_data.get('remaining_volume', 0.0),
            scale_in_levels=scaling_params.get('scale_in_levels'),
            scale_out_levels=scaling_params.get('scale_out_levels'),
            scaled_entries=[],
            scaled_exits=[],
            is_scaling_position=bool(scaling_data)
        )
        
        self.active_positions[position_id] = position

        # CRITICAL FIX: Register position in timeframe tracking system
        # Extract timeframe from comment to properly register the position
        comment = getattr(mt5_position, 'comment', '')
        timeframe = self._get_timeframe_from_comment(comment)

        if timeframe:
            # Check if timeframe already has a position registered
            if self.timeframe_positions.get(timeframe) is not None:
                existing_position_id = self.timeframe_positions[timeframe]
                logger.warning(f"CONFLICT: {timeframe} already has position {existing_position_id}, but found another position {position_id}")
                logger.warning(f"This indicates a bug in position tracking - investigating...")

            # Register this position for the timeframe
            self.timeframe_positions[timeframe] = position_id
            logger.info(f"Registered detected {timeframe} position: {position_id}")
        else:
            logger.warning(f"Could not determine timeframe for detected position {position_id} with comment: '{comment}'")

        logger.info(f"Detected external AI bot position: {position_id}")
        logger.info(f"Current timeframe positions: {self.timeframe_positions}")
        
    def _handle_position_close(self, position_id: int):
        """Handle position closure."""
        if position_id in self.active_positions:
            position = self.active_positions[position_id]

            # Update daily P&L
            self.daily_pnl += position.profit

            # Unregister from timeframe positions
            for timeframe, tf_position_id in self.timeframe_positions.items():
                if tf_position_id == position_id:
                    self.timeframe_positions[timeframe] = None
                    logger.info(f"Unregistered {timeframe} position: {position_id}")
                    break

            # Provide feedback to AI model manager
            if self.trading_engine and self.trading_engine.ai_manager and position.order.signal:
                for prediction in position.order.signal.ai_predictions.get('predictions', []):
                    self.trading_engine.ai_manager.track_prediction_accuracy(
                        model_name=prediction['model_name'],
                        predicted_signal=prediction['signal'],
                        actual_outcome=1 if position.profit > 0 else -1 if position.profit < 0 else 0,
                        time_horizon_minutes=int((datetime.now() - position.time_open).total_seconds() / 60)
                    )

            # Move to history
            if position.order:
                self.order_history.append(position.order)

            # Remove from active positions
            del self.active_positions[position_id]

            logger.info(f"Position closed: {position_id}, Profit: {position.profit:.2f}")
            
    def _update_daily_pnl(self):
        """Update daily P&L from active positions and MT5 history."""
        # Get P&L from currently active AI bot positions
        active_pnl = sum(pos.profit for pos in self.active_positions.values())

        # Get closed trades P&L from MT5 history for today
        closed_pnl = self._get_daily_closed_trades_pnl()

        # Total daily P&L
        self.daily_pnl = active_pnl + closed_pnl

        logger.debug(f"Daily P&L updated: Active={active_pnl:.2f}, Closed={closed_pnl:.2f}, Total={self.daily_pnl:.2f}")

    def _get_daily_closed_trades_pnl(self) -> float:
        """Get P&L from closed AI bot trades from fresh start time forward."""
        try:
            import MetaTrader5 as mt5
            from datetime import datetime, timedelta, timezone

            # Use fresh start time if set, otherwise use today's start (SAST-aligned)
            if self.fresh_start_time:
                start_time = self.fresh_start_time
                logger.debug(f"Using fresh start time for P&L calculation: {start_time}")
            else:
                # Fallback to today's date range using SAST-aligned time
                gmt_now = datetime.now(timezone.utc)
                # SAST is GMT+2, adjust by subtracting 2 hours to align with SAST day
                sast_aligned_time = gmt_now - timedelta(hours=2)
                today = sast_aligned_time.date()
                # Start time should be 02:00 GMT (00:00 SAST) for the current SAST day
                # This means we want the start of the SAST day, which is 02:00 GMT
                start_time = datetime.combine(today, datetime.min.time())
                start_time = start_time.replace(tzinfo=timezone.utc) + timedelta(hours=2)

            end_time = datetime.now(timezone.utc)

            # Get deals (closed trades) from MT5
            deals = mt5.history_deals_get(start_time, end_time, group="*")

            if deals is None:
                return 0.0

            total_pnl = 0.0
            ai_bot_trades = 0

            for deal in deals:
                # Only count AI bot trades (our magic number and symbol)
                if (hasattr(deal, 'magic') and deal.magic == self.magic_number and
                    hasattr(deal, 'symbol') and deal.symbol == self.symbol and
                    hasattr(deal, 'entry') and deal.entry == 1):  # Entry = 1 means exit (close) trade

                    total_pnl += deal.profit
                    ai_bot_trades += 1

            logger.debug(f"Found {ai_bot_trades} closed AI bot trades today with total P&L: {total_pnl:.2f}")
            return total_pnl

        except Exception as e:
            logger.warning(f"Error getting daily closed trades P&L: {e}")
            return 0.0

    def get_all_historical_trades_pnl(self) -> Dict:
        """Get ALL historical AI bot trades P&L and statistics."""
        try:
            import MetaTrader5 as mt5
            from datetime import datetime, timedelta

            # Get all historical data from when AI bot started (May 1st, 2025)
            start_time = datetime(2025, 5, 1)
            end_time = datetime.now()

            # Get deals (closed trades) from MT5
            deals = mt5.history_deals_get(start_time, end_time, group="*")

            if deals is None:
                return {"total_pnl": 0.0, "total_trades": 0, "winning_trades": 0, "losing_trades": 0, "win_rate": 0.0}

            total_pnl = 0.0
            ai_bot_trades = 0
            winning_trades = 0
            losing_trades = 0
            trade_details = []

            for deal in deals:
                # CURRENT AI SYSTEM FILTERING: Use unique magic number (54321)
                if (hasattr(deal, 'magic') and deal.magic == self.magic_number and
                    hasattr(deal, 'symbol') and deal.symbol == self.symbol and
                    hasattr(deal, 'entry') and deal.entry == 1):  # Entry = 1 means exit (close) trade

                    # With unique magic number 54321, all trades with this magic are from current AI system
                    comment = getattr(deal, 'comment', '')
                    is_ai_bot_trade = True  # All trades with magic 54321 are from current AI system

                    if is_ai_bot_trade:
                        profit = deal.profit
                        total_pnl += profit
                        ai_bot_trades += 1

                        if profit > 0:
                            winning_trades += 1
                        elif profit < 0:
                            losing_trades += 1

                        trade_details.append({
                            "time": deal.time,
                            "profit": profit,
                            "comment": comment
                        })

            win_rate = (winning_trades / ai_bot_trades * 100) if ai_bot_trades > 0 else 0.0

            logger.info(f"Historical AI bot trades: {ai_bot_trades}, Total P&L: {total_pnl:.2f}, Win Rate: {win_rate:.1f}%")

            return {
                "total_pnl": total_pnl,
                "total_trades": ai_bot_trades,
                "winning_trades": winning_trades,
                "losing_trades": losing_trades,
                "win_rate": win_rate,
                "trade_details": trade_details[-10:]  # Last 10 trades for display
            }

        except Exception as e:
            logger.warning(f"Error getting historical trades P&L: {e}")
            return {"total_pnl": 0.0, "total_trades": 0, "winning_trades": 0, "losing_trades": 0, "win_rate": 0.0}

    def get_historical_trades_by_timeframe(self) -> Dict:
        """Get historical AI bot trades grouped by timeframe using NEW comment-based detection."""
        try:
            import MetaTrader5 as mt5
            from datetime import datetime

            # Get all historical data from when AI bot started (May 1st, 2025)
            start_time = datetime(2025, 5, 1)
            end_time = datetime.now()

            # Get deals (closed trades) from MT5
            deals = mt5.history_deals_get(start_time, end_time, group="*")

            if deals is None:
                return {"short_term": [], "medium_term": [], "long_term": [], "unknown": []}

            # Initialize timeframe trade collections
            timeframe_trades = {
                "short_term": [],
                "medium_term": [],
                "long_term": [],
                "unknown": []  # For trades without timeframe info
            }

            for deal in deals:
                # CURRENT AI SYSTEM FILTERING: Use unique magic number (54321)
                if (hasattr(deal, 'magic') and deal.magic == self.magic_number and
                    hasattr(deal, 'symbol') and deal.symbol == self.symbol and
                    hasattr(deal, 'entry') and deal.entry == 1):  # Entry = 1 means exit (close) trade

                    comment = getattr(deal, 'comment', '')

                    # Use NEW comment-based timeframe detection
                    timeframe = self._get_timeframe_from_comment(comment)

                    trade_data = {
                        "time": deal.time,
                        "profit": deal.profit,
                        "comment": comment
                    }

                    if timeframe:
                        timeframe_trades[timeframe].append(trade_data)
                    else:
                        timeframe_trades["unknown"].append(trade_data)

            logger.info(f"Historical trades by timeframe: "
                       f"Short={len(timeframe_trades['short_term'])}, "
                       f"Medium={len(timeframe_trades['medium_term'])}, "
                       f"Long={len(timeframe_trades['long_term'])}, "
                       f"Unknown={len(timeframe_trades['unknown'])}")

            return timeframe_trades

        except Exception as e:
            logger.warning(f"Error getting historical trades by timeframe: {e}")
            return {"short_term": [], "medium_term": [], "long_term": [], "unknown": []}

    def _reset_daily_counters(self):
        """Reset daily and monthly counters if new day/month."""
        # Use GMT time for consistency with MT5
        from datetime import timezone, timedelta
        
        # Get current GMT time
        gmt_now = datetime.now(timezone.utc)
        
        # SAST is GMT+2, so daily reset should happen at 02:00 GMT (00:00 SAST)
        # Adjust GMT time by subtracting 2 hours to get SAST-aligned date
        sast_aligned_time = gmt_now - timedelta(hours=2)
        current_date = sast_aligned_time.date()
        current_month_start = current_date.replace(day=1)

        # Reset daily counters if new day (based on SAST-aligned date)
        if current_date != self.last_reset_date:
            self.daily_trade_count = 0
            self.daily_pnl = 0.0
            self.last_reset_date = current_date
            
            # Set fresh_start_time to the start of the current SAST day (02:00 GMT)
            # This ensures daily P&L only includes trades from 02:00 GMT (00:00 SAST) onwards
            start_of_sast_day = datetime.combine(current_date, datetime.min.time())
            self.fresh_start_time = start_of_sast_day.replace(tzinfo=timezone.utc) + timedelta(hours=2)

            # Reset daily timeframe counters
            self.timeframe_daily_trades = {
                'short_term': 0,
                'medium_term': 0,
                'long_term': 0
            }

            # Save reset counters to shared file
            self._save_shared_counters()

            # Reset trading engine emergency flags if available
            if self.trading_engine and hasattr(self.trading_engine, 'reset_daily_emergency_flags'):
                self.trading_engine.reset_daily_emergency_flags()

            logger.info("Daily trading counters reset")
            logger.info(f"Fresh start time set to: {self.fresh_start_time} (02:00 GMT / 00:00 SAST)")
            logger.info(f"Daily timeframe trades reset: {self.timeframe_daily_trades}")

        # Reset monthly counters if new month
        if current_month_start != self.last_monthly_reset:
            self.timeframe_monthly_trades = {
                'short_term': 0,
                'medium_term': 0,
                'long_term': 0
            }
            self.last_monthly_reset = current_month_start

            # Save reset counters to shared file
            self._save_shared_counters()

            logger.info("Monthly trading counters reset")
            logger.info(f"Monthly timeframe trades reset: {self.timeframe_monthly_trades}")

    def close_position(self, position_id: int, reason: str = "Manual") -> bool:
        """Close a specific AI bot position (will not close manual trades)."""
        try:
            if position_id not in self.active_positions:
                logger.warning(f"Position {position_id} not found in active AI bot positions")
                return False

            position = self.active_positions[position_id]

            # Get timeframe from position signal for close comment
            timeframe_group = 'UNKNOWN'
            if position.order and position.order.signal:
                timeframe_group = self._get_signal_timeframe(position.order.signal) or 'medium_term'
                timeframe_abbrev = {
                    'short_term': 'SHORT',
                    'medium_term': 'MEDIUM',
                    'long_term': 'LONG'
                }.get(timeframe_group, 'MEDIUM')
            else:
                timeframe_abbrev = 'UNKNOWN'

            # Prepare close request - simplified for synthetic indices
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": mt5.ORDER_TYPE_SELL if position.order.order_type == OrderType.BUY else mt5.ORDER_TYPE_BUY,
                "position": position_id,
                "deviation": self.deviation,
                "magic": self.magic_number,
                "comment": f"AI_BOT_{timeframe_abbrev}_Close_{reason}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK,
            }

            # Send close request
            result = mt5.order_send(request)

            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                logger.info(f"Position {position_id} closed successfully. Reason: {reason}")
                return True
            else:
                error_msg = f"Failed to close position {position_id}: {result.comment if result else 'No result'}"
                logger.error(error_msg)

                # If position close fails repeatedly, check if position still exists in MT5
                mt5_positions = mt5.positions_get(symbol=position.symbol)
                position_exists = any(pos.ticket == position_id for pos in mt5_positions) if mt5_positions else False

                if not position_exists:
                    # Position doesn't exist in MT5 anymore, remove from our tracking
                    logger.warning(f"Position {position_id} not found in MT5, removing from active positions")
                    self._remove_position_from_tracking(position_id)
                    return True

                return False

        except Exception as e:
            logger.error(f"Error closing position {position_id}: {e}")
            return False

    def _remove_position_from_tracking(self, position_id: int):
        """Remove position from all tracking systems."""
        try:
            # Remove from active positions
            if position_id in self.active_positions:
                position = self.active_positions[position_id]
                del self.active_positions[position_id]
                logger.info(f"Removed position {position_id} from active positions")

                # Remove from timeframe tracking
                timeframe_group = None
                if position.order and position.order.signal:
                    timeframe_group = self._get_signal_timeframe(position.order.signal)

                if timeframe_group and self.timeframe_positions.get(timeframe_group) == position_id:
                    del self.timeframe_positions[timeframe_group]
                    logger.info(f"Removed position {position_id} from {timeframe_group} timeframe tracking")

                # Remove from scaling tracking if exists
                if position_id in self.scaling_positions:
                    del self.scaling_positions[position_id]
                    logger.info(f"Removed position {position_id} from scaling tracking")

        except Exception as e:
            logger.error(f"Error removing position {position_id} from tracking: {e}")

    def close_all_positions(self, reason: str = "Emergency") -> int:
        """Close all active AI bot positions (will not close manual trades)."""
        closed_count = 0
        position_ids = list(self.active_positions.keys())

        for position_id in position_ids:
            if self.close_position(position_id, reason):
                closed_count += 1

        logger.info(f"Closed {closed_count}/{len(position_ids)} AI bot positions. Reason: {reason}")
        logger.info("Manual trades were NOT affected and remain open")
        logger.info("All timeframes now available for new trades")
        return closed_count

    def modify_position(self, position_id: int, new_sl: Optional[float] = None,
                       new_tp: Optional[float] = None) -> bool:
        """Modify stop loss and/or take profit of a position."""
        try:
            if position_id not in self.active_positions:
                logger.warning(f"Position {position_id} not found")
                return False

            position = self.active_positions[position_id]

            # Use current values if not specified
            sl = new_sl if new_sl is not None else position.stop_loss
            tp = new_tp if new_tp is not None else position.take_profit

            # Prepare modification request
            request = {
                "action": mt5.TRADE_ACTION_SLTP,
                "symbol": position.symbol,
                "position": position_id,
                "sl": sl,
                "tp": tp,
                "magic": self.magic_number,
                "comment": "Modify_SL_TP"
            }

            # Send modification request
            result = mt5.order_send(request)

            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                # Update local position data
                position.stop_loss = sl
                position.take_profit = tp
                logger.info(f"Position {position_id} modified: SL={sl}, TP={tp}")
                return True
            else:
                logger.error(f"Failed to modify position {position_id}: {result.comment if result else 'No result'}")
                return False

        except Exception as e:
            logger.error(f"Error modifying position {position_id}: {e}")
            return False

    # ==================== POSITION SCALING METHODS ====================

    def _should_use_position_scaling(self, signal: TradingSignal) -> bool:
        """Determine if position scaling should be used for this signal."""
        # Check if signal confidence is high enough
        if signal.confidence <= 0.7:
            return False

        # Check if volume is large enough for scaling
        full_volume = self._calculate_volume(signal)

        # Get symbol minimum volume
        try:
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                return False

            min_volume = symbol_info.volume_min

            # Only enable scaling if full volume is at least 2x minimum
            # This ensures 50% scaling still meets minimum requirements
            if full_volume < (min_volume * 2):
                logger.debug(f"Volume {full_volume:.3f} too small for scaling (min: {min_volume}, need: {min_volume * 2})")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking volume for scaling: {e}")
            return False

    def _calculate_scaling_parameters(self, signal: TradingSignal, full_volume: float) -> Optional[Dict]:
        """Calculate position scaling parameters."""
        try:
            # Get symbol minimum volume to ensure compliance
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                logger.error("Could not get symbol info for scaling")
                return None

            min_volume = symbol_info.volume_min

            # Get scaling configuration
            scale_out_levels = config.SYNTHETIC_RISK_RULES["profit_taking"]["scale_out_levels"]

            # Calculate initial entry volume (start with 50% of intended position)
            # But ensure it's never below minimum volume
            initial_volume = max(full_volume * 0.5, min_volume)
            remaining_volume = full_volume - initial_volume

            # Safety check: if initial volume equals full volume, disable scaling
            if initial_volume >= full_volume:
                logger.debug(f"Cannot scale: initial volume {initial_volume:.3f} >= full volume {full_volume:.3f}")
                return None

            # Calculate scale-in levels (if price moves favorably, add more)
            current_price = signal.entry_price
            fixed_distance = self._estimate_fixed_distance(signal)

            if signal.signal_type.value > 0:  # Buy signal
                # Scale in if price dips (better entry)
                scale_in_levels = [
                    current_price - (fixed_distance * 0.3),  # 30% of fixed distance below
                    current_price - (fixed_distance * 0.6),  # 60% of fixed distance below
                ]
                # Scale out at profit levels
                scale_out_prices = [
                    current_price + (fixed_distance * level * 2) for level in scale_out_levels
                ]
            else:  # Sell signal
                # Scale in if price rises (better entry)
                scale_in_levels = [
                    current_price + (fixed_distance * 0.3),  # 30% of fixed distance above
                    current_price + (fixed_distance * 0.6),  # 60% of fixed distance above
                ]
                # Scale out at profit levels
                scale_out_prices = [
                    current_price - (fixed_distance * level * 2) for level in scale_out_levels
                ]

            return {
                'initial_volume': initial_volume,
                'remaining_volume': remaining_volume,
                'scale_in_levels': scale_in_levels,
                'scale_out_levels': scale_out_prices,
                'scale_in_volumes': [remaining_volume * 0.3, remaining_volume * 0.7],  # 30%, 70% of remaining
                'scale_out_volumes': [initial_volume * level for level in scale_out_levels]
            }

        except Exception as e:
            logger.error(f"Error calculating scaling parameters: {e}")
            return None

    def _estimate_fixed_distance(self, signal: TradingSignal) -> float:
        """Estimate fixed distance for scaling calculations."""
        # Use fixed distances based on timeframe
        timeframe_group = self._get_signal_timeframe(signal) or 'medium_term'

        distance_map = {
            'short_term': 50.0,
            'medium_term': 100.0,
            'long_term': 200.0
        }

        return distance_map.get(timeframe_group, 100.0)

    def _handle_position_scaling(self, position: Position):
        """Handle position scaling logic for scale-in and scale-out."""
        try:
            if not position.is_scaling_position:
                return

            position_id = position.position_id
            current_price = position.price_current

            # Check for scale-out opportunities (take partial profits)
            if position.scale_out_levels and position.profit > 0:
                self._check_scale_out_levels(position, current_price)

            # Check for scale-in opportunities (add to position)
            if position.scale_in_levels and position.remaining_volume > 0:
                self._check_scale_in_levels(position, current_price)

        except Exception as e:
            logger.error(f"Error in position scaling for position {position.position_id}: {e}")

    def _check_scale_out_levels(self, position: Position, current_price: float):
        """Check if any scale-out levels have been hit."""
        try:
            if not position.scale_out_levels:
                return

            for i, target_price in enumerate(position.scale_out_levels):
                # Check if price has reached this scale-out level
                if position.order.order_type == OrderType.BUY:
                    price_hit = current_price >= target_price
                else:  # SELL
                    price_hit = current_price <= target_price

                if price_hit:
                    # Calculate volume to close (based on scale-out configuration)
                    scale_out_levels = config.SYNTHETIC_RISK_RULES["profit_taking"]["scale_out_levels"]
                    if i < len(scale_out_levels):
                        close_percentage = scale_out_levels[i]
                        close_volume = position.volume * close_percentage

                        # Execute partial close
                        if self._execute_partial_close(position, close_volume, f"Scale-out level {i+1}"):
                            logger.info(f"Scaled out {close_percentage*100:.0f}% of position {position.position_id} at {current_price:.2f}")

                            # Remove this level from future checks
                            position.scale_out_levels.pop(i)
                            break

        except Exception as e:
            logger.error(f"Error checking scale-out levels: {e}")

    def _check_scale_in_levels(self, position: Position, current_price: float):
        """Check if any scale-in levels have been hit."""
        try:
            if not position.scale_in_levels or position.remaining_volume <= 0:
                return

            for i, target_price in enumerate(position.scale_in_levels):
                # Check if price has reached this scale-in level (better entry)
                if position.order.order_type == OrderType.BUY:
                    price_hit = current_price <= target_price  # Buy lower
                else:  # SELL
                    price_hit = current_price >= target_price  # Sell higher

                if price_hit:
                    # Calculate volume to add
                    add_volume = min(position.remaining_volume * 0.5, position.remaining_volume)

                    # Execute scale-in (add to position)
                    if self._execute_scale_in(position, add_volume, f"Scale-in level {i+1}"):
                        logger.info(f"Scaled into position {position.position_id} with {add_volume:.2f} volume at {current_price:.2f}")

                        # Update remaining volume
                        position.remaining_volume -= add_volume

                        # Remove this level from future checks
                        position.scale_in_levels.pop(i)
                        break

        except Exception as e:
            logger.error(f"Error checking scale-in levels: {e}")

    def _execute_partial_close(self, position: Position, close_volume: float, reason: str) -> bool:
        """Execute partial position close."""
        try:
            # Prepare close request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": close_volume,
                "type": mt5.ORDER_TYPE_SELL if position.order.order_type == OrderType.BUY else mt5.ORDER_TYPE_BUY,
                "position": position.position_id,
                "deviation": self.deviation,
                "magic": self.magic_number,
                "comment": f"Partial_Close_{reason}"
            }

            # Send close order
            result = mt5.order_send(request)

            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                logger.info(f"Partial close successful: {close_volume:.2f} volume, reason: {reason}")
                return True
            else:
                logger.error(f"Partial close failed: {result.comment if result else 'No result'}")
                return False

        except Exception as e:
            logger.error(f"Error executing partial close: {e}")
            return False

    def _execute_scale_in(self, position: Position, add_volume: float, reason: str) -> bool:
        """Execute scale-in (add to existing position)."""
        try:
            # Get current price
            current_price = self._get_current_price()
            if current_price is None:
                return False

            # Prepare scale-in request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": add_volume,
                "type": position.order.order_type.value,
                "deviation": self.deviation,
                "magic": self.magic_number,
                "comment": f"Scale_In_{reason}"
            }

            # Send scale-in order
            result = mt5.order_send(request)

            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                logger.info(f"Scale-in successful: {add_volume:.2f} volume, reason: {reason}")
                return True
            else:
                logger.error(f"Scale-in failed: {result.comment if result else 'No result'}")
                return False

        except Exception as e:
            logger.error(f"Error executing scale-in: {e}")
            return False

    # ==================== TIME-BASED EXIT METHODS ====================

    def _handle_time_based_exits(self, position: Position):
        """Handle time-based exit logic for positions - DISABLED."""
        # TIME-BASED PROFIT TAKING FUNCTIONALITY REMOVED
        # This function is now disabled as requested by user
        pass

    def _check_timeframe_time_exits(self, position: Position, position_age_seconds: float):
        """Check timeframe-specific time-based exits - DISABLED."""
        # TIMEFRAME-SPECIFIC TIME-BASED EXIT FUNCTIONALITY REMOVED
        # This function is now disabled as requested by user
        pass

    def get_execution_statistics(self) -> Dict:
        """Get execution performance statistics."""
        stats = {
            "total_orders": len(self.order_history) + len(self.active_orders),
            "successful_fills": self.fill_rates["successful"],
            "failed_fills": self.fill_rates["failed"],
            "fill_rate": 0.0,
            "avg_execution_time_ms": 0.0,
            "avg_slippage": 0.0,
            "daily_trade_count": self.daily_trade_count,
            "daily_pnl": self.daily_pnl,
            "active_positions": len(self.active_positions),
            "mt5_connected": self.mt5_connected
        }

        # Calculate fill rate
        total_attempts = self.fill_rates["successful"] + self.fill_rates["failed"]
        if total_attempts > 0:
            stats["fill_rate"] = self.fill_rates["successful"] / total_attempts

        # Calculate average execution time
        if self.execution_times:
            stats["avg_execution_time_ms"] = sum(self.execution_times) / len(self.execution_times)
            stats["max_execution_time_ms"] = max(self.execution_times)
            stats["min_execution_time_ms"] = min(self.execution_times)

        # Calculate average slippage
        if self.slippage_data:
            stats["avg_slippage"] = sum(self.slippage_data) / len(self.slippage_data)
            stats["max_slippage"] = max(self.slippage_data)

        return stats

    def _get_position_timeframe(self, position) -> str:
        """Get timeframe for a position."""
        try:
            # First try to get from signal
            if position.order and position.order.signal:
                timeframe = self._get_signal_timeframe(position.order.signal)
                if timeframe:
                    return timeframe.replace('_', ' ').title()

            # Try to get from comment if available
            comment = self._get_position_comment(position)
            if comment:
                timeframe = self._get_timeframe_from_comment(comment)
                if timeframe:
                    return timeframe.replace('_', ' ').title()

            # Check timeframe positions tracking
            for tf, pos_id in self.timeframe_positions.items():
                if pos_id == position.position_id:
                    return tf.replace('_', ' ').title()

            return "Unknown"
        except Exception as e:
            logger.warning(f"Error getting position timeframe: {e}")
            return "Unknown"

    def _get_position_model_info(self, position) -> str:
        """Get model information for a position."""
        try:
            if position.order and position.order.signal:
                signal = position.order.signal

                # Get model info from signal reasoning
                if hasattr(signal, 'reasoning') and signal.reasoning:
                    reasoning = signal.reasoning.lower()

                    # Extract model names from reasoning
                    model_names = []
                    for model_type in ['lstm', 'cnn', 'transformer', 'ensemble', 'gradient_boost', 'random_forest', 'svm', 'neural_net', 'xgboost']:
                        if model_type in reasoning:
                            model_names.append(model_type.upper())

                    if model_names:
                        return f"Models: {', '.join(model_names[:3])}"  # Show first 3 models

                # Fallback to signal type
                return f"Signal: {signal.signal_type.name}"

            return "External Position"
        except Exception as e:
            logger.warning(f"Error getting position model info: {e}")
            return "Unknown"

    def _get_position_comment(self, position) -> str:
        """Get comment for a position."""
        try:
            # Try to get from MT5 position data if available
            import MetaTrader5 as mt5
            mt5_positions = mt5.positions_get(symbol=self.symbol)

            if mt5_positions:
                for mt5_pos in mt5_positions:
                    if mt5_pos.ticket == position.position_id:
                        return getattr(mt5_pos, 'comment', '')

            # Fallback to generating comment from order info
            if position.order and position.order.signal:
                timeframe = self._get_signal_timeframe(position.order.signal) or 'medium_term'
                timeframe_abbrev = {
                    'short_term': 'SHORT',
                    'medium_term': 'MEDIUM',
                    'long_term': 'LONG'
                }.get(timeframe, 'MEDIUM')

                return f"AI_BOT_{timeframe_abbrev}_{position.order.signal.signal_type.name}"

            return "AI_BOT_UNKNOWN"
        except Exception as e:
            logger.warning(f"Error getting position comment: {e}")
            return "Unknown"

    def get_position_summary(self) -> Dict:
        """Get summary of current positions."""
        if not self.active_positions:
            return {"total_positions": 0, "total_pnl": 0.0}

        total_pnl = sum(pos.profit for pos in self.active_positions.values())
        total_volume = sum(pos.volume for pos in self.active_positions.values())

        # Group by direction
        long_positions = [pos for pos in self.active_positions.values()
                         if pos.order.order_type == OrderType.BUY]
        short_positions = [pos for pos in self.active_positions.values()
                          if pos.order.order_type == OrderType.SELL]

        return {
            "total_positions": len(self.active_positions),
            "total_pnl": total_pnl,
            "total_volume": total_volume,
            "long_positions": len(long_positions),
            "short_positions": len(short_positions),
            "long_pnl": sum(pos.profit for pos in long_positions),
            "short_pnl": sum(pos.profit for pos in short_positions),
            "positions": [
                {
                    "id": pos.position_id,
                    "symbol": pos.symbol,
                    "volume": pos.volume,
                    "price_open": pos.price_open,
                    "price_current": pos.price_current,
                    "profit": pos.profit,
                    "direction": "LONG" if pos.order.order_type == OrderType.BUY else "SHORT",
                    "time_open": pos.time_open.strftime("%Y-%m-%d %H:%M:%S"),
                    "timeframe": self._get_position_timeframe(pos),
                    "signal_type": pos.order.signal.signal_type.name if pos.order and pos.order.signal else "UNKNOWN",
                    "model_info": self._get_position_model_info(pos),
                    "comment": self._get_position_comment(pos)
                }
                for pos in self.active_positions.values()
            ]
        }

    def get_closed_positions(self) -> List[Position]:
        """Get a list of positions that have been closed since the last check."""
        # This is a simplified implementation. In a real system, you'd likely
        # query the trade history from the broker.
        closed_positions = []
        for order_id, order in list(self.active_orders.items()):
            if order.status != OrderStatus.FILLED:
                continue
            if order_id not in self.active_positions:
                closed_positions.append(self.order_history[-1])
        return closed_positions

    def emergency_stop(self) -> bool:
        """Emergency stop - close all positions and disable trading."""
        logger.warning("EMERGENCY STOP ACTIVATED")

        # Close all positions
        closed_count = self.close_all_positions("EMERGENCY_STOP")

        # Disable monitoring
        self.monitoring_active = False

        # Log emergency stop
        logger.critical(f"Emergency stop completed. Closed {closed_count} positions.")

        return closed_count > 0 or len(self.active_positions) == 0

    def cleanup(self):
        """Clean up resources and connections."""
        logger.info("Cleaning up Order Execution System...")

        # Stop monitoring
        self.monitoring_active = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)

        # Close MT5 connection
        if self.mt5_connected:
            mt5.shutdown()
            self.mt5_connected = False

        logger.info("Order Execution System cleanup completed")
