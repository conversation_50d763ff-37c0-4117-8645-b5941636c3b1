#!/usr/bin/env python3
"""
Cross-Model Ensemble Synergy System
Advanced coordination layer for AI trading models that enhances the existing timeframe-independent system.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SynergyAnalysis:
    """Results of cross-model synergy analysis."""
    agreement_score: float
    confidence_adjustment: float
    position_multiplier: float
    risk_adjustment: float
    synergy_insights: List[str]
    recommended_action: str

class CrossModelSynergy:
    """
    Cross-Model Ensemble Synergy System
    
    Enhances the existing timeframe-independent trading system by adding intelligent
    coordination between timeframes while preserving the working ensemble logic.
    """
    
    def __init__(self):
        """Initialize the synergy system."""
        self.logger = logging.getLogger(__name__)
        
        # Synergy configuration
        self.config = {
            "agreement_boost_threshold": 0.7,  # When to boost confidence
            "conflict_penalty_threshold": 0.3,  # When to reduce confidence
            "max_position_multiplier": 1.5,    # Maximum position size boost
            "min_position_multiplier": 0.5,    # Minimum position size reduction
            "synergy_memory_periods": 50       # Historical periods to analyze
        }
        
        # Track historical synergy performance
        self.synergy_history = []
        self.performance_tracker = {
            "agreement_trades": {"wins": 0, "losses": 0, "total_pnl": 0.0},
            "conflict_trades": {"wins": 0, "losses": 0, "total_pnl": 0.0},
            "neutral_trades": {"wins": 0, "losses": 0, "total_pnl": 0.0}
        }
    
    def analyze_cross_timeframe_synergy(self, predictions: Dict, market_data: pd.DataFrame) -> SynergyAnalysis:
        """
        Analyze cross-timeframe synergy while preserving existing ensemble logic.
        
        Args:
            predictions: Model predictions from all timeframes
            market_data: Recent market data for context
            
        Returns:
            SynergyAnalysis with enhancement recommendations
        """
        try:
            # Group predictions by timeframe
            timeframe_groups = self._group_predictions_by_timeframe(predictions)
            
            # Calculate cross-timeframe agreement
            agreement_score = self._calculate_agreement_score(timeframe_groups)
            
            # Analyze market context for synergy insights
            market_context = self._analyze_market_context(market_data)
            
            # Calculate synergy adjustments
            confidence_adjustment = self._calculate_confidence_adjustment(agreement_score, market_context)
            position_multiplier = self._calculate_position_multiplier(agreement_score, timeframe_groups)
            risk_adjustment = self._calculate_risk_adjustment(agreement_score, market_context)
            
            # Generate synergy insights
            synergy_insights = self._generate_synergy_insights(timeframe_groups, agreement_score, market_context)
            
            # Recommend action based on synergy analysis
            recommended_action = self._recommend_action(agreement_score, timeframe_groups, market_context)
            
            # Create synergy analysis result
            synergy_analysis = SynergyAnalysis(
                agreement_score=agreement_score,
                confidence_adjustment=confidence_adjustment,
                position_multiplier=position_multiplier,
                risk_adjustment=risk_adjustment,
                synergy_insights=synergy_insights,
                recommended_action=recommended_action
            )
            
            # Track for performance analysis
            self._track_synergy_analysis(synergy_analysis, predictions)
            
            return synergy_analysis
            
        except Exception as e:
            self.logger.error(f"Error in synergy analysis: {e}")
            return self._get_neutral_synergy_analysis()
    
    def _group_predictions_by_timeframe(self, predictions: Dict) -> Dict[str, List[Dict]]:
        """Group model predictions by timeframe category."""
        timeframe_groups = {
            "short_term": [],
            "medium_term": [],
            "long_term": []
        }
        
        for model_name, prediction in predictions.items():
            if "short_term" in model_name:
                timeframe_groups["short_term"].append({
                    "model": model_name,
                    "signal": prediction.get("signal", 0),
                    "confidence": prediction.get("confidence", 0.0)
                })
            elif "medium_term" in model_name:
                timeframe_groups["medium_term"].append({
                    "model": model_name,
                    "signal": prediction.get("signal", 0),
                    "confidence": prediction.get("confidence", 0.0)
                })
            elif "long_term" in model_name:
                timeframe_groups["long_term"].append({
                    "model": model_name,
                    "signal": prediction.get("signal", 0),
                    "confidence": prediction.get("confidence", 0.0)
                })
        
        return timeframe_groups
    
    def _calculate_agreement_score(self, timeframe_groups: Dict[str, List[Dict]]) -> float:
        """Calculate cross-timeframe agreement score (0.0 to 1.0)."""
        try:
            # Get dominant signal for each timeframe
            timeframe_signals = {}
            
            for timeframe, models in timeframe_groups.items():
                if not models:
                    continue
                    
                # Calculate weighted average signal for this timeframe
                total_weight = sum(model["confidence"] for model in models)
                if total_weight > 0:
                    weighted_signal = sum(model["signal"] * model["confidence"] for model in models) / total_weight
                    timeframe_signals[timeframe] = weighted_signal
            
            if len(timeframe_signals) < 2:
                return 0.5  # Neutral if insufficient data
            
            # Calculate agreement between timeframes
            signals = list(timeframe_signals.values())
            
            # Agreement based on signal direction consistency
            positive_signals = sum(1 for s in signals if s > 0.5)
            negative_signals = sum(1 for s in signals if s < -0.5)
            neutral_signals = len(signals) - positive_signals - negative_signals
            
            # High agreement if most timeframes agree on direction
            max_agreement = max(positive_signals, negative_signals, neutral_signals)
            agreement_score = max_agreement / len(signals)
            
            return agreement_score
            
        except Exception as e:
            self.logger.warning(f"Error calculating agreement score: {e}")
            return 0.5
    
    def _analyze_market_context(self, market_data: pd.DataFrame) -> Dict:
        """Analyze current market context for synergy insights."""
        try:
            if market_data.empty or len(market_data) < 20:
                return {"regime": "unknown", "volatility": "normal", "trend": "neutral"}
            
            # Volatility analysis
            returns = market_data['close'].pct_change().fillna(0)
            recent_vol = returns.rolling(20).std().iloc[-1]
            long_vol = returns.rolling(50).std().iloc[-1] if len(market_data) >= 50 else recent_vol
            
            vol_ratio = recent_vol / long_vol if long_vol > 0 else 1.0
            
            if vol_ratio > 1.3:
                volatility = "high"
            elif vol_ratio < 0.8:
                volatility = "low"
            else:
                volatility = "normal"
            
            # Trend analysis
            sma_short = market_data['close'].rolling(10).mean().iloc[-1]
            sma_long = market_data['close'].rolling(20).mean().iloc[-1]
            current_price = market_data['close'].iloc[-1]
            
            if current_price > sma_short > sma_long:
                trend = "bullish"
            elif current_price < sma_short < sma_long:
                trend = "bearish"
            else:
                trend = "neutral"
            
            # Market regime (simplified)
            if volatility == "high":
                regime = "volatile"
            elif trend != "neutral":
                regime = "trending"
            else:
                regime = "ranging"
            
            return {
                "regime": regime,
                "volatility": volatility,
                "trend": trend,
                "vol_ratio": vol_ratio
            }
            
        except Exception as e:
            self.logger.warning(f"Error analyzing market context: {e}")
            return {"regime": "unknown", "volatility": "normal", "trend": "neutral"}
    
    def _calculate_confidence_adjustment(self, agreement_score: float, market_context: Dict) -> float:
        """Calculate confidence adjustment based on synergy analysis."""
        try:
            base_adjustment = 0.0
            
            # Agreement-based adjustment
            if agreement_score >= self.config["agreement_boost_threshold"]:
                base_adjustment += 0.1  # Boost confidence when models agree
            elif agreement_score <= self.config["conflict_penalty_threshold"]:
                base_adjustment -= 0.05  # Reduce confidence when models conflict
            
            # Market context adjustments
            if market_context["volatility"] == "high":
                base_adjustment -= 0.02  # Be more cautious in high volatility
            elif market_context["volatility"] == "low":
                base_adjustment += 0.02  # Be more confident in low volatility
            
            # Ensure reasonable bounds
            return max(-0.15, min(0.15, base_adjustment))
            
        except Exception as e:
            self.logger.warning(f"Error calculating confidence adjustment: {e}")
            return 0.0
    
    def _calculate_position_multiplier(self, agreement_score: float, timeframe_groups: Dict) -> float:
        """Calculate position size multiplier based on cross-timeframe synergy."""
        try:
            base_multiplier = 1.0
            
            # Strong agreement = larger positions
            if agreement_score >= 0.8:
                base_multiplier = 1.3
            elif agreement_score >= 0.6:
                base_multiplier = 1.1
            elif agreement_score <= 0.3:
                base_multiplier = 0.7
            
            # Check for strong signals across multiple timeframes
            strong_signal_count = 0
            for timeframe, models in timeframe_groups.items():
                for model in models:
                    if abs(model["signal"]) >= 2 and model["confidence"] >= 0.6:
                        strong_signal_count += 1
            
            # Boost for multiple strong signals
            if strong_signal_count >= 2:
                base_multiplier *= 1.2
            
            # Ensure bounds
            return max(self.config["min_position_multiplier"], 
                      min(self.config["max_position_multiplier"], base_multiplier))
            
        except Exception as e:
            self.logger.warning(f"Error calculating position multiplier: {e}")
            return 1.0
    
    def _calculate_risk_adjustment(self, agreement_score: float, market_context: Dict) -> float:
        """Calculate risk adjustment (stop loss modification)."""
        try:
            base_adjustment = 0.0
            
            # Tighter stops when models disagree
            if agreement_score <= 0.4:
                base_adjustment = -0.1  # 10% tighter stops
            elif agreement_score >= 0.8:
                base_adjustment = 0.05   # 5% wider stops (more confidence)
            
            # Market context adjustments
            if market_context["volatility"] == "high":
                base_adjustment += 0.05  # Wider stops in high volatility
            
            return max(-0.2, min(0.1, base_adjustment))
            
        except Exception as e:
            self.logger.warning(f"Error calculating risk adjustment: {e}")
            return 0.0

    def _generate_synergy_insights(self, timeframe_groups: Dict, agreement_score: float, market_context: Dict) -> List[str]:
        """Generate human-readable synergy insights."""
        insights = []

        try:
            # Agreement insights
            if agreement_score >= 0.8:
                insights.append("Strong cross-timeframe agreement - high confidence setup")
            elif agreement_score <= 0.3:
                insights.append("Timeframe conflict detected - proceed with caution")
            else:
                insights.append("Moderate timeframe consensus - standard approach")

            # Market context insights
            if market_context["volatility"] == "high":
                insights.append("High volatility environment - wider stops recommended")
            elif market_context["volatility"] == "low":
                insights.append("Low volatility environment - tighter stops possible")

            # Trend insights
            if market_context["trend"] != "neutral":
                insights.append(f"Market trend: {market_context['trend']} - align with trend bias")

            # Specific timeframe insights
            for timeframe, models in timeframe_groups.items():
                if models:
                    strong_models = [m for m in models if abs(m["signal"]) >= 2 and m["confidence"] >= 0.6]
                    if strong_models:
                        insights.append(f"{timeframe.title()} timeframe shows strong signals ({len(strong_models)} models)")

            return insights

        except Exception as e:
            self.logger.warning(f"Error generating synergy insights: {e}")
            return ["Synergy analysis unavailable"]

    def _recommend_action(self, agreement_score: float, timeframe_groups: Dict, market_context: Dict) -> str:
        """Recommend action based on synergy analysis."""
        try:
            # Count strong signals across timeframes
            total_strong_signals = 0
            bullish_signals = 0
            bearish_signals = 0

            for timeframe, models in timeframe_groups.items():
                for model in models:
                    if abs(model["signal"]) >= 2 and model["confidence"] >= 0.6:
                        total_strong_signals += 1
                        if model["signal"] > 0:
                            bullish_signals += 1
                        else:
                            bearish_signals += 1

            # High agreement scenarios
            if agreement_score >= 0.8:
                if total_strong_signals >= 2:
                    return "AGGRESSIVE_TRADE"  # Multiple strong signals + agreement
                else:
                    return "ENHANCED_TRADE"   # Good agreement, normal execution

            # Conflict scenarios
            elif agreement_score <= 0.3:
                if total_strong_signals >= 1:
                    return "CAUTIOUS_TRADE"   # Strong signal but conflict
                else:
                    return "AVOID_TRADE"      # Conflict + weak signals

            # Neutral scenarios
            else:
                if total_strong_signals >= 1:
                    return "STANDARD_TRADE"   # Normal execution
                else:
                    return "WAIT"             # No strong signals

        except Exception as e:
            self.logger.warning(f"Error recommending action: {e}")
            return "STANDARD_TRADE"

    def _track_synergy_analysis(self, synergy_analysis: SynergyAnalysis, predictions: Dict):
        """Track synergy analysis for performance monitoring."""
        try:
            analysis_record = {
                "timestamp": datetime.now(),
                "agreement_score": synergy_analysis.agreement_score,
                "confidence_adjustment": synergy_analysis.confidence_adjustment,
                "position_multiplier": synergy_analysis.position_multiplier,
                "recommended_action": synergy_analysis.recommended_action,
                "total_models": len(predictions)
            }

            self.synergy_history.append(analysis_record)

            # Keep only recent history
            if len(self.synergy_history) > self.config["synergy_memory_periods"]:
                self.synergy_history = self.synergy_history[-self.config["synergy_memory_periods"]:]

        except Exception as e:
            self.logger.warning(f"Error tracking synergy analysis: {e}")

    def _get_neutral_synergy_analysis(self) -> SynergyAnalysis:
        """Return neutral synergy analysis in case of errors."""
        return SynergyAnalysis(
            agreement_score=0.5,
            confidence_adjustment=0.0,
            position_multiplier=1.0,
            risk_adjustment=0.0,
            synergy_insights=["Synergy analysis unavailable - using standard approach"],
            recommended_action="STANDARD_TRADE"
        )

    def get_synergy_performance_stats(self) -> Dict:
        """Get performance statistics for synergy system."""
        try:
            if not self.synergy_history:
                return {"status": "insufficient_data"}

            recent_analyses = self.synergy_history[-20:] if len(self.synergy_history) >= 20 else self.synergy_history

            avg_agreement = np.mean([a["agreement_score"] for a in recent_analyses])
            avg_position_multiplier = np.mean([a["position_multiplier"] for a in recent_analyses])

            action_distribution = {}
            for analysis in recent_analyses:
                action = analysis["recommended_action"]
                action_distribution[action] = action_distribution.get(action, 0) + 1

            return {
                "status": "active",
                "total_analyses": len(self.synergy_history),
                "recent_avg_agreement": avg_agreement,
                "recent_avg_position_multiplier": avg_position_multiplier,
                "action_distribution": action_distribution,
                "performance_tracker": self.performance_tracker
            }

        except Exception as e:
            self.logger.warning(f"Error getting synergy performance stats: {e}")
            return {"status": "error", "error": str(e)}

    def update_trade_result(self, synergy_action: str, trade_result: Dict):
        """Update performance tracking with trade results."""
        try:
            if synergy_action not in ["AGGRESSIVE_TRADE", "ENHANCED_TRADE", "CAUTIOUS_TRADE", "STANDARD_TRADE"]:
                return

            pnl = trade_result.get("pnl", 0.0)
            is_win = pnl > 0

            # Categorize trade based on synergy action
            if synergy_action in ["AGGRESSIVE_TRADE", "ENHANCED_TRADE"]:
                category = "agreement_trades"
            elif synergy_action == "CAUTIOUS_TRADE":
                category = "conflict_trades"
            else:
                category = "neutral_trades"

            # Update performance tracker
            if is_win:
                self.performance_tracker[category]["wins"] += 1
            else:
                self.performance_tracker[category]["losses"] += 1

            self.performance_tracker[category]["total_pnl"] += pnl

        except Exception as e:
            self.logger.warning(f"Error updating trade result: {e}")
