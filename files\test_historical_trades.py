#!/usr/bin/env python3
"""
Test script to verify historical trades are being picked up correctly.
"""

import sys
sys.path.append('.')
from order_execution_system import OrderExecutionSystem
import config

def test_historical_trades():
    """Test the historical trades functionality."""
    print("🔍 Testing Historical Trades Functionality...")
    
    # Create order executor (pass None for data_collector since we're just testing historical trades)
    executor = OrderExecutionSystem(None)
    
    # Test historical trades
    historical_stats = executor.get_all_historical_trades_pnl()
    
    print("\n📊 Historical AI Bot Trades (UNIQUE MAGIC NUMBER 54321):")
    print(f"Total Trades: {historical_stats['total_trades']}")
    print(f"Total P&L: ${historical_stats['total_pnl']:.2f}")
    print(f"Winning Trades: {historical_stats['winning_trades']}")
    print(f"Losing Trades: {historical_stats['losing_trades']}")
    print(f"Win Rate: {historical_stats['win_rate']:.1f}%")

    # Show some recent trade details
    if 'trade_details' in historical_stats and historical_stats['trade_details']:
        print("\n📋 Recent Trade Details:")
        for trade in historical_stats['trade_details'][-5:]:  # Last 5 trades
            time_str = trade['time']
            if isinstance(trade['time'], (int, float)):
                from datetime import datetime
                time_str = datetime.fromtimestamp(trade['time']).strftime('%Y-%m-%d %H:%M:%S')
            print(f"  💰 {trade['profit']:+.2f} | {time_str} | {trade['comment']}")

    if historical_stats['total_trades'] > 0:
        print("\n✅ SUCCESS: AI Bot trades found (manual trades excluded)!")
        print("Dashboard should now show ONLY AI bot trades.")
    else:
        print("\n❌ ISSUE: No AI bot trades found.")
        print("Check if MT5 is connected and has AI bot trade history.")

if __name__ == "__main__":
    test_historical_trades()
