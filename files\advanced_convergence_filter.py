#!/usr/bin/env python3
"""
Advanced Convergence Filter for AI Trading System
Based on DeepSeek analysis and optimized for DEX 900 DOWN Index

This enhanced filter incorporates:
- Optimal 0.35% EMA/SMA threshold (DeepSeek validated)
- ADX trend strength confirmation
- Position size scaling instead of complete blocking
- Model-specific regime responses
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Tuple, Optional, Any
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedConvergenceFilter:
    """
    Advanced EMA20/SMA50 convergence detector with regime switching
    for Deriv's DEX 900 DOWN synthetic index.
    
    Based on DeepSeek analysis showing:
    - 82% of large losses occurred when EMA/SMA gap was <0.3%
    - Optimal threshold: 0.35% (58% win rate, 0.81% avg profit)
    - ADX < 20 indicates weak trend (danger zone)
    """
    
    def __init__(self, 
                 gap_threshold: float = 0.0035,  # 0.35% optimal threshold
                 adx_threshold: int = 20,
                 atr_multiplier: float = 1.2,
                 enabled: bool = True):
        """
        Initialize the advanced convergence filter.
        
        Args:
            gap_threshold: Minimum percentage gap between EMA20/SMA50 (0.35% optimal)
            adx_threshold: ADX value below which market is considered non-trending
            atr_multiplier: ATR multiplier for volatility confirmation
            enabled: Whether the filter is active
        """
        self.gap_threshold = gap_threshold
        self.adx_threshold = adx_threshold
        self.atr_multiplier = atr_multiplier
        self.enabled = enabled
        self.hysteresis_band = 0.001  # 0.1% buffer to prevent rapid toggling
        
        # State tracking
        self.last_gap = None
        self.current_regime = None
        self.blocked_signals_count = 0
        self.total_checks_count = 0
        self.position_reductions_count = 0
        
        logger.info(f"🛡️ Advanced Convergence Filter initialized")
        logger.info(f"   Gap threshold: {self.gap_threshold*100:.2f}% (DeepSeek optimized)")
        logger.info(f"   ADX threshold: {self.adx_threshold}")
        logger.info(f"   Filter enabled: {self.enabled}")
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate required technical indicators from OHLC data.
        
        Args:
            df: DataFrame with OHLC data (minimum 50 periods)
            
        Returns:
            Dictionary with calculated indicators
        """
        if len(df) < 50:
            logger.warning("Insufficient data for technical indicators (need 50+ periods)")
            return {}
        
        try:
            # EMA20 and SMA50 (existing calculations)
            ema20 = df['close'].ewm(span=20, adjust=False).mean().iloc[-1]
            sma50 = df['close'].rolling(window=50).mean().iloc[-1]
            
            # ADX calculation (simplified)
            adx = self._calculate_adx(df)
            
            # ATR calculations
            atr_14 = self._calculate_atr(df, window=14)
            atr_30 = self._calculate_atr(df, window=30)
            
            return {
                'ema20': ema20,
                'sma50': sma50,
                'adx': adx,
                'atr_14': atr_14,
                'atr_30': atr_30,
                'gap_pct': abs(ema20 - sma50) / sma50 * 100,
                'ema_above_sma': ema20 > sma50
            }
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
            return {}
    
    def _calculate_atr(self, df: pd.DataFrame, window: int = 14) -> float:
        """Calculate Average True Range."""
        try:
            high = df['high'].values
            low = df['low'].values
            close = df['close'].values
            
            # True Range calculation
            tr1 = high[1:] - low[1:]
            tr2 = np.abs(high[1:] - close[:-1])
            tr3 = np.abs(low[1:] - close[:-1])
            
            true_range = np.maximum(tr1, np.maximum(tr2, tr3))
            
            # Average True Range
            atr = np.mean(true_range[-window:]) if len(true_range) >= window else 0.0
            return atr
            
        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return 0.0
    
    def _calculate_adx(self, df: pd.DataFrame, window: int = 14) -> float:
        """Calculate ADX (Average Directional Index)."""
        try:
            high = df['high'].values
            low = df['low'].values
            close = df['close'].values
            
            # Directional Movement calculation
            up_move = high[1:] - high[:-1]
            down_move = low[:-1] - low[1:]
            
            # Positive and Negative Directional Movement
            dm_pos = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
            dm_neg = np.where((down_move > up_move) & (down_move > 0), down_move, 0)
            
            # True Range for DI calculation
            atr = self._calculate_atr(df, window)
            
            if atr == 0:
                return 0.0
            
            # Directional Indicators
            di_pos = 100 * (np.mean(dm_pos[-window:]) / atr)
            di_neg = 100 * (np.mean(dm_neg[-window:]) / atr)
            
            # ADX calculation
            dx = abs(di_pos - di_neg) / (di_pos + di_neg) * 100 if (di_pos + di_neg) != 0 else 0
            return dx
            
        except Exception as e:
            logger.error(f"Error calculating ADX: {e}")
            return 0.0
    
    def assess_market_regime(self, indicators: Dict[str, float]) -> Dict[str, Any]:
        """
        Determine market regime and trading permissions based on DeepSeek analysis.
        
        Args:
            indicators: Dictionary with technical indicators
            
        Returns:
            Dictionary with regime assessment and trading rules
        """
        self.total_checks_count += 1
        
        if not indicators:
            return self._create_regime_response(False, "No indicators available", 0.0, "unknown")
        
        gap_pct = indicators['gap_pct']
        adx = indicators['adx']
        atr_14 = indicators['atr_14']
        atr_30 = indicators['atr_30']
        
        # Regime detection with hysteresis (prevents rapid switching)
        if self.last_gap is not None:
            if gap_pct > (self.gap_threshold * 100 + self.hysteresis_band * 100):
                new_regime = 'trending'
            elif gap_pct < (self.gap_threshold * 100 - self.hysteresis_band * 100):
                new_regime = 'converged'
            else:
                new_regime = self.current_regime or 'trending'
        else:
            new_regime = 'trending' if gap_pct > (self.gap_threshold * 100) else 'converged'
        
        self.current_regime = new_regime
        self.last_gap = gap_pct
        
        # Apply DeepSeek's regime-based rules
        if new_regime == 'trending':
            # Clear trend - normal trading
            return self._create_regime_response(
                can_trade=True,
                reason=f"Trending regime: gap {gap_pct:.3f}% > {self.gap_threshold*100:.2f}%",
                gap_pct=gap_pct,
                regime='trending',
                position_multiplier=1.0,
                stop_multiplier=1.0,
                indicators=indicators
            )
            
        elif new_regime == 'converged':
            # Convergence detected - apply DeepSeek's additional filters
            
            # Check ADX for trend strength
            if adx < self.adx_threshold:
                self.blocked_signals_count += 1
                return self._create_regime_response(
                    can_trade=False,
                    reason=f"Weak trend: ADX {adx:.1f} < {self.adx_threshold} (DeepSeek danger zone)",
                    gap_pct=gap_pct,
                    regime='converged_weak',
                    indicators=indicators
                )
            
            # Check volatility expansion
            is_volatile = atr_14 > atr_30 * self.atr_multiplier if atr_30 > 0 else False
            
            if is_volatile:
                # Allow reduced position trading during volatile convergence
                self.position_reductions_count += 1
                return self._create_regime_response(
                    can_trade=True,
                    reason=f"Converged but volatile: gap {gap_pct:.3f}%, ADX {adx:.1f}, ATR expansion",
                    gap_pct=gap_pct,
                    regime='converged_volatile',
                    position_multiplier=0.25,  # 75% reduction as per DeepSeek
                    stop_multiplier=2.5,       # Wider stops
                    indicators=indicators
                )
            else:
                # Block trading during non-volatile convergence
                self.blocked_signals_count += 1
                return self._create_regime_response(
                    can_trade=False,
                    reason=f"Converged without volatility: gap {gap_pct:.3f}%, no ATR expansion",
                    gap_pct=gap_pct,
                    regime='converged_quiet',
                    indicators=indicators
                )
        
        # Fallback
        return self._create_regime_response(False, "Unknown regime", gap_pct, "unknown")
    
    def _create_regime_response(self, can_trade: bool, reason: str, gap_pct: float, 
                               regime: str, position_multiplier: float = 0.0, 
                               stop_multiplier: float = 1.0, indicators: Dict = None) -> Dict[str, Any]:
        """Create standardized regime response."""
        response = {
            'can_trade': can_trade and self.enabled,
            'reason': reason,
            'gap_pct': gap_pct,
            'regime': regime,
            'position_multiplier': position_multiplier,
            'stop_multiplier': stop_multiplier,
            'filter_enabled': self.enabled,
            'timestamp': datetime.now()
        }
        
        if indicators:
            response.update({
                'ema20': indicators.get('ema20', 0),
                'sma50': indicators.get('sma50', 0),
                'adx': indicators.get('adx', 0),
                'atr_14': indicators.get('atr_14', 0)
            })
        
        # Log the decision
        if can_trade and self.enabled:
            if position_multiplier < 1.0:
                logger.info(f"🔶 POSITION REDUCED: {reason}")
                logger.info(f"   Position multiplier: {position_multiplier:.2f}")
            else:
                logger.debug(f"✅ Trading allowed: {reason}")
        else:
            logger.info(f"🚫 TRADING BLOCKED: {reason}")
        
        return response
    
    def get_model_specific_actions(self, regime: str) -> Dict[str, str]:
        """
        Generate model-specific recommendations based on DeepSeek analysis.
        
        Args:
            regime: Current market regime
            
        Returns:
            Dictionary with model-specific actions
        """
        if regime == 'trending':
            return {
                'short_term_pattern_nn': 'Normal operation',
                'short_term_momentum_rf': 'Normal operation', 
                'short_term_reversion_gb': 'Normal operation',
                'medium_term_trend_lstm': 'Full position size',
                'medium_term_breakout_rf': 'Full position size',
                'medium_term_volatility_xgb': 'Normal operation',
                'long_term_macro_dnn': 'Confirm with MACD',
                'long_term_levels_rf': 'Normal operation',
                'long_term_portfolio_gb': 'Normal operation'
            }
        else:
            return {
                'short_term_pattern_nn': 'Switch to Bollinger reversion (2.2σ)',
                'short_term_momentum_rf': 'Reduce sensitivity',
                'short_term_reversion_gb': 'Primary model during convergence',
                'medium_term_trend_lstm': 'Max 25% position size',
                'medium_term_breakout_rf': 'Disable breakout detection',
                'medium_term_volatility_xgb': 'Primary model during chop',
                'long_term_macro_dnn': 'Disable trading',
                'long_term_levels_rf': 'Support/resistance only',
                'long_term_portfolio_gb': 'Defensive allocation'
            }
    
    def check_dataframe(self, df: pd.DataFrame) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Main interface method - check DataFrame for trading permission.
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            Tuple of (can_trade, reason, details)
        """
        try:
            # Calculate indicators
            indicators = self.calculate_technical_indicators(df)
            
            if not indicators:
                return False, "Unable to calculate technical indicators", {}
            
            # Assess regime
            regime_info = self.assess_market_regime(indicators)
            
            # Add model-specific actions
            regime_info['model_actions'] = self.get_model_specific_actions(regime_info['regime'])
            
            return regime_info['can_trade'], regime_info['reason'], regime_info
            
        except Exception as e:
            logger.error(f"Error in advanced convergence filter: {e}")
            return False, f"Filter error: {e}", {}
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive filter statistics."""
        block_rate = (self.blocked_signals_count / self.total_checks_count * 100) if self.total_checks_count > 0 else 0
        reduction_rate = (self.position_reductions_count / self.total_checks_count * 100) if self.total_checks_count > 0 else 0
        
        return {
            "enabled": self.enabled,
            "gap_threshold_pct": self.gap_threshold * 100,
            "adx_threshold": self.adx_threshold,
            "total_checks": self.total_checks_count,
            "blocked_signals": self.blocked_signals_count,
            "position_reductions": self.position_reductions_count,
            "block_rate_pct": block_rate,
            "reduction_rate_pct": reduction_rate,
            "current_regime": self.current_regime
        }

def test_advanced_filter():
    """Test the advanced convergence filter."""
    print("🧪 TESTING ADVANCED CONVERGENCE FILTER")
    print("=" * 60)
    
    # Create sample data
    np.random.seed(42)
    dates = pd.date_range('2025-01-01', periods=100, freq='5min')
    
    # Simulate price data with convergence scenario
    base_price = 50000
    price_data = base_price + np.cumsum(np.random.randn(100) * 10)
    
    df = pd.DataFrame({
        'datetime': dates,
        'open': price_data,
        'high': price_data + np.random.rand(100) * 20,
        'low': price_data - np.random.rand(100) * 20,
        'close': price_data + np.random.randn(100) * 5,
        'volume': np.random.randint(1000, 5000, 100)
    })
    
    # Test filter
    filter_obj = AdvancedConvergenceFilter()
    can_trade, reason, details = filter_obj.check_dataframe(df)
    
    print(f"✅ Filter test completed")
    print(f"   Can trade: {can_trade}")
    print(f"   Reason: {reason}")
    print(f"   Regime: {details.get('regime', 'unknown')}")
    print(f"   Gap: {details.get('gap_pct', 0):.3f}%")
    print(f"   Position multiplier: {details.get('position_multiplier', 0):.2f}")
    
    # Show statistics
    stats = filter_obj.get_statistics()
    print(f"\n📊 Filter Statistics:")
    for key, value in stats.items():
        print(f"   {key}: {value}")

if __name__ == "__main__":
    test_advanced_filter()
