#!/usr/bin/env python3
"""
Quick verification that the daily profit limit is now set to $20.
"""

def verify_profit_limit():
    """Verify the daily profit limit is $20."""
    print("🔍 VERIFYING DAILY PROFIT LIMIT UPDATE")
    print("=" * 50)
    
    try:
        import config
        
        # Get the profit limit
        circuit_breakers = config.SYNTHETIC_RISK_RULES.get("circuit_breakers", {})
        max_daily_profit = circuit_breakers.get("max_daily_profit", None)
        max_daily_loss = circuit_breakers.get("max_daily_drawdown", None)
        
        print(f"📊 CURRENT LIMITS:")
        print(f"   Daily Loss Limit: ${max_daily_loss:.2f}")
        print(f"   Daily Profit Limit: ${max_daily_profit:.2f}")
        
        # Verify the profit limit is $20
        if max_daily_profit == 20.0:
            print("✅ Daily profit limit correctly updated to $20.00")
        else:
            print(f"❌ Daily profit limit incorrect: expected $20.00, got ${max_daily_profit:.2f}")
            return False
            
        # Test scenarios around the new limit
        print(f"\n🧪 TESTING $20 PROFIT LIMIT SCENARIOS:")
        print("-" * 40)
        
        test_scenarios = [
            (19.99, "CONTINUE", "Just under limit"),
            (20.00, "CONTINUE", "At limit (not exceeded)"),
            (20.01, "STOP", "Just over limit"),
            (25.00, "STOP", "Well over limit"),
        ]
        
        for pnl, expected, description in test_scenarios:
            would_stop = pnl > max_daily_profit
            actual = "STOP" if would_stop else "CONTINUE"
            status = "✅" if actual == expected else "❌"
            print(f"{status} P&L: ${pnl:6.2f} | Expected: {expected:8} | Actual: {actual:8} | {description}")
        
        print(f"\n🎯 DAILY TRADING ENVELOPE:")
        print(f"   Loss Limit: -${max_daily_loss:.2f}")
        print(f"   Profit Limit: +${max_daily_profit:.2f}")
        print(f"   Total Range: ${max_daily_loss + max_daily_profit:.2f} (${max_daily_loss:.2f} + ${max_daily_profit:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = verify_profit_limit()
    if success:
        print("\n🎉 VERIFICATION SUCCESSFUL!")
        print("💡 Daily profit limit updated to $20.00")
        print("🔄 Ready to restart AI trading system")
    else:
        print("\n❌ VERIFICATION FAILED!")
        print("⚠️  Please check configuration")
