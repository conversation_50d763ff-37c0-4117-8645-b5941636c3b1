"""
Debug Signal Generation - Test why strong signals aren't triggering trades
"""

import sys
import time
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/debug_signal_generation.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("DebugSignalGeneration")

def test_signal_generation_detailed():
    """Test signal generation with detailed debugging."""
    print("🔍 DEBUGGING SIGNAL GENERATION")
    print("=" * 60)
    
    try:
        # Import components
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        from trading_signal_generator import TradingSignalGenerator
        from order_execution_system import OrderExecutionSystem
        
        print("✅ Components imported")
        
        # Initialize components
        print("🔧 Initializing components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Components initialized")
        
        # Load models
        print("📚 Loading models...")
        loaded_count = 0
        for model_name in ai_manager.model_configs.keys():
            if ai_manager.load_model(model_name):
                loaded_count += 1
                
        print(f"✅ {loaded_count} models loaded")
        
        # Get current price
        current_price = order_executor._get_current_price()
        print(f"💰 Current price: {current_price:.2f}")
        
        # Check daily limits
        print("\n📊 CHECKING DAILY LIMITS:")
        print(f"   Daily trade count: {signal_generator.daily_trade_count}")
        print(f"   Max daily trades: {signal_generator.max_daily_trades}")
        print(f"   Can generate signal: {signal_generator._can_generate_signal()}")
        
        # Get ensemble prediction directly
        print("\n🧠 GETTING ENSEMBLE PREDICTION:")
        
        # Get model features for all models
        model_features = {}
        for model_name in ai_manager.model_configs.keys():
            if model_name in ai_manager.models:
                try:
                    # Use the same feature generation as dashboard
                    config = ai_manager.model_configs[model_name]
                    timeframes = config["timeframes"]
                    feature_types = config["features"]
                    lookback = config["lookback"]
                    
                    # Get data for primary timeframe
                    primary_tf = timeframes[0]
                    data = data_collector.get_latest_data(primary_tf, lookback)
                    
                    if not data.empty:
                        # Calculate indicators
                        df_with_indicators = pattern_detector.calculate_synthetic_indicators(data)
                        
                        if not df_with_indicators.empty:
                            # Extract features
                            features = ai_manager.extract_features(df_with_indicators, feature_types, lookback)
                            if features is not None and len(features) > 0:
                                model_features[model_name] = features
                                print(f"   ✅ {model_name}: {len(features)} features")
                            else:
                                print(f"   ❌ {model_name}: No features extracted")
                        else:
                            print(f"   ❌ {model_name}: No indicators calculated")
                    else:
                        print(f"   ❌ {model_name}: No data available")
                        
                except Exception as e:
                    print(f"   ❌ {model_name}: Error - {e}")
        
        print(f"\n📋 Total models with features: {len(model_features)}")
        
        if model_features:
            # Get ensemble prediction
            ensemble = ai_manager.get_ensemble_prediction_with_features(model_features, target_timeframe=5)
            
            print("\n🎯 ENSEMBLE PREDICTION RESULTS:")
            print(f"   Consensus: {ensemble.get('consensus', 'unknown')}")
            print(f"   Confidence: {ensemble.get('confidence', 0):.3f}")
            print(f"   Strong signals: {len(ensemble.get('strong_signals', []))}")
            print(f"   Consensus signals: {len(ensemble.get('timeframe_consensus_signals', []))}")
            
            # Check strong signals
            strong_signals = ensemble.get('strong_signals', [])
            if strong_signals:
                print("\n🚀 STRONG SIGNALS FOUND:")
                for signal in strong_signals:
                    print(f"   Model: {signal['model_name']}")
                    print(f"   Signal: {signal['signal']}")
                    print(f"   Confidence: {signal['confidence']:.3f}")
                    print(f"   Should trigger: {abs(signal['signal']) == 2 and signal['confidence'] >= 0.6}")
                    print()
            
            # Check consensus signals
            consensus_signals = ensemble.get('timeframe_consensus_signals', [])
            if consensus_signals:
                print("🤝 CONSENSUS SIGNALS FOUND:")
                for signal in consensus_signals:
                    print(f"   Model: {signal['model_name']}")
                    print(f"   Signal: {signal['signal']}")
                    print(f"   Confidence: {signal['confidence']:.3f}")
                    print()
        
        # Now test actual signal generation
        print("\n🎯 TESTING ACTUAL SIGNAL GENERATION:")
        signal = signal_generator.generate_signal(current_price)
        
        if signal:
            print("🎉 SIGNAL GENERATED!")
            print(f"   Type: {signal.signal_type.name}")
            print(f"   Confidence: {signal.confidence:.3f}")
            print(f"   Entry: {signal.entry_price:.2f}")
            print(f"   Stop Loss: {signal.stop_loss:.2f}")
            print(f"   Take Profit: {signal.take_profit:.2f}")
            print(f"   Reasoning: {signal.reasoning}")
        else:
            print("❌ NO SIGNAL GENERATED")
            print("   This indicates the signal generator logic is not working correctly")
            
        return True
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        logger.error(f"Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function."""
    print("🔍 AI TRADING SYSTEM - SIGNAL GENERATION DEBUG")
    print("=" * 70)
    print("This will test why strong signals aren't triggering trades")
    print("=" * 70)
    
    success = test_signal_generation_detailed()
    
    if success:
        print("\n✅ DEBUG COMPLETED")
        print("Check the output above to see why signals aren't triggering trades")
    else:
        print("\n❌ DEBUG FAILED")
        print("Check the logs for detailed error information")
        
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
