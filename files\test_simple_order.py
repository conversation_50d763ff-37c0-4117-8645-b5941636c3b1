#!/usr/bin/env python3
"""
Test script to place a simple order without SL/TP to see if basic trading works.
"""

import sys
import logging
import time
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_simple_order():
    """Test placing a simple order without SL/TP."""
    try:
        print("🧪 TESTING SIMPLE ORDER WITHOUT SL/TP")
        print("=" * 60)
        print("⚠️  This will place a REAL trade without SL/TP!")
        print("⚠️  Make sure you're using a DEMO account!")
        print("=" * 60)
        
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ MT5 initialization failed!")
            return False
            
        print("✅ MT5 initialized successfully")
        
        # Get current price
        tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
        if tick is None:
            print("❌ Could not get current price!")
            return False
        current_price = (tick.bid + tick.ask) / 2
        
        print(f"✅ Current Price: {current_price:.2f}")
        print(f"✅ Bid: {tick.bid:.2f}")
        print(f"✅ Ask: {tick.ask:.2f}")
        print(f"✅ Spread: {tick.ask - tick.bid:.2f}")
        
        # Check account balance
        account_info = mt5.account_info()
        if not account_info:
            print("❌ Could not get account info!")
            return False
            
        print(f"✅ Account Balance: ${account_info.balance:.2f}")
        print(f"✅ Free Margin: ${account_info.margin_free:.2f}")
        
        # Create simple order request (no SL/TP)
        print("\n🚀 PLACING SIMPLE BUY ORDER (NO SL/TP)...")
        
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": "DEX 900 DOWN Index",
            "volume": 0.01,
            "type": mt5.ORDER_TYPE_BUY,
            "deviation": 50,
            "magic": 123456,
            "comment": "Simple_Test_Order",
        }
        
        print(f"   Order Details:")
        print(f"   - Symbol: {request['symbol']}")
        print(f"   - Volume: {request['volume']} lots")
        print(f"   - Type: BUY")
        print(f"   - Deviation: {request['deviation']} points")
        print(f"   - Magic: {request['magic']}")
        
        # First check the order
        print("\n🔍 Checking order validity...")
        check_result = mt5.order_check(request)
        
        if check_result:
            print(f"   Return Code: {check_result.retcode}")
            print(f"   Comment: {check_result.comment}")

            # Return code 0 means success for order_check
            if check_result.retcode == 0:
                print("✅ Order check PASSED!")
                print(f"   Required Margin: ${check_result.margin:.2f}")
                # Note: OrderCheckResult may not have price attribute
            else:
                print(f"❌ Order check FAILED: {check_result.comment}")
                return False
        else:
            print("❌ No result from order_check!")
            return False
            
        # Now send the order
        print("\n⚡ SENDING ORDER...")
        result = mt5.order_send(request)
        
        if result:
            print(f"   Return Code: {result.retcode}")
            print(f"   Comment: {result.comment}")
            print(f"   Request ID: {result.request_id}")
            print(f"   Order ID: {result.order}")
            print(f"   Deal ID: {result.deal}")
            print(f"   Volume: {result.volume}")
            print(f"   Price: {result.price:.2f}")
            print(f"   Bid: {result.bid:.2f}")
            print(f"   Ask: {result.ask:.2f}")
            
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                print("🎉 ORDER EXECUTED SUCCESSFULLY!")
                order_id = result.order
                deal_id = result.deal
                
                # Wait for position to appear
                print("\n⏳ Waiting for position to appear...")
                time.sleep(3)
                
                # Check positions
                positions = mt5.positions_get(symbol="DEX 900 DOWN Index")
                if positions:
                    print(f"✅ Found {len(positions)} position(s)")
                    
                    for pos in positions:
                        print(f"   Position {pos.ticket}:")
                        print(f"   - Type: {'BUY' if pos.type == 0 else 'SELL'}")
                        print(f"   - Volume: {pos.volume}")
                        print(f"   - Open Price: {pos.price_open:.2f}")
                        print(f"   - Current Price: {pos.price_current:.2f}")
                        print(f"   - Profit: ${pos.profit:.2f}")
                        print(f"   - SL: {pos.sl:.2f}")
                        print(f"   - TP: {pos.tp:.2f}")
                        
                        # Close this position
                        print(f"\n🔄 Closing position {pos.ticket}...")
                        
                        close_request = {
                            "action": mt5.TRADE_ACTION_DEAL,
                            "symbol": "DEX 900 DOWN Index",
                            "volume": pos.volume,
                            "type": mt5.ORDER_TYPE_SELL,  # Opposite of open
                            "position": pos.ticket,
                            "deviation": 50,
                            "magic": 123456,
                            "comment": "Close_Simple_Test",
                        }
                        
                        close_result = mt5.order_send(close_request)
                        
                        if close_result and close_result.retcode == mt5.TRADE_RETCODE_DONE:
                            print("✅ Position closed successfully!")
                            print(f"   Close Price: {close_result.price:.2f}")
                            # Note: OrderSendResult may not have profit attribute
                            
                            # Wait and verify closure
                            time.sleep(2)
                            remaining_positions = mt5.positions_get(symbol="DEX 900 DOWN Index")
                            if remaining_positions:
                                remaining_ids = [p.ticket for p in remaining_positions]
                                if pos.ticket not in remaining_ids:
                                    print("✅ Position successfully removed")
                                else:
                                    print("⚠️  Position still exists")
                            else:
                                print("✅ No positions remaining")
                                
                        else:
                            close_comment = close_result.comment if close_result else "No result"
                            print(f"❌ Failed to close position: {close_comment}")
                            print("   Please manually close the position in MT5")
                            
                else:
                    print("⚠️  No positions found after order execution")
                    
                return True
                
            else:
                retcode_meanings = {
                    10004: "REQUOTE",
                    10006: "REJECT", 
                    10007: "CANCEL",
                    10008: "PLACED",
                    10009: "DONE",
                    10010: "DONE_PARTIAL",
                    10011: "ERROR",
                    10012: "TIMEOUT",
                    10013: "INVALID",
                    10014: "INVALID_VOLUME",
                    10015: "INVALID_PRICE",
                    10016: "INVALID_STOPS",
                    10017: "TRADE_DISABLED",
                    10018: "MARKET_CLOSED",
                    10019: "NO_MONEY",
                    10020: "PRICE_CHANGED",
                    10021: "PRICE_OFF",
                    10022: "INVALID_EXPIRATION",
                    10023: "ORDER_CHANGED",
                    10024: "TOO_MANY_REQUESTS",
                    10025: "NO_CHANGES",
                    10026: "SERVER_DISABLES_AT",
                    10027: "CLIENT_DISABLES_AT",
                    10028: "LOCKED",
                    10029: "FROZEN",
                    10030: "INVALID_FILL",
                    10031: "CONNECTION",
                    10032: "ONLY_REAL",
                    10033: "LIMIT_ORDERS",
                    10034: "LIMIT_VOLUME",
                    10035: "INVALID_ORDER",
                    10036: "POSITION_CLOSED",
                }
                
                meaning = retcode_meanings.get(result.retcode, f"UNKNOWN_{result.retcode}")
                print(f"❌ ORDER EXECUTION FAILED: {meaning}")
                return False
                
        else:
            print("❌ No result from order_send!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    print("🧪 SIMPLE ORDER TEST (NO SL/TP)")
    print("=" * 60)
    print("⚠️  IMPORTANT: This will place a REAL trade!")
    print("⚠️  Make sure you're using a DEMO account!")
    print("=" * 60)
    
    success = test_simple_order()
    
    if success:
        print("\n🎉 SIMPLE ORDER TEST PASSED!")
        print("✅ Basic trading functionality works!")
        print("✅ Can open and close positions!")
    else:
        print("\n❌ SIMPLE ORDER TEST FAILED!")
        print("🔧 Basic trading functionality has issues.")
        
    sys.exit(0 if success else 1)
