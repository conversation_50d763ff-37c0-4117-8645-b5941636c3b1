@echo off
REM AI Trading Bot 2.0 - Model Training Batch File
REM This script allows manual training of all AI models

echo ========================================
echo AI TRADING BOT 2.0 - MODEL TRAINING
echo ========================================
echo.
echo This will train all 9 AI models for the trading system.
echo Training may take 5-15 minutes depending on your hardware.
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if the training script exists
if not exist "train_all_models.py" (
    echo ERROR: train_all_models.py not found
    echo Please ensure you are running this from the correct directory
    pause
    exit /b 1
)

echo Starting model training...
echo.

REM Run the training script
python train_all_models.py

REM Check the exit code
if errorlevel 1 (
    echo.
    echo ERROR: Model training failed
    echo Please check the logs for details
) else (
    echo.
    echo SUCCESS: Model training completed
    echo Check the training report above for details
)

echo.
echo Press any key to exit...
pause >nul