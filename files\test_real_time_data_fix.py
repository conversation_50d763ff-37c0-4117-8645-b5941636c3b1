#!/usr/bin/env python3
"""
Test script to verify the real-time data aggregation fix.
This will test if tick data is being properly converted to OHLCV bars.
"""

import time
import pandas as pd
from datetime import datetime, timedelta
from synthetic_data_collector import SyntheticDataCollector
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_real_time_data_aggregation():
    """Test if real-time data collection and aggregation is working."""
    
    print("🧪 TESTING REAL-TIME DATA AGGREGATION FIX")
    print("=" * 60)
    
    # Initialize data collector
    print("\n1️⃣ Initializing data collector...")
    collector = SyntheticDataCollector()
    
    # Check initial data state
    print("\n2️⃣ Checking initial data state...")
    initial_1m = collector.get_latest_data(1, 5)
    initial_5m = collector.get_latest_data(5, 5)
    
    print(f"   Initial 1-min data: {len(initial_1m)} records")
    if not initial_1m.empty:
        latest_1m_time = pd.to_datetime(initial_1m.iloc[-1]['timestamp'], unit='s')
        print(f"   Latest 1-min timestamp: {latest_1m_time}")
    
    print(f"   Initial 5-min data: {len(initial_5m)} records")
    if not initial_5m.empty:
        latest_5m_time = pd.to_datetime(initial_5m.iloc[-1]['timestamp'], unit='s')
        print(f"   Latest 5-min timestamp: {latest_5m_time}")
    
    # Start real-time collection
    print("\n3️⃣ Starting real-time data collection...")
    collector.start_real_time_collection()
    
    if not collector.is_collecting:
        print("❌ Failed to start real-time collection!")
        return False
    
    print("✅ Real-time collection started")
    
    # Monitor for updates
    print("\n4️⃣ Monitoring for data updates (60 seconds)...")
    start_time = datetime.now()
    update_count = 0
    
    for i in range(12):  # Check every 5 seconds for 60 seconds
        time.sleep(5)
        
        # Check for new data
        current_1m = collector.get_latest_data(1, 5)
        current_5m = collector.get_latest_data(5, 5)
        
        # Compare with initial data
        new_1m_records = len(current_1m) - len(initial_1m)
        new_5m_records = len(current_5m) - len(initial_5m)
        
        if new_1m_records > 0 or new_5m_records > 0:
            update_count += 1
            print(f"   Update {update_count}: +{new_1m_records} 1-min, +{new_5m_records} 5-min records")
            
            # Show latest data
            if not current_1m.empty:
                latest_1m = pd.to_datetime(current_1m.iloc[-1]['timestamp'], unit='s')
                latest_1m_price = current_1m.iloc[-1]['close']
                print(f"      Latest 1-min: {latest_1m} - Price: {latest_1m_price:.2f}")
            
            if not current_5m.empty:
                latest_5m = pd.to_datetime(current_5m.iloc[-1]['timestamp'], unit='s')
                latest_5m_price = current_5m.iloc[-1]['close']
                print(f"      Latest 5-min: {latest_5m} - Price: {latest_5m_price:.2f}")
        
        print(f"   Check {i+1}/12 - Updates detected: {update_count}")
    
    # Stop collection
    print("\n5️⃣ Stopping real-time collection...")
    collector.stop_real_time_collection()
    
    # Final assessment
    print("\n📊 RESULTS:")
    print("=" * 30)
    
    if update_count > 0:
        print(f"✅ SUCCESS: {update_count} data updates detected")
        print("✅ Real-time OHLCV aggregation is working!")
        
        # Test feature extraction with fresh data
        print("\n6️⃣ Testing feature extraction with fresh data...")
        test_feature_extraction(collector)
        
        return True
    else:
        print("❌ FAILURE: No data updates detected")
        print("❌ Real-time OHLCV aggregation is NOT working")
        return False

def test_feature_extraction(collector):
    """Test if feature extraction works with fresh data."""
    try:
        from trading_signal_generator import TradingSignalGenerator
        from ai_model_manager import AIModelManager
        from synthetic_pattern_detector import SyntheticPatternDetector
        
        # Initialize components
        pattern_detector = SyntheticPatternDetector()
        ai_manager = AIModelManager(collector, pattern_detector)
        signal_generator = TradingSignalGenerator(collector, ai_manager, pattern_detector)
        
        # Get fresh data
        fresh_data = collector.get_latest_data(5, 100)
        if fresh_data.empty:
            print("❌ No fresh data available for feature extraction test")
            return
        
        # Calculate indicators
        df_indicators = pattern_detector.calculate_synthetic_indicators(fresh_data)
        
        # Test feature extraction
        model_features = signal_generator._extract_features_for_ai(df_indicators)
        
        if model_features:
            print(f"✅ Feature extraction successful for {len(model_features)} models")
            for model_name, features in model_features.items():
                print(f"   {model_name}: {len(features)} features")
        else:
            print("❌ Feature extraction failed - no features generated")
            
    except Exception as e:
        print(f"❌ Feature extraction test failed: {e}")

if __name__ == "__main__":
    try:
        success = test_real_time_data_aggregation()
        
        if success:
            print("\n🎉 REAL-TIME DATA FIX VERIFICATION: PASSED")
            print("   The models should now receive fresh market data!")
        else:
            print("\n💥 REAL-TIME DATA FIX VERIFICATION: FAILED")
            print("   Additional debugging required.")
            
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
