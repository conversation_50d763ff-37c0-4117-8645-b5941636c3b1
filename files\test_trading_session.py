#!/usr/bin/env python3
"""
Test script to check trading session status and account permissions.
This investigates why orders are being rejected.
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_trading_session():
    """Check trading session and account status."""
    try:
        print("🔍 CHECKING TRADING SESSION AND ACCOUNT STATUS")
        print("=" * 60)
        
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ MT5 initialization failed!")
            return False
            
        print("✅ MT5 initialized successfully")
        
        # Check account info in detail
        print("\n👤 DETAILED ACCOUNT INFORMATION:")
        account_info = mt5.account_info()
        
        if account_info:
            print(f"   Login: {account_info.login}")
            print(f"   Server: {account_info.server}")
            print(f"   Name: {account_info.name}")
            print(f"   Company: {account_info.company}")
            print(f"   Currency: {account_info.currency}")
            print(f"   Balance: ${account_info.balance:.2f}")
            print(f"   Equity: ${account_info.equity:.2f}")
            print(f"   Margin: ${account_info.margin:.2f}")
            print(f"   Free Margin: ${account_info.margin_free:.2f}")
            print(f"   Margin Level: {account_info.margin_level:.2f}%")
            print(f"   Trade Allowed: {account_info.trade_allowed}")
            print(f"   Trade Expert: {account_info.trade_expert}")
            print(f"   Leverage: 1:{account_info.leverage}")
            print(f"   Margin Call: {account_info.margin_so_call:.2f}%")
            print(f"   Margin Stop Out: {account_info.margin_so_so:.2f}%")
            
            if not account_info.trade_allowed:
                print("❌ TRADING NOT ALLOWED on this account!")
                return False
            elif not account_info.trade_expert:
                print("❌ EXPERT ADVISOR TRADING NOT ALLOWED!")
                return False
            else:
                print("✅ Trading permissions OK")
        else:
            print("❌ Could not get account information!")
            return False
            
        # Check symbol info in detail
        print("\n📊 DETAILED SYMBOL INFORMATION:")
        symbol_info = mt5.symbol_info("DEX 900 DOWN Index")
        
        if symbol_info:
            print(f"   Name: {symbol_info.name}")
            print(f"   Description: {symbol_info.description}")
            print(f"   Path: {symbol_info.path}")
            print(f"   Currency Base: {symbol_info.currency_base}")
            print(f"   Currency Profit: {symbol_info.currency_profit}")
            print(f"   Currency Margin: {symbol_info.currency_margin}")
            print(f"   Digits: {symbol_info.digits}")
            print(f"   Point: {symbol_info.point}")
            print(f"   Spread: {symbol_info.spread}")
            print(f"   Trade Mode: {symbol_info.trade_mode}")
            print(f"   Trade Execution: {symbol_info.trade_exemode}")
            print(f"   Trade Stops Level: {symbol_info.trade_stops_level}")
            print(f"   Trade Freeze Level: {symbol_info.trade_freeze_level}")
            print(f"   Volume Min: {symbol_info.volume_min}")
            print(f"   Volume Max: {symbol_info.volume_max}")
            print(f"   Volume Step: {symbol_info.volume_step}")
            print(f"   Volume Limit: {symbol_info.volume_limit}")
            print(f"   Margin Initial: {symbol_info.margin_initial}")
            print(f"   Margin Maintenance: {symbol_info.margin_maintenance}")
            
            # Check trade mode
            trade_modes = {
                0: "DISABLED",
                1: "LONGONLY", 
                2: "SHORTONLY",
                3: "CLOSEONLY",
                4: "FULL"
            }
            mode_name = trade_modes.get(symbol_info.trade_mode, "UNKNOWN")
            print(f"   Trade Mode Meaning: {mode_name}")
            
            if symbol_info.trade_mode != 4:
                print(f"❌ TRADING RESTRICTED! Mode: {mode_name}")
                if symbol_info.trade_mode == 0:
                    print("   Trading is completely DISABLED for this symbol!")
                elif symbol_info.trade_mode == 3:
                    print("   Only CLOSING positions allowed!")
                return False
            else:
                print("✅ Full trading allowed for symbol")
        else:
            print("❌ Could not get symbol information!")
            return False
            
        # Check market session
        print("\n⏰ MARKET SESSION INFORMATION:")
        current_time = datetime.now()
        print(f"   Current Time: {current_time}")
        
        # Get current tick
        tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
        if tick:
            print(f"   Last Tick Time: {datetime.fromtimestamp(tick.time)}")
            print(f"   Bid: {tick.bid:.2f}")
            print(f"   Ask: {tick.ask:.2f}")
            print(f"   Spread: {tick.ask - tick.bid:.2f}")
            
            # Check if tick is recent (within last minute)
            tick_age = current_time.timestamp() - tick.time
            if tick_age < 60:
                print(f"✅ Recent tick data (age: {tick_age:.1f} seconds)")
            else:
                print(f"⚠️  Old tick data (age: {tick_age:.1f} seconds)")
        else:
            print("❌ No tick data available!")
            return False
            
        # Check terminal info
        print("\n🖥️ TERMINAL INFORMATION:")
        terminal_info = mt5.terminal_info()
        if terminal_info:
            print(f"   Community Account: {terminal_info.community_account}")
            print(f"   Community Connection: {terminal_info.community_connection}")
            print(f"   Connected: {terminal_info.connected}")
            print(f"   DLL Allowed: {terminal_info.dlls_allowed}")
            print(f"   Trade Allowed: {terminal_info.trade_allowed}")
            print(f"   Trade Context Busy: {terminal_info.tradeapi_disabled}")
            print(f"   Email Enabled: {terminal_info.email_enabled}")
            print(f"   FTP Enabled: {terminal_info.ftp_enabled}")
            print(f"   Notifications Enabled: {terminal_info.notifications_enabled}")
            print(f"   MQ ID: {terminal_info.mqid}")
            print(f"   Build: {terminal_info.build}")
            print(f"   Max Bars: {terminal_info.maxbars}")
            print(f"   Code Page: {terminal_info.codepage}")
            print(f"   CPU Cores: {terminal_info.cpu_cores}")
            print(f"   Disk Space: {terminal_info.disk_space} MB")
            print(f"   Physical Memory: {terminal_info.memory_physical} MB")
            print(f"   Memory Total: {terminal_info.memory_total} MB")
            print(f"   Memory Available: {terminal_info.memory_available} MB")
            print(f"   Memory Used: {terminal_info.memory_used} MB")
            
            if not terminal_info.connected:
                print("❌ TERMINAL NOT CONNECTED to broker!")
                return False
            elif not terminal_info.trade_allowed:
                print("❌ TRADING NOT ALLOWED in terminal!")
                return False
            elif terminal_info.tradeapi_disabled:
                print("❌ TRADE API DISABLED!")
                return False
            else:
                print("✅ Terminal trading permissions OK")
        else:
            print("❌ Could not get terminal information!")
            return False
            
        # Test a simple order check with minimal parameters
        print("\n🧪 TESTING MINIMAL ORDER:")
        
        minimal_request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": "DEX 900 DOWN Index",
            "volume": 0.01,
            "type": mt5.ORDER_TYPE_BUY,
            "deviation": 50,  # Larger deviation
            "magic": 123456,
            "comment": "Minimal_Test",
        }
        
        print(f"   Testing minimal BUY order...")
        result = mt5.order_check(minimal_request)
        
        if result:
            print(f"   Return Code: {result.retcode}")
            print(f"   Comment: {result.comment}")
            print(f"   Request ID: {result.request_id}")
            print(f"   Volume: {result.volume}")
            print(f"   Price: {result.price:.2f}")
            print(f"   Bid: {result.bid:.2f}")
            print(f"   Ask: {result.ask:.2f}")
            print(f"   Margin: ${result.margin:.2f}")
            print(f"   Margin Free: ${result.margin_free:.2f}")
            print(f"   Margin Level: {result.margin_level:.2f}%")
            print(f"   Profit: ${result.profit:.2f}")
            
            # Check return codes
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                print("✅ ORDER CHECK PASSED!")
            else:
                retcode_meanings = {
                    10004: "TRADE_RETCODE_REQUOTE",
                    10006: "TRADE_RETCODE_REJECT", 
                    10007: "TRADE_RETCODE_CANCEL",
                    10008: "TRADE_RETCODE_PLACED",
                    10009: "TRADE_RETCODE_DONE",
                    10010: "TRADE_RETCODE_DONE_PARTIAL",
                    10011: "TRADE_RETCODE_ERROR",
                    10012: "TRADE_RETCODE_TIMEOUT",
                    10013: "TRADE_RETCODE_INVALID",
                    10014: "TRADE_RETCODE_INVALID_VOLUME",
                    10015: "TRADE_RETCODE_INVALID_PRICE",
                    10016: "TRADE_RETCODE_INVALID_STOPS",
                    10017: "TRADE_RETCODE_TRADE_DISABLED",
                    10018: "TRADE_RETCODE_MARKET_CLOSED",
                    10019: "TRADE_RETCODE_NO_MONEY",
                    10020: "TRADE_RETCODE_PRICE_CHANGED",
                    10021: "TRADE_RETCODE_PRICE_OFF",
                    10022: "TRADE_RETCODE_INVALID_EXPIRATION",
                    10023: "TRADE_RETCODE_ORDER_CHANGED",
                    10024: "TRADE_RETCODE_TOO_MANY_REQUESTS",
                    10025: "TRADE_RETCODE_NO_CHANGES",
                    10026: "TRADE_RETCODE_SERVER_DISABLES_AT",
                    10027: "TRADE_RETCODE_CLIENT_DISABLES_AT",
                    10028: "TRADE_RETCODE_LOCKED",
                    10029: "TRADE_RETCODE_FROZEN",
                    10030: "TRADE_RETCODE_INVALID_FILL",
                    10031: "TRADE_RETCODE_CONNECTION",
                    10032: "TRADE_RETCODE_ONLY_REAL",
                    10033: "TRADE_RETCODE_LIMIT_ORDERS",
                    10034: "TRADE_RETCODE_LIMIT_VOLUME",
                    10035: "TRADE_RETCODE_INVALID_ORDER",
                    10036: "TRADE_RETCODE_POSITION_CLOSED",
                }
                
                meaning = retcode_meanings.get(result.retcode, f"UNKNOWN_{result.retcode}")
                print(f"❌ ORDER CHECK FAILED: {meaning}")
        else:
            print("❌ No result from order_check!")
            
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 TRADING SESSION AND ACCOUNT STATUS CHECK")
    print("=" * 60)
    print("⚠️  This investigates why orders are being rejected")
    print("=" * 60)
    
    success = test_trading_session()
    
    if success:
        print("\n🎉 TRADING SESSION CHECK COMPLETE!")
        print("✅ Check the detailed information above!")
    else:
        print("\n❌ TRADING SESSION CHECK FOUND ISSUES!")
        print("🔧 Please check the error messages above.")
        
    sys.exit(0 if success else 1)
