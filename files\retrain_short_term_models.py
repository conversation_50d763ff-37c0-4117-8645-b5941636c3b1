#!/usr/bin/env python3
"""
Retrain volume-contaminated models with clean features.
Phase 1 completion: Remove volume contamination and implement clean algorithmic features.
"""

import sys
import time
from datetime import datetime

def retrain_clean_models():
    """Retrain the four volume-contaminated models with clean features."""
    print("🔥 RETRAINING VOLUME-CONTAMINATED MODELS WITH CLEAN FEATURES")
    print("=" * 80)
    print("Phase 1 Volume Removal: Retraining models with clean algorithmic features...")
    print("Removing volume contamination and implementing synthetic-optimized features.")
    
    try:
        # Import components
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        
        print("\n🔧 INITIALIZING COMPONENTS...")
        print("-" * 40)
        
        # Initialize components
        print("   Initializing Data Collector...")
        data_collector = SyntheticDataCollector()
        
        if not data_collector.mt5_connected:
            print("❌ MT5 not connected - cannot retrain models")
            return False
        
        print("   ✅ Data Collector initialized")
        
        print("   Initializing Pattern Detector...")
        pattern_detector = SyntheticPatternDetector(data_collector)
        print("   ✅ Pattern Detector initialized")
        
        print("   Initializing AI Model Manager...")
        ai_manager = AIModelManager(data_collector, pattern_detector)
        print("   ✅ AI Model Manager initialized")
        
        # Define volume-contaminated models to retrain with clean features
        contaminated_models = [
            "short_term_pattern_nn",      # volume → volatility_regime
            "short_term_momentum_rf",     # volume_velocity → volatility_adjusted_momentum
            "medium_term_trend_lstm",     # volume_trend → trend_sustainability
            "medium_term_breakout_rf"     # volume_breakout → false_breakout_filter
        ]

        print(f"\n📊 VOLUME-CONTAMINATED MODELS TO RETRAIN WITH CLEAN FEATURES:")
        print("-" * 70)
        for model in contaminated_models:
            config = ai_manager.model_configs[model]
            timeframes = config['timeframes']
            purpose = config['purpose']
            features = config['features']
            print(f"   🧠 {model}")
            print(f"      Timeframes: {timeframes}")
            print(f"      Purpose: {purpose}")
            print(f"      Clean Features: {features}")
        
        # Verify we have sufficient data for clean feature extraction
        print(f"\n📈 VERIFYING DATA FOR CLEAN FEATURE EXTRACTION...")
        print("-" * 60)

        # Check data availability for all timeframes used by contaminated models
        timeframes_needed = [1, 5, 15, 30, 60]  # All timeframes used by the 4 models

        data_status = {}
        for timeframe in timeframes_needed:
            df = data_collector.get_latest_data(timeframe, count=200)  # Need enough for indicators
            data_status[timeframe] = len(df) if not df.empty else 0

            if data_status[timeframe] >= 100:
                print(f"   ✅ {timeframe}-minute data: {data_status[timeframe]} records (sufficient)")
            elif data_status[timeframe] >= 50:
                print(f"   ⚠️  {timeframe}-minute data: {data_status[timeframe]} records (minimal)")
            else:
                print(f"   ❌ {timeframe}-minute data: {data_status[timeframe]} records (insufficient)")

        # Check if we have enough data overall
        sufficient_data = all(count >= 50 for count in data_status.values())

        if sufficient_data:
            print(f"\n✅ Sufficient data available for clean feature extraction")
        else:
            print(f"\n⚠️  Limited data - training may be affected")
            print(f"   Consider running data collection if training fails")

        # Test clean feature extraction
        print(f"\n🧹 TESTING CLEAN FEATURE EXTRACTION...")
        print("-" * 50)

        try:
            from trading_signal_generator import TradingSignalGenerator
            signal_gen = TradingSignalGenerator(data_collector, ai_manager, pattern_detector)

            # Test with 5-minute data
            test_data = data_collector.get_latest_data(5, 50)
            if not test_data.empty:
                df_indicators = pattern_detector.calculate_synthetic_indicators(test_data)

                # Test each clean feature function
                clean_functions = [
                    "_get_volatility_regime_features",
                    "_get_volatility_adjusted_momentum_features",
                    "_get_trend_sustainability_features",
                    "_get_false_breakout_filter_features"
                ]

                for func_name in clean_functions:
                    if hasattr(signal_gen, func_name):
                        func = getattr(signal_gen, func_name)
                        features = func(df_indicators, df_indicators.iloc[-1])
                        print(f"   ✅ {func_name}: {len(features)} features extracted")
                    else:
                        print(f"   ❌ {func_name}: Function missing")

                print(f"   ✅ Clean feature extraction test completed")
            else:
                print(f"   ⚠️  No test data available for feature extraction test")

        except Exception as e:
            print(f"   ❌ Clean feature extraction test failed: {e}")
        
        # Start retraining
        print(f"\n🚀 STARTING CLEAN MODEL RETRAINING...")
        print("=" * 60)

        training_results = {}
        total_models = len(contaminated_models)

        for i, model_name in enumerate(contaminated_models, 1):
            print(f"\n🧠 TRAINING MODEL {i}/{total_models}: {model_name}")
            print("-" * 50)
            
            start_time = time.time()
            
            try:
                # Train the model
                success = ai_manager.train_model(model_name)
                
                training_time = time.time() - start_time
                
                if success:
                    # Get performance metrics
                    performance = ai_manager.model_performance.get(model_name, {})
                    accuracy = performance.get('accuracy', 0)
                    samples = performance.get('total_samples', 0)
                    
                    print(f"   ✅ SUCCESS: {model_name}")
                    print(f"      Training time: {training_time:.1f} seconds")
                    print(f"      Accuracy: {accuracy:.3f}")
                    print(f"      Training samples: {samples}")
                    
                    training_results[model_name] = {
                        'success': True,
                        'accuracy': accuracy,
                        'samples': samples,
                        'time': training_time
                    }
                else:
                    print(f"   ❌ FAILED: {model_name}")
                    print(f"      Training time: {training_time:.1f} seconds")
                    
                    training_results[model_name] = {
                        'success': False,
                        'time': training_time
                    }
                    
            except Exception as e:
                training_time = time.time() - start_time
                print(f"   ❌ ERROR: {model_name}")
                print(f"      Exception: {e}")
                print(f"      Training time: {training_time:.1f} seconds")
                
                training_results[model_name] = {
                    'success': False,
                    'error': str(e),
                    'time': training_time
                }
        
        # Generate summary
        print(f"\n📊 RETRAINING SUMMARY:")
        print("=" * 50)
        
        successful_models = [name for name, result in training_results.items() if result['success']]
        failed_models = [name for name, result in training_results.items() if not result['success']]
        
        print(f"✅ Successful: {len(successful_models)}/{total_models}")
        for model in successful_models:
            result = training_results[model]
            print(f"   • {model}: {result['accuracy']:.3f} accuracy ({result['samples']} samples)")
        
        if failed_models:
            print(f"\n❌ Failed: {len(failed_models)}/{total_models}")
            for model in failed_models:
                result = training_results[model]
                error = result.get('error', 'Training failed')
                print(f"   • {model}: {error}")
        
        total_time = sum(result['time'] for result in training_results.values())
        print(f"\n⏱️  Total retraining time: {total_time:.1f} seconds")
        
        # Final status
        if len(successful_models) == total_models:
            print(f"\n🎉 ALL VOLUME-CONTAMINATED MODELS RETRAINED SUCCESSFULLY!")
            print(f"   ✅ Volume contamination completely eliminated")
            print(f"   ✅ Clean algorithmic features implemented")
            print(f"   ✅ Models optimized for synthetic index behavior")
            print(f"   🚀 Ready for Phase 2 enhancements")
            return True
        elif len(successful_models) > 0:
            print(f"\n⚠️  PARTIAL SUCCESS: {len(successful_models)}/{total_models} models retrained")
            print(f"   Some models failed but system can still operate with clean features")
            return True
        else:
            print(f"\n❌ RETRAINING FAILED: No models were successfully retrained")
            return False

    except Exception as e:
        print(f"❌ Error during clean model retraining: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main retraining function."""
    print("🔥 PHASE 1 VOLUME REMOVAL - CLEAN MODEL RETRAINING")
    print("=" * 80)
    print("This will retrain all volume-contaminated models with clean features:")
    print("• short_term_pattern_nn: volume → volatility_regime")
    print("• short_term_momentum_rf: volume_velocity → volatility_adjusted_momentum")
    print("• medium_term_trend_lstm: volume_trend → trend_sustainability")
    print("• medium_term_breakout_rf: volume_breakout → false_breakout_filter")
    print()

    # Auto-confirm for clean model retraining
    print("🚀 Starting clean model retraining automatically...")
    # response = input("Continue with clean model retraining? (y/N): ").strip().lower()
    # if response not in ['y', 'yes']:
    #     print("Clean model retraining cancelled.")
    #     return False

    print()

    # Start retraining
    success = retrain_clean_models()

    print(f"\n💡 PHASE 1 COMPLETION STATUS:")
    print("=" * 50)

    if success:
        print("🎉 PHASE 1 VOLUME REMOVAL COMPLETE!")
        print("✅ All volume contamination eliminated")
        print("✅ Clean algorithmic features implemented")
        print("✅ Models optimized for synthetic index behavior")
        print("✅ System ready for improved trading performance")
        print("🚀 Ready to proceed with Phase 2 enhancements")
        print("✅ You can restart your trading system to use the clean models")
    else:
        print("❌ Phase 1 had issues - check error messages above")
        print("💡 You can still use existing models while troubleshooting")
        print("💡 The trading system will continue to work with current models")

    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
