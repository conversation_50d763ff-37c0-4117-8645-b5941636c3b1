#!/usr/bin/env python3
"""Quick script to check database contents."""

import os
import sqlite3
import pandas as pd
from datetime import datetime

def check_database():
    """Check what's in the database."""
    try:
        # Connect to database (check both locations)
        db_paths = [
            'data/synthetic_cache/synthetic_data.db',  # Config location (check first)
            'synthetic_data.db',  # Root directory
        ]

        conn = None
        for db_path in db_paths:
            try:
                if os.path.exists(db_path):
                    print(f"📁 Found database at: {db_path}")
                    conn = sqlite3.connect(db_path)
                    break
                else:
                    print(f"❌ No database at: {db_path}")
            except Exception as e:
                print(f"❌ Error accessing {db_path}: {e}")

        if conn is None:
            print("❌ No database found in any location!")
            return
        cursor = conn.cursor()

        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        print("🔍 DATABASE ANALYSIS")
        print("=" * 50)
        
        if not tables:
            print("❌ No tables found in database!")
            return
            
        print(f"📊 Found {len(tables)} tables:")
        for table in tables:
            table_name = table[0]
            print(f"\n📋 Table: {table_name}")
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print("   Columns:")
            for col in columns:
                print(f"     - {col[1]} ({col[2]})")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   📈 Rows: {count}")
            
            # Get latest 3 records if any
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} ORDER BY rowid DESC LIMIT 3")
                recent = cursor.fetchall()
                print("   🕒 Latest records:")
                for i, row in enumerate(recent, 1):
                    print(f"     {i}. {row}")

        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    check_database()
