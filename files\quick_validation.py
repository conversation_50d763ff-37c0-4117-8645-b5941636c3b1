#!/usr/bin/env python3
"""
Quick validation of volume removal implementation.
"""

def check_model_configs():
    """Check if model configurations are updated."""
    print("🧠 Checking model configurations...")
    
    try:
        # Read ai_model_manager.py
        with open('ai_model_manager.py', 'r') as f:
            content = f.read()
        
        # Check for old volume features
        old_features = ['["price_action", "volume", "micro_patterns"]',
                       '["momentum", "rsi", "macd", "volume_velocity"]',
                       '["trend_strength", "moving_averages", "volume_trend"]',
                       '["support_resistance", "volume_breakout", "volatility"]']
        
        # Check for new clean features  
        new_features = ['["price_action", "micro_patterns", "volatility_regime"]',
                       '["momentum", "rsi", "macd", "volatility_adjusted_momentum"]',
                       '["trend_strength", "moving_averages", "trend_sustainability"]',
                       '["support_resistance", "false_breakout_filter", "volatility"]']
        
        old_found = sum(1 for feature in old_features if feature in content)
        new_found = sum(1 for feature in new_features if feature in content)
        
        print(f"   Old volume features found: {old_found}/4")
        print(f"   New clean features found: {new_found}/4")
        
        if old_found == 0 and new_found == 4:
            print("   ✅ Model configurations successfully updated!")
            return True
        else:
            print("   ❌ Model configurations need more work")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking configs: {e}")
        return False

def check_feature_functions():
    """Check if feature functions are updated."""
    print("\n🔧 Checking feature functions...")
    
    try:
        # Read trading_signal_generator.py
        with open('trading_signal_generator.py', 'r') as f:
            content = f.read()
        
        # Check for new functions
        new_functions = ['_get_volatility_regime_features',
                        '_get_volatility_adjusted_momentum_features',
                        '_get_trend_sustainability_features', 
                        '_get_false_breakout_filter_features']
        
        # Check for old functions (should be removed)
        old_functions = ['def _get_volume_features(',
                        'def _get_volume_velocity_features(',
                        'def _get_volume_trend_features(',
                        'def _get_volume_breakout_features(']
        
        new_found = sum(1 for func in new_functions if func in content)
        old_found = sum(1 for func in old_functions if func in content)
        
        print(f"   New clean functions found: {new_found}/4")
        print(f"   Old volume functions found: {old_found}/4")
        
        if new_found == 4 and old_found == 0:
            print("   ✅ Feature functions successfully updated!")
            return True
        else:
            print("   ❌ Feature functions need more work")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking functions: {e}")
        return False

def main():
    """Main validation."""
    print("🔥 QUICK VOLUME REMOVAL VALIDATION")
    print("=" * 50)
    
    config_ok = check_model_configs()
    functions_ok = check_feature_functions()
    
    print(f"\n📊 VALIDATION SUMMARY:")
    print(f"Model Configurations: {'✅ UPDATED' if config_ok else '❌ NEEDS WORK'}")
    print(f"Feature Functions: {'✅ UPDATED' if functions_ok else '❌ NEEDS WORK'}")
    
    if config_ok and functions_ok:
        print(f"\n🎉 PHASE 1 VOLUME REMOVAL SUCCESSFUL!")
        print("✅ All volume contamination removed")
        print("✅ Clean features implemented")
        print("🚀 Ready for model retraining!")
    else:
        print(f"\n⚠️  PHASE 1 PARTIALLY COMPLETE")
        print("🔧 Some components need additional work")
    
    return config_ok and functions_ok

if __name__ == "__main__":
    success = main()
    print(f"\nValidation {'PASSED' if success else 'FAILED'}")
