#!/usr/bin/env python3
"""
Test Strong Signal Fix
Verify that strong signals now trigger trades and dashboard shows consistent model count.
"""

import logging
import numpy as np

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_strong_signal_fix():
    """Test the strong signal confidence threshold fix."""
    print("🔍 TESTING STRONG SIGNAL FIXES")
    print("=" * 60)
    print("✅ Fix 1: Lowered strong signal threshold from 0.7 to 0.6")
    print("✅ Fix 2: Dashboard now uses timeframe-specific ensemble")
    print("=" * 60)
    
    try:
        # Import components
        from ai_model_manager import AIModelManager
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from trading_signal_generator import TradingSignalGenerator
        from model_decision_logger import decision_logger
        
        print("🔧 Initializing components...")
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        
        # Load models
        loaded_models = []
        for model_name in ai_manager.model_configs.keys():
            if ai_manager.load_model(model_name):
                loaded_models.append(model_name)
                
        print(f"✅ {len(loaded_models)} models loaded")
        
        if not loaded_models:
            print("⚠️ No models loaded - cannot test strong signals")
            return False
            
        print("\n🎯 TESTING STRONG SIGNAL DETECTION")
        print("-" * 40)
        
        # Test with current price
        test_price = 53727.26
        print(f"💰 Testing with price: {test_price}")
        
        # Generate signal
        signal = signal_generator.generate_signal(test_price)
        
        print("\n📊 SIGNAL RESULT:")
        if signal:
            print(f"   🚀 Signal Generated: {signal.signal_type.name}")
            print(f"   🎯 Confidence: {signal.confidence:.3f}")
            print(f"   💭 Reasoning: {signal.reasoning}")
            
            # Check if it was triggered by strong signal
            if "Strong signal" in signal.reasoning:
                print("   ✅ STRONG SIGNAL TRIGGERED TRADE!")
            else:
                print("   📊 Ensemble-based signal")
        else:
            print("   📊 No signal generated")
            
        print("\n🔍 TESTING STRONG SIGNAL DETECTION")
        print("-" * 40)

        # Test with mock strong signal
        print("🧪 Creating mock strong signal scenario...")

        # Create mock features for medium_term_breakout_rf
        mock_features = np.random.random(50).reshape(1, -1)

        # Test direct prediction
        if "medium_term_breakout_rf" in ai_manager.models:
            print("📊 Testing medium_term_breakout_rf directly...")
            prediction = ai_manager.predict("medium_term_breakout_rf", mock_features)
            if prediction:
                print(f"   🎯 Signal: {prediction['signal']}")
                print(f"   🎯 Confidence: {prediction['confidence']:.3f}")
                print(f"   🎯 Strong Signal? {abs(prediction['signal']) == 2 and prediction['confidence'] >= 0.6}")

        # Test ensemble with all models
        model_features = {}
        for model_name in ai_manager.model_configs.keys():
            if model_name in ai_manager.models:
                model_features[model_name] = np.random.random(50).reshape(1, -1)

        if model_features:
            print(f"\n📊 Testing ensemble with {len(model_features)} models")

            # Test ensemble with timeframe filtering
            ensemble_result = ai_manager.get_ensemble_prediction_with_features(model_features, target_timeframe=5)

            print(f"🎯 Ensemble Result:")
            print(f"   📊 Total Models with Predictions: {len(ensemble_result.get('predictions', {}))}")
            print(f"   🎯 Relevant Models for Voting: {ensemble_result.get('relevant_models', 0)}")
            print(f"   📊 Valid Predictions for Ensemble: {ensemble_result.get('total_models', 0)}")
            print(f"   ⏰ Target Timeframe: {ensemble_result.get('target_timeframe', 0)} minutes")
            print(f"   🔄 Consensus: {ensemble_result.get('consensus', 'unknown')}")

            # Show ALL models that made predictions
            predictions = ensemble_result.get('predictions', {})
            if predictions:
                print(f"\n   📋 ALL Models Making Predictions:")
                for model_name, pred_data in predictions.items():
                    category = pred_data.get('timeframe_category', 'UNKNOWN')
                    weight = pred_data.get('weight', 0)
                    signal = pred_data.get('prediction', 0)
                    confidence = pred_data.get('confidence', 0)
                    print(f"      • {model_name} ({category}): Signal {signal}, Conf {confidence:.3f}, Weight {weight:.2f}")

            # Check for strong signals (THIS IS THE KEY TEST)
            strong_signals = ensemble_result.get('strong_signals', [])
            print(f"\n   ⚡ STRONG SIGNALS TEST:")
            if strong_signals:
                print(f"      ✅ {len(strong_signals)} Strong Signals Detected!")
                for strong_signal in strong_signals:
                    print(f"         🚀 {strong_signal['model_name']}: Signal {strong_signal['signal']} (Conf: {strong_signal['confidence']:.3f})")
                    print(f"            Category: {strong_signal['timeframe_category']}")
                    print(f"            Should Trigger Trade: {abs(strong_signal['signal']) == 2 and strong_signal['confidence'] >= 0.6}")
            else:
                print(f"      ❌ No strong signals detected")
                print(f"      💡 This means no model produced signal ±2 with confidence ≥ 0.6")
                
        print("\n✅ FIXES VERIFICATION:")
        print("1. 🎯 Strong signal threshold: 0.6 (was 0.7)")
        print("2. 🕐 Timeframe filtering: Active")
        print("3. 📊 Dashboard consistency: Fixed")
        
        print("\n💡 WHAT SHOULD HAPPEN NOW:")
        print("• Strong signals (±2) with confidence ≥ 0.6 trigger immediate trades")
        print("• Dashboard shows same model count as console (6 for 5-min timeframe)")
        print("• Only relevant timeframe models vote on decisions")
        print("• medium_term_breakout_rf STRONG_SELL (0.615 conf) should now trigger trade!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing fixes: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        try:
            data_collector.cleanup()
        except:
            pass

if __name__ == "__main__":
    test_strong_signal_fix()
