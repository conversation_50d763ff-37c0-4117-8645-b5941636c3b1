#!/usr/bin/env python3
"""
Quick test to verify feature dimension consistency between training and live prediction.
"""

import sys
import os
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_feature_consistency():
    """Test that live feature extraction matches training feature extraction."""
    try:
        print("🔍 TESTING FEATURE DIMENSION CONSISTENCY")
        print("=" * 60)
        
        # Import required modules
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        from trading_signal_generator import TradingSignalGenerator
        
        # Initialize components
        print("📊 Initializing components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        
        # Load models
        print("🤖 Loading AI models...")

        # Load each model individually
        model_names = [
            "short_term_pattern_nn", "short_term_momentum_rf", "short_term_reversion_gb",
            "medium_term_trend_lstm", "medium_term_breakout_rf", "medium_term_volatility_xgb",
            "long_term_macro_dnn", "long_term_levels_rf", "long_term_portfolio_gb"
        ]

        loaded_count = 0
        for model_name in model_names:
            if ai_manager.load_model(model_name):
                loaded_count += 1

        if loaded_count == 0:
            print("❌ No models loaded! Please train models first.")
            return False

        print(f"✅ Loaded {loaded_count} models")
        
        # Get some test data
        print("📈 Getting test data...")
        df = data_collector.get_latest_data(15, count=100)  # 15min data
        
        if df.empty:
            print("❌ No data available! Please collect data first.")
            return False
            
        print(f"✅ Got {len(df)} data points")
        
        # Calculate indicators
        print("🔧 Calculating synthetic indicators...")
        df_indicators = pattern_detector.calculate_synthetic_indicators(df)
        
        # Test each model
        print("\n🧪 TESTING FEATURE EXTRACTION FOR EACH MODEL:")
        print("-" * 60)
        
        all_passed = True
        
        for model_name in ai_manager.models.keys():
            try:
                print(f"\n🔍 Testing {model_name}...")
                
                # Get expected feature count from scaler
                expected_features = ai_manager.scalers[model_name].n_features_in_
                print(f"   Expected features: {expected_features}")
                
                # Extract features using live method
                features = signal_generator._extract_features_for_model(
                    df_indicators, model_name, df_indicators.iloc[-1]
                )
                
                if features is None:
                    print(f"   ❌ Feature extraction failed!")
                    all_passed = False
                    continue
                    
                actual_features = len(features)
                print(f"   Actual features: {actual_features}")
                
                if actual_features == expected_features:
                    print(f"   ✅ PASS - Feature dimensions match!")
                else:
                    print(f"   ❌ FAIL - Feature dimension mismatch!")
                    all_passed = False
                    
            except Exception as e:
                print(f"   ❌ ERROR testing {model_name}: {e}")
                all_passed = False
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 ALL TESTS PASSED! Feature dimensions are consistent!")
            print("✅ The AI trading system should now work without dimension errors.")
        else:
            print("❌ SOME TESTS FAILED! Feature dimensions are inconsistent.")
            print("🔧 Please check the feature extraction methods.")
            
        return all_passed
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_feature_consistency()
    sys.exit(0 if success else 1)
