#!/usr/bin/env python3
"""
Test script to verify that daily trade limits have been successfully removed.
"""

import os
import sys
import json
from datetime import datetime

def test_config_changes():
    """Test that config.py has removed daily trade limits."""
    print("🔧 TESTING CONFIG.PY CHANGES...")
    print("-" * 50)
    
    try:
        import config
        
        # Check that daily trade limits are removed/commented
        if hasattr(config, 'MAX_DAILY_TRADES'):
            print("❌ MAX_DAILY_TRADES still exists in config")
            return False
        else:
            print("✅ MAX_DAILY_TRADES removed from config")
        
        if hasattr(config, 'MAX_DAILY_TRADES_PER_TIMEFRAME'):
            print("❌ MAX_DAILY_TRADES_PER_TIMEFRAME still exists in config")
            return False
        else:
            print("✅ MAX_DAILY_TRADES_PER_TIMEFRAME removed from config")
        
        # Check that other limits are still present
        if hasattr(config, 'MAX_CONCURRENT_POSITIONS'):
            print(f"✅ MAX_CONCURRENT_POSITIONS kept: {config.MAX_CONCURRENT_POSITIONS}")
        else:
            print("❌ MAX_CONCURRENT_POSITIONS missing")
            return False
        
        # Check circuit breakers
        if hasattr(config, 'SYNTHETIC_RISK_RULES'):
            circuit_breakers = config.SYNTHETIC_RISK_RULES.get('circuit_breakers', {})
            if 'max_daily_drawdown' in circuit_breakers:
                print(f"✅ Daily loss limit kept: ${circuit_breakers['max_daily_drawdown']}")
            else:
                print("❌ Daily loss limit missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing config: {e}")
        return False

def test_dashboard_changes():
    """Test that dashboard shows correct circuit breakers."""
    print("\n🖥️ TESTING DASHBOARD CHANGES...")
    print("-" * 50)
    
    try:
        # Check dashboard server
        server_file = "dashboard_server.py"
        if os.path.exists(server_file):
            with open(server_file, 'r', encoding='utf-8') as f:
                server_content = f.read()
            
            # Check that old trade limits are removed
            if '"max_daily_trades"' in server_content:
                print("❌ max_daily_trades still in dashboard server")
                return False
            else:
                print("✅ max_daily_trades removed from dashboard server")
            
            if '"max_per_timeframe"' in server_content:
                print("❌ max_per_timeframe still in dashboard server")
                return False
            else:
                print("✅ max_per_timeframe removed from dashboard server")
            
            # Check that new circuit breakers are present
            if '"one_per_timeframe"' in server_content:
                print("✅ one_per_timeframe added to dashboard server")
            else:
                print("❌ one_per_timeframe missing from dashboard server")
                return False
        
        # Check dashboard JavaScript
        js_file = "dashboard/static/dashboard.js"
        if os.path.exists(js_file):
            with open(js_file, 'r', encoding='utf-8') as f:
                js_content = f.read()
            
            # Check that daily limits are removed from display
            if 'dailyLimit' in js_content and '${dailyTrades}/${dailyLimit}' in js_content:
                print("❌ Daily limits still displayed in JavaScript")
                return False
            else:
                print("✅ Daily limits removed from JavaScript display")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing dashboard: {e}")
        return False

def test_order_execution_changes():
    """Test that order execution system has removed daily trade limit checks."""
    print("\n⚙️ TESTING ORDER EXECUTION CHANGES...")
    print("-" * 50)
    
    try:
        order_file = "order_execution_system.py"
        if os.path.exists(order_file):
            with open(order_file, 'r', encoding='utf-8') as f:
                order_content = f.read()
            
            # Check that daily trade limit checks are removed
            if 'config.MAX_DAILY_TRADES' in order_content and 'REMOVED' not in order_content:
                print("❌ Daily trade limit checks still active")
                return False
            else:
                print("✅ Daily trade limit checks removed")
            
            # Check that timeframe position limits are kept
            if 'timeframe_positions.get(timeframe)' in order_content:
                print("✅ Timeframe position limits kept (1 per timeframe)")
            else:
                print("❌ Timeframe position limits missing")
                return False
            
            # Check that concurrent position limits are kept
            if 'max_concurrent_positions' in order_content:
                print("✅ Concurrent position limits kept")
            else:
                print("❌ Concurrent position limits missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing order execution: {e}")
        return False

def test_signal_generator_changes():
    """Test that signal generator has removed daily trade limit checks."""
    print("\n📡 TESTING SIGNAL GENERATOR CHANGES...")
    print("-" * 50)
    
    try:
        signal_file = "trading_signal_generator.py"
        if os.path.exists(signal_file):
            with open(signal_file, 'r', encoding='utf-8') as f:
                signal_content = f.read()
            
            # Check that daily trade limit checks are removed
            if 'self.max_daily_trades' in signal_content and 'REMOVED' not in signal_content:
                print("❌ Daily trade limit checks still active in signal generator")
                return False
            else:
                print("✅ Daily trade limit checks removed from signal generator")
            
            # Check that other limits are kept
            if 'recent_signals' in signal_content:
                print("✅ Signal frequency limits kept (anti-spam)")
            else:
                print("❌ Signal frequency limits missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing signal generator: {e}")
        return False

def create_summary_report():
    """Create a summary of what was changed."""
    print("\n📋 SUMMARY OF CHANGES MADE:")
    print("=" * 60)
    
    changes = [
        "✅ REMOVED: Daily trade limits (no limit on trades per day)",
        "✅ REMOVED: Timeframe-specific daily trade limits",
        "✅ REMOVED: 'Max per timeframe' from dashboard display",
        "✅ REMOVED: Daily trade limit checks from order execution",
        "✅ REMOVED: Daily trade limit checks from signal generation",
        "",
        "🛡️ KEPT: All other circuit breakers and safety measures:",
        "   • Daily loss limit ($20.00)",
        "   • Maximum concurrent positions (3)",
        "   • One trade per timeframe limit",
        "   • Drawdown limit (50%)",
        "   • MT5 connection requirements",
        "   • Signal frequency limits (anti-spam)",
        "",
        "📊 DASHBOARD UPDATES:",
        "   • Removed daily limit display from timeframe sections",
        "   • Updated circuit breakers to show relevant limits only",
        "   • Trade counts still tracked for monitoring (no limits)",
        "",
        "🎯 RESULT:",
        "   • System can now trade unlimited times per day",
        "   • Each timeframe can still only have 1 active trade",
        "   • All other safety measures remain in place",
        "   • Loss limits and position limits still enforced"
    ]
    
    for change in changes:
        print(change)

def main():
    """Main test function."""
    print("🧪 AI TRADING SYSTEM - DAILY LIMITS REMOVAL TEST")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Run all tests
    tests = [
        test_config_changes,
        test_dashboard_changes,
        test_order_execution_changes,
        test_signal_generator_changes
    ]
    
    for test_func in tests:
        if not test_func():
            all_tests_passed = False
    
    # Show results
    print("\n🎯 TEST RESULTS:")
    print("=" * 30)
    
    if all_tests_passed:
        print("✅ ALL TESTS PASSED!")
        print("🎉 Daily trade limits successfully removed!")
        print("🛡️ All other safety measures remain in place!")
        
        create_summary_report()
        
        print("\n💡 NEXT STEPS:")
        print("   1. Restart the AI trading system")
        print("   2. Check dashboard for updated display")
        print("   3. Monitor system behavior (unlimited trades)")
        print("   4. Verify other circuit breakers still work")
        
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️  Please review the errors above")
        print("🔧 Manual fixes may be required")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
