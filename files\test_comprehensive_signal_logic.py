#!/usr/bin/env python3
"""
Comprehensive test for the new 3-tier signal logic:
1. ULTRA-STRONG: ±2 with confidence ≥0.6 (single model triggers trade)
2. TIMEFRAME CONSENSUS: ±1 with confidence ≥0.7 (need 2+ models in same timeframe)
3. WEAK SIGNALS: ±1 with confidence <0.7 (full ensemble consensus)

Tests ALL models and BOTH BUY/SELL signals.
"""

import sys
import os
import logging
import numpy as np
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_comprehensive_signal_logic():
    """Test the comprehensive 3-tier signal logic."""
    try:
        print("🧪 COMPREHENSIVE 3-TIER SIGNAL LOGIC TEST")
        print("=" * 60)
        
        # Import components
        from ai_model_manager import AIModelManager
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        
        # Initialize components
        print("📊 Initializing AI components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        
        # Skip model loading for this test - we're testing logic only
        print("🤖 Testing signal logic (no model loading needed)...")
        
        # Get all model names
        all_models = list(ai_manager.model_configs.keys())
        print(f"📋 Total models configured: {len(all_models)}")
        
        # Test model categorization
        print(f"\n🏷️  MODEL CATEGORIZATION TEST:")
        timeframe_groups = {"short_term": [], "medium_term": [], "long_term": []}
        
        for model_name in all_models:
            category = ai_manager._get_model_timeframe_category(model_name)
            print(f"   📈 {model_name}: {category}")
            if category.lower() in timeframe_groups:
                timeframe_groups[category.lower()].append(model_name)
        
        print(f"\n📊 TIMEFRAME GROUPS:")
        for group, models in timeframe_groups.items():
            print(f"   {group.upper()}: {len(models)} models - {models}")
        
        # Test scenarios
        print(f"\n🎯 TESTING SIGNAL SCENARIOS:")
        
        # Scenario 1: Ultra-strong signals (±2)
        print(f"\n1️⃣  ULTRA-STRONG SIGNALS (±2 with confidence ≥0.6)")
        test_ultra_strong_scenarios(ai_manager, timeframe_groups)
        
        # Scenario 2: Timeframe consensus (±1 with confidence ≥0.7)
        print(f"\n2️⃣  TIMEFRAME CONSENSUS (±1 with confidence ≥0.7, need 2+ models)")
        test_timeframe_consensus_scenarios(ai_manager, timeframe_groups)
        
        # Scenario 3: Weak signals (±1 with confidence <0.7)
        print(f"\n3️⃣  WEAK SIGNALS (±1 with confidence <0.7, ensemble consensus)")
        test_weak_signal_scenarios(ai_manager, timeframe_groups)
        
        print(f"\n✅ COMPREHENSIVE TEST COMPLETE")
        return True
        
    except Exception as e:
        print(f"❌ Error in comprehensive test: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        try:
            if 'data_collector' in locals():
                data_collector.stop_real_time_collection()
        except:
            pass

def test_ultra_strong_scenarios(ai_manager, timeframe_groups):
    """Test ultra-strong signal scenarios."""
    print("   Testing single model ±2 signals...")
    
    # Test each timeframe group
    for group_name, models in timeframe_groups.items():
        if not models:
            continue
            
        print(f"   🔍 {group_name.upper()} GROUP:")
        
        # Test BUY and SELL for first model in group
        test_model = models[0]
        
        # Mock predictions for ultra-strong signals
        mock_predictions = {
            test_model: {
                "prediction": 2,  # STRONG BUY
                "confidence": 0.8
            }
        }
        
        # Test consensus detection
        consensus_signals = ai_manager._check_timeframe_consensus(mock_predictions, [test_model])
        ultra_strong_count = len([p for p in mock_predictions.values() if abs(p["prediction"]) == 2 and p["confidence"] >= 0.6])
        
        print(f"      📈 {test_model}: STRONG BUY (2) with 80% confidence")
        print(f"      ⚡ Ultra-strong signals: {ultra_strong_count} (should be 1)")
        print(f"      🤝 Consensus signals: {len(consensus_signals)} (should be 0)")
        
        # Test SELL scenario
        mock_predictions[test_model] = {"prediction": -2, "confidence": 0.7}
        consensus_signals = ai_manager._check_timeframe_consensus(mock_predictions, [test_model])
        ultra_strong_count = len([p for p in mock_predictions.values() if abs(p["prediction"]) == 2 and p["confidence"] >= 0.6])
        
        print(f"      📉 {test_model}: STRONG SELL (-2) with 70% confidence")
        print(f"      ⚡ Ultra-strong signals: {ultra_strong_count} (should be 1)")
        print(f"      🤝 Consensus signals: {len(consensus_signals)} (should be 0)")

def test_timeframe_consensus_scenarios(ai_manager, timeframe_groups):
    """Test timeframe consensus scenarios."""
    print("   Testing 2+ models ±1 signals in same timeframe...")
    
    for group_name, models in timeframe_groups.items():
        if len(models) < 2:
            print(f"   ⚠️  {group_name.upper()}: Only {len(models)} models, skipping consensus test")
            continue
            
        print(f"   🔍 {group_name.upper()} GROUP:")
        
        # Test BUY consensus (2 models with BUY 1)
        mock_predictions = {}
        for i, model in enumerate(models[:2]):  # Take first 2 models
            mock_predictions[model] = {
                "prediction": 1,  # BUY
                "confidence": 0.75  # Above 0.7 threshold
            }
        
        consensus_signals = ai_manager._check_timeframe_consensus(mock_predictions, models[:2])
        
        print(f"      📈 BUY Consensus Test:")
        print(f"         Models: {models[:2]}")
        print(f"         Signals: BUY (1) with 75% confidence each")
        print(f"         🤝 Consensus detected: {len(consensus_signals)} signals (should be 2)")
        
        if consensus_signals:
            for signal in consensus_signals:
                print(f"            ✅ {signal['model_name']}: {signal['signal']} (conf: {signal['confidence']:.3f})")
        
        # Test SELL consensus (2 models with SELL -1)
        for model in models[:2]:
            mock_predictions[model] = {
                "prediction": -1,  # SELL
                "confidence": 0.8   # Above 0.7 threshold
            }
        
        consensus_signals = ai_manager._check_timeframe_consensus(mock_predictions, models[:2])
        
        print(f"      📉 SELL Consensus Test:")
        print(f"         Models: {models[:2]}")
        print(f"         Signals: SELL (-1) with 80% confidence each")
        print(f"         🤝 Consensus detected: {len(consensus_signals)} signals (should be 2)")
        
        if consensus_signals:
            for signal in consensus_signals:
                print(f"            ✅ {signal['model_name']}: {signal['signal']} (conf: {signal['confidence']:.3f})")
        
        # Test mixed signals (should NOT trigger consensus)
        mock_predictions[models[0]] = {"prediction": 1, "confidence": 0.75}   # BUY
        mock_predictions[models[1]] = {"prediction": -1, "confidence": 0.75}  # SELL
        
        consensus_signals = ai_manager._check_timeframe_consensus(mock_predictions, models[:2])
        
        print(f"      🔄 Mixed Signals Test:")
        print(f"         {models[0]}: BUY (1), {models[1]}: SELL (-1)")
        print(f"         🤝 Consensus detected: {len(consensus_signals)} signals (should be 0)")

def test_weak_signal_scenarios(ai_manager, timeframe_groups):
    """Test weak signal scenarios."""
    print("   Testing ±1 signals with confidence <0.7...")
    
    for group_name, models in timeframe_groups.items():
        if not models:
            continue
            
        print(f"   🔍 {group_name.upper()} GROUP:")
        
        # Test weak signals (below consensus threshold)
        test_model = models[0]
        mock_predictions = {
            test_model: {
                "prediction": 1,  # BUY
                "confidence": 0.6  # Below 0.7 threshold
            }
        }
        
        consensus_signals = ai_manager._check_timeframe_consensus(mock_predictions, [test_model])
        
        print(f"      📈 {test_model}: BUY (1) with 60% confidence")
        print(f"      🤝 Consensus detected: {len(consensus_signals)} signals (should be 0)")
        print(f"      💡 This should go to ensemble voting")

if __name__ == "__main__":
    test_comprehensive_signal_logic()
