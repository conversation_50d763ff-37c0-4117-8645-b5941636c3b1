#!/usr/bin/env python3
"""
Check Cached EMA/SMA Data
Examine the actual EMA/SMA values from cached CSV files
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def check_timeframe_data(timeframe_min):
    """Check EMA/SMA data for a specific timeframe."""
    cache_file = f"data/synthetic_cache/DEX 900 DOWN Index_{timeframe_min}min_latest.csv"
    
    if not os.path.exists(cache_file):
        print(f"Cache file not found: {cache_file}")
        return
    
    try:
        df = pd.read_csv(cache_file)
        print(f"\n=== {timeframe_min}-MINUTE TIMEFRAME DATA ===")
        print(f"Records available: {len(df)}")
        
        if len(df) == 0:
            print("No data in cache file")
            return
        
        # Show column names
        print(f"Columns: {list(df.columns)}")
        
        # Convert timestamp if needed
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        elif 'time' in df.columns:
            df['timestamp'] = pd.to_datetime(df['time'], unit='s')
        
        # Sort by timestamp
        df = df.sort_values('timestamp')
        
        # Calculate EMA20 and SMA50
        df['ema20'] = df['close'].ewm(span=20, adjust=False).mean()
        df['sma50'] = df['close'].rolling(window=50).mean()
        
        # Get latest values
        latest = df.iloc[-1]
        print(f"Latest timestamp: {latest['timestamp']}")
        print(f"Latest close: {latest['close']:.2f}")
        
        if not pd.isna(latest['ema20']):
            print(f"Latest EMA20: {latest['ema20']:.2f}")
        else:
            print("Latest EMA20: N/A (insufficient data)")
            
        if not pd.isna(latest['sma50']):
            print(f"Latest SMA50: {latest['sma50']:.2f}")
        else:
            print("Latest SMA50: N/A (insufficient data)")
        
        # Calculate gap percentage
        if not pd.isna(latest['ema20']) and not pd.isna(latest['sma50']):
            gap_abs = abs(latest['ema20'] - latest['sma50'])
            gap_pct = gap_abs / latest['close'] * 100
            print(f"Gap absolute: {gap_abs:.2f}")
            print(f"Gap percentage: {gap_pct:.3f}%")
            
            # Compare with console output values
            if timeframe_min == 5:
                expected_ema = 55326.23
                expected_sma = 55292.01
                print(f"Expected EMA20 (from console): {expected_ema:.2f}")
                print(f"Expected SMA50 (from console): {expected_sma:.2f}")
                print(f"EMA20 difference: {abs(latest['ema20'] - expected_ema):.2f}")
                print(f"SMA50 difference: {abs(latest['sma50'] - expected_sma):.2f}")
            elif timeframe_min == 30:
                expected_ema = 55342.65
                expected_sma = 55377.35
                print(f"Expected EMA20 (from console): {expected_ema:.2f}")
                print(f"Expected SMA50 (from console): {expected_sma:.2f}")
                print(f"EMA20 difference: {abs(latest['ema20'] - expected_ema):.2f}")
                print(f"SMA50 difference: {abs(latest['sma50'] - expected_sma):.2f}")
        
        # Show last 5 records
        print("\nLast 5 records:")
        for i in range(max(0, len(df)-5), len(df)):
            row = df.iloc[i]
            ema20 = f"{row['ema20']:.2f}" if not pd.isna(row['ema20']) else 'N/A'
            sma50 = f"{row['sma50']:.2f}" if not pd.isna(row['sma50']) else 'N/A'
            print(f"{row['timestamp']} - Close: {row['close']:.2f}, EMA20: {ema20}, SMA50: {sma50}")
        
        # Check data freshness
        current_time = datetime.now()
        if 'timestamp' in df.columns:
            latest_time = pd.to_datetime(latest['timestamp'])
            if latest_time.tz is None:
                latest_time = latest_time.tz_localize('UTC')
            if current_time.tzinfo is None:
                current_time = current_time.replace(tzinfo=latest_time.tzinfo)
            time_diff = current_time - latest_time
            print(f"Data age: {time_diff}")
    
    except Exception as e:
        print(f"Error processing {timeframe_min}min data: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("=== CACHED EMA/SMA DATA ANALYSIS ===")
    
    # Check the main timeframes mentioned in the console output
    check_timeframe_data(5)   # SHORT_TERM (5min)
    check_timeframe_data(30)  # MEDIUM_TERM (30min)
    
    # Also check other available timeframes
    other_timeframes = [1, 15, 60, 240, 1440]
    for tf in other_timeframes:
        check_timeframe_data(tf)

if __name__ == "__main__":
    main()