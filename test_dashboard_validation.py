#!/usr/bin/env python3
"""
Test the dashboard validation function from SystemOrchestrator.
"""

import sys
import time
import logging

# Add current directory to path
sys.path.append('.')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dashboard_validation():
    """Test the dashboard validation function."""
    try:
        import requests

        logger.info("Testing dashboard validation logic...")

        # Try to connect to dashboard (same logic as SystemOrchestrator)
        for attempt in range(1, 6):  # 1-5 attempts
            try:
                logger.info(f"Dashboard connection attempt {attempt}/5...")
                response = requests.get('http://localhost:5000', timeout=10)
                if response.status_code == 200:
                    logger.info(f"SUCCESS: Dashboard connection validated (status: {response.status_code})")
                    logger.info(f"Dashboard response length: {len(response.text)} characters")
                    return True
                else:
                    logger.warning(f"Dashboard responded with status: {response.status_code}")
            except requests.exceptions.ConnectionError as e:
                logger.warning(f"Connection error on attempt {attempt}: {e}")
            except requests.exceptions.Timeout as e:
                logger.warning(f"Timeout error on attempt {attempt}: {e}")
            except Exception as e:
                logger.warning(f"Unexpected error on attempt {attempt}: {e}")
            
            if attempt < 5:
                logger.info("Waiting 3 seconds before next attempt...")
                time.sleep(3)

        logger.error("ERROR: Dashboard connection failed after 5 attempts")
        return False

    except ImportError:
        logger.error("ERROR: requests module not available for dashboard validation")
        return False
    except Exception as e:
        logger.error(f"Dashboard connection validation failed: {e}")
        return False

def test_system_orchestrator_validation():
    """Test using the actual SystemOrchestrator validation method."""
    try:
        from system_orchestrator import AITradingSystemOrchestrator
        
        logger.info("Testing SystemOrchestrator dashboard validation...")
        
        orchestrator = AITradingSystemOrchestrator()
        result = orchestrator.validate_dashboard_connection()
        
        if result:
            logger.info("✅ SystemOrchestrator validation PASSED")
        else:
            logger.error("❌ SystemOrchestrator validation FAILED")
        
        return result
        
    except Exception as e:
        logger.error(f"Error testing SystemOrchestrator validation: {e}")
        return False

def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("DASHBOARD VALIDATION TEST")
    logger.info("=" * 60)
    
    # Test 1: Direct validation logic
    logger.info("\n1. Testing direct validation logic...")
    direct_result = test_dashboard_validation()
    
    # Test 2: SystemOrchestrator validation
    logger.info("\n2. Testing SystemOrchestrator validation...")
    orchestrator_result = test_system_orchestrator_validation()
    
    # Results
    logger.info("\n" + "=" * 40)
    logger.info("TEST RESULTS")
    logger.info("=" * 40)
    
    logger.info(f"Direct validation: {'✅ PASSED' if direct_result else '❌ FAILED'}")
    logger.info(f"SystemOrchestrator validation: {'✅ PASSED' if orchestrator_result else '❌ FAILED'}")
    
    if direct_result and orchestrator_result:
        logger.info("\n🎉 All dashboard validation tests PASSED!")
        logger.info("The dashboard connection validation should work correctly.")
        return True
    else:
        logger.error("\n💥 Some dashboard validation tests FAILED!")
        logger.error("There may be an issue with the validation logic.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
