#!/usr/bin/env python3
"""
Quick analysis of the Excel trading data file.
"""

import pandas as pd
import sys

def main():
    try:
        print("📊 Loading Excel file...")
        df = pd.read_excel('files/ReportHistory-5407342.xlsx')
        
        print(f"✅ Loaded {len(df)} records")
        print(f"📋 Columns: {list(df.columns)}")
        
        print("\n📊 Sample data:")
        print(df.head(3).to_string())
        
        print(f"\n📈 Data summary:")
        print(df.describe())
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    main()
