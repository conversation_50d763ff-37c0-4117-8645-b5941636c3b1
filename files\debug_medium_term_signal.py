#!/usr/bin/env python3
"""
Debug why MEDIUM TERM BUY 2 signal (99.5% confidence) is not opening a trade.
Check the exact signal processing flow.
"""

import sys
import json
import os
from datetime import datetime

def check_shared_predictions_cache():
    """Check the shared predictions cache to see current model states."""
    print("🔍 CHECKING SHARED PREDICTIONS CACHE")
    print("=" * 60)
    
    cache_file = "data/shared_predictions_cache.json"
    
    if not os.path.exists(cache_file):
        print("❌ Shared predictions cache file not found!")
        return None
    
    try:
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
        
        print(f"✅ Cache file loaded")
        print(f"   Timestamp: {cache_data.get('timestamp', 'Unknown')}")
        
        # Check individual predictions
        predictions = cache_data.get('predictions', {})
        print(f"\n📊 INDIVIDUAL MODEL PREDICTIONS:")
        print("-" * 50)
        
        medium_term_models = []
        strong_signals_found = []
        
        for model_name, pred in predictions.items():
            signal = pred.get('signal', 0)
            confidence = pred.get('confidence', 0)
            
            print(f"🧠 {model_name}:")
            print(f"   Signal: {signal}")
            print(f"   Confidence: {confidence:.3f}")
            
            # Check if this is a medium term model
            if 'medium_term' in model_name.lower():
                medium_term_models.append({
                    'name': model_name,
                    'signal': signal,
                    'confidence': confidence
                })
            
            # Check for strong signals (±2 with confidence ≥0.6)
            if abs(signal) == 2 and confidence >= 0.6:
                strong_signals_found.append({
                    'name': model_name,
                    'signal': signal,
                    'confidence': confidence,
                    'timeframe': 'medium_term' if 'medium_term' in model_name.lower() else 'short_term' if 'short_term' in model_name.lower() else 'long_term'
                })
            
            print()
        
        # Check ensemble data
        ensemble_data = cache_data.get('ensemble_data', {})
        print(f"🎯 ENSEMBLE DATA:")
        print("-" * 30)
        print(f"   Ensemble Signal: {ensemble_data.get('ensemble_signal', 0)}")
        print(f"   Confidence: {ensemble_data.get('confidence', 0):.3f}")
        print(f"   Consensus: {ensemble_data.get('consensus', 'unknown')}")
        
        # Check strong signals in ensemble
        strong_signals = ensemble_data.get('strong_signals', [])
        consensus_signals = ensemble_data.get('timeframe_consensus_signals', [])
        
        print(f"\n⚡ STRONG SIGNALS IN ENSEMBLE:")
        print("-" * 40)
        if strong_signals:
            for signal in strong_signals:
                print(f"   🚀 {signal.get('model_name', 'Unknown')}")
                print(f"      Signal: {signal.get('signal', 0)}")
                print(f"      Confidence: {signal.get('confidence', 0):.3f}")
                print(f"      Timeframe: {signal.get('timeframe_category', 'Unknown')}")
                print()
        else:
            print("   ❌ No strong signals found in ensemble data")
        
        print(f"🤝 CONSENSUS SIGNALS IN ENSEMBLE:")
        print("-" * 40)
        if consensus_signals:
            for signal in consensus_signals:
                print(f"   🤝 {signal.get('model_name', 'Unknown')}")
                print(f"      Signal: {signal.get('signal', 0)}")
                print(f"      Confidence: {signal.get('confidence', 0):.3f}")
                print(f"      Timeframe: {signal.get('timeframe_category', 'Unknown')}")
                print()
        else:
            print("   ❌ No consensus signals found in ensemble data")
        
        # Analysis
        print(f"\n📋 ANALYSIS:")
        print("=" * 30)
        print(f"📊 Medium Term Models Found: {len(medium_term_models)}")
        for model in medium_term_models:
            print(f"   • {model['name']}: Signal {model['signal']} (Conf: {model['confidence']:.3f})")
        
        print(f"\n⚡ Strong Signals Found: {len(strong_signals_found)}")
        for signal in strong_signals_found:
            print(f"   • {signal['name']} ({signal['timeframe']}): Signal {signal['signal']} (Conf: {signal['confidence']:.3f})")
        
        # Check for the specific MEDIUM TERM BREAKOUT RF model
        medium_breakout = None
        for model in medium_term_models:
            if 'breakout' in model['name'].lower():
                medium_breakout = model
                break
        
        if medium_breakout:
            print(f"\n🎯 MEDIUM TERM BREAKOUT RF ANALYSIS:")
            print(f"   Signal: {medium_breakout['signal']}")
            print(f"   Confidence: {medium_breakout['confidence']:.3f}")
            
            if abs(medium_breakout['signal']) == 2 and medium_breakout['confidence'] >= 0.6:
                print(f"   ✅ SHOULD TRIGGER STRONG SIGNAL!")
                
                # Check if it's in the ensemble strong signals
                found_in_ensemble = False
                for signal in strong_signals:
                    if 'breakout' in signal.get('model_name', '').lower():
                        found_in_ensemble = True
                        break
                
                if found_in_ensemble:
                    print(f"   ✅ Found in ensemble strong signals")
                else:
                    print(f"   ❌ NOT found in ensemble strong signals - BUG!")
            else:
                print(f"   ❌ Does not meet strong signal criteria")
        else:
            print(f"\n❌ MEDIUM TERM BREAKOUT RF model not found!")
        
        return cache_data
        
    except Exception as e:
        print(f"❌ Error reading cache: {e}")
        return None

def check_recent_logs():
    """Check recent log files for signal processing information."""
    print(f"\n📋 CHECKING RECENT LOGS")
    print("=" * 40)
    
    # Check for recent log files
    log_dirs = ["logs", "logs/model_decisions", "logs/model_decisions/daily"]
    
    for log_dir in log_dirs:
        if os.path.exists(log_dir):
            print(f"✅ Found log directory: {log_dir}")
            
            # List recent files
            try:
                files = os.listdir(log_dir)
                recent_files = [f for f in files if datetime.now().strftime('%Y-%m-%d') in f]
                
                if recent_files:
                    print(f"   📄 Recent files: {recent_files}")
                else:
                    print(f"   ⚠️  No recent files found")
            except Exception as e:
                print(f"   ❌ Error listing files: {e}")
        else:
            print(f"❌ Log directory not found: {log_dir}")

def main():
    """Main debug function."""
    print("🔍 DEBUGGING MEDIUM TERM SIGNAL PROCESSING")
    print("=" * 70)
    print("Investigating why MEDIUM TERM BUY 2 (99.5% confidence) is not opening trades...")
    
    # Check shared cache
    cache_data = check_shared_predictions_cache()
    
    # Check logs
    check_recent_logs()
    
    # Recommendations
    print(f"\n💡 DEBUGGING RECOMMENDATIONS:")
    print("=" * 50)
    
    if cache_data:
        ensemble_data = cache_data.get('ensemble_data', {})
        strong_signals = ensemble_data.get('strong_signals', [])
        
        if strong_signals:
            medium_strong = [s for s in strong_signals if 'medium_term' in s.get('timeframe_category', '').lower()]
            if medium_strong:
                print(f"✅ MEDIUM TERM strong signals are being detected")
                print(f"🔍 Check console output for:")
                print(f"   • 'STRONG SIGNAL DETECTED: medium_term_breakout_rf'")
                print(f"   • 'BLOCKED: Timeframe medium_term already has active position'")
                print(f"   • Order execution success/failure messages")
            else:
                print(f"❌ MEDIUM TERM strong signals NOT being detected")
                print(f"🔧 Possible issues:")
                print(f"   • Model not producing ±2 signals")
                print(f"   • Confidence threshold not met")
                print(f"   • Timeframe categorization issue")
        else:
            print(f"❌ NO strong signals being detected at all")
            print(f"🔧 Check:")
            print(f"   • AI model predictions")
            print(f"   • Signal strength thresholds")
            print(f"   • Ensemble prediction logic")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. Monitor console output for 'STRONG SIGNAL DETECTED' messages")
    print(f"2. Check if MEDIUM TERM timeframe slot is occupied")
    print(f"3. Verify order execution system is processing the signals")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
