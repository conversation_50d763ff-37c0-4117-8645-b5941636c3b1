#!/usr/bin/env python3
"""
AI Model Performance Tracker
Comprehensive performance tracking and reporting for all 9 AI models.
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from ai_model_manager import AIModelManager
from synthetic_data_collector import Synthetic<PERSON><PERSON><PERSON>ollector
from synthetic_pattern_detector import SyntheticPatternDetector

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceTracker:
    """Comprehensive performance tracking for AI models."""
    
    def __init__(self):
        """Initialize the performance tracker."""
        self.data_collector = SyntheticDataCollector()
        self.pattern_detector = SyntheticPatternDetector(self.data_collector)
        self.ai_manager = AIModelManager(self.data_collector, self.pattern_detector)
        
    def load_all_models(self):
        """Load all 9 AI models."""
        logger.info("Loading all AI models...")
        
        loaded_count = 0
        failed_models = []
        
        for model_name in self.ai_manager.model_configs.keys():
            try:
                success = self.ai_manager.load_model(model_name)
                if success:
                    loaded_count += 1
                    logger.info(f"✅ {model_name} loaded successfully")
                else:
                    failed_models.append(model_name)
                    logger.warning(f"❌ {model_name} failed to load")
            except Exception as e:
                failed_models.append(model_name)
                logger.error(f"❌ {model_name} error: {e}")
        
        logger.info(f"Loaded {loaded_count}/{len(self.ai_manager.model_configs)} models")
        
        if failed_models:
            logger.warning(f"Failed to load: {failed_models}")
        
        return loaded_count, failed_models
    
    def get_training_performance(self):
        """Get training performance metrics for all models."""
        logger.info("Analyzing training performance...")
        
        training_metrics = {}
        
        for model_name in self.ai_manager.model_configs.keys():
            if model_name in self.ai_manager.model_performance:
                performance = self.ai_manager.model_performance[model_name]
                training_metrics[model_name] = {
                    "accuracy": performance.get("accuracy", 0.0),
                    "total_samples": performance.get("total_samples", 0),
                    "class_distribution": performance.get("class_distribution", {}),
                    "model_type": self.ai_manager.model_configs[model_name]["model_class"],
                    "purpose": self.ai_manager.model_configs[model_name]["purpose"]
                }
            else:
                training_metrics[model_name] = {
                    "accuracy": 0.0,
                    "total_samples": 0,
                    "class_distribution": {},
                    "model_type": self.ai_manager.model_configs[model_name]["model_class"],
                    "purpose": self.ai_manager.model_configs[model_name]["purpose"]
                }
        
        return training_metrics
    
    def generate_comprehensive_report(self):
        """Generate comprehensive performance report."""
        logger.info("Generating comprehensive performance report...")
        
        # Get performance report from AI manager
        report = self.ai_manager.get_performance_report()
        
        # Add training metrics
        training_metrics = self.get_training_performance()
        
        # Enhance report with training data
        for model_name, model_data in report["models"].items():
            if model_name in training_metrics:
                model_data["training_performance"] = training_metrics[model_name]
        
        return report
    
    def print_performance_summary(self, report):
        """Print a formatted performance summary."""
        print("\n" + "="*80)
        print("🎯 AI MODEL PERFORMANCE TRACKING REPORT")
        print("="*80)
        
        # Summary statistics
        summary = report.get("summary", {})
        print(f"📊 OVERALL STATISTICS:")
        print(f"   Total Models: {summary.get('total_models', 0)}")
        print(f"   Models Loaded: {summary.get('models_loaded', 0)}")
        print(f"   Models with Live Data: {summary.get('models_with_live_data', 0)}")
        
        if summary.get('average_accuracy'):
            avg_acc = summary['average_accuracy']
            print(f"   Average Live Accuracy: {avg_acc:.3f} ({avg_acc*100:.1f}%)")
        
        print(f"   Total Live Predictions: {summary.get('total_predictions', 0)}")
        
        print("\n" + "-"*80)
        print("📈 INDIVIDUAL MODEL PERFORMANCE:")
        print("-"*80)
        
        # Individual model performance
        models = report.get("models", {})
        
        for model_name, model_data in models.items():
            model_type = model_data.get("model_type", "unknown")
            purpose = model_data.get("purpose", "unknown")[:40]
            loaded = "✅" if model_data.get("loaded", False) else "❌"
            
            print(f"\n🤖 {model_name}")
            print(f"   Status: {loaded} | Type: {model_type} | Purpose: {purpose}")
            
            # Training performance
            training = model_data.get("training_performance", {})
            if training:
                train_acc = training.get("accuracy", 0)
                train_samples = training.get("total_samples", 0)
                print(f"   Training: {train_acc:.3f} accuracy ({train_acc*100:.1f}%) | {train_samples:,} samples")
            else:
                print(f"   Training: No data available")
            
            # Live performance
            live = model_data.get("live_performance", {})
            if live and live.get("total_predictions", 0) > 0:
                live_acc = live.get("accuracy", 0)
                live_preds = live.get("total_predictions", 0)
                correct = live.get("correct_predictions", 0)
                print(f"   Live: {live_acc:.3f} accuracy ({live_acc*100:.1f}%) | {correct}/{live_preds} predictions")
            else:
                print(f"   Live: No predictions yet")
            
            # Prediction statistics
            pred_stats = model_data.get("prediction_stats", {})
            if pred_stats:
                total_preds = pred_stats.get("total_predictions", 0)
                avg_conf = pred_stats.get("avg_confidence", 0)
                print(f"   Stats: {total_preds} predictions | {avg_conf:.3f} avg confidence")
        
        print("\n" + "="*80)
        
        # Performance quality assessment
        self._print_quality_assessment(report)
    
    def _print_quality_assessment(self, report):
        """Print quality assessment based on performance data."""
        print("🎯 TRAINING QUALITY ASSESSMENT:")
        print("-"*80)
        
        models = report.get("models", {})
        training_accuracies = []
        live_accuracies = []
        total_samples = 0
        
        for model_data in models.values():
            # Training accuracy
            training = model_data.get("training_performance", {})
            if training and training.get("accuracy", 0) > 0:
                training_accuracies.append(training["accuracy"])
                total_samples += training.get("total_samples", 0)
            
            # Live accuracy
            live = model_data.get("live_performance", {})
            if live and live.get("total_predictions", 0) > 0:
                live_accuracies.append(live["accuracy"])
        
        # Training assessment
        if training_accuracies:
            avg_training = np.mean(training_accuracies)
            min_training = np.min(training_accuracies)
            max_training = np.max(training_accuracies)
            
            print(f"📚 Training Performance:")
            print(f"   Average Accuracy: {avg_training:.3f} ({avg_training*100:.1f}%)")
            print(f"   Range: {min_training:.3f} - {max_training:.3f} ({min_training*100:.1f}% - {max_training*100:.1f}%)")
            print(f"   Total Training Samples: {total_samples:,}")
            
            # Quality rating
            if avg_training >= 0.7:
                quality = "🎉 EXCELLENT"
            elif avg_training >= 0.6:
                quality = "✅ GOOD"
            elif avg_training >= 0.5:
                quality = "⚠️ ACCEPTABLE"
            else:
                quality = "❌ NEEDS IMPROVEMENT"
            
            print(f"   Quality Rating: {quality}")
        else:
            print(f"📚 Training Performance: No data available")
        
        # Live assessment
        if live_accuracies:
            avg_live = np.mean(live_accuracies)
            print(f"\n🔴 Live Performance:")
            print(f"   Average Live Accuracy: {avg_live:.3f} ({avg_live*100:.1f}%)")
            print(f"   Models with Live Data: {len(live_accuracies)}")
        else:
            print(f"\n🔴 Live Performance: No live data yet")
        
        print("\n" + "="*80)
    
    def save_report(self, report, filename=None):
        """Save performance report to file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.json"
        
        try:
            import json
            
            # Convert pandas timestamps to strings for JSON serialization
            def convert_timestamps(obj):
                if isinstance(obj, pd.Timestamp):
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    return {k: convert_timestamps(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_timestamps(item) for item in obj]
                else:
                    return obj
            
            serializable_report = convert_timestamps(report)
            
            with open(filename, 'w') as f:
                json.dump(serializable_report, f, indent=2, default=str)
            
            logger.info(f"Performance report saved to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving report: {e}")
    
    def cleanup(self):
        """Cleanup resources."""
        try:
            self.data_collector.cleanup()
        except Exception as e:
            logger.warning(f"Error during cleanup: {e}")

def main():
    """Main function to run performance tracking."""
    tracker = None
    
    try:
        print("🚀 Starting AI Model Performance Tracking...")
        
        # Initialize tracker
        tracker = PerformanceTracker()
        
        # Load all models
        loaded_count, failed_models = tracker.load_all_models()
        
        if loaded_count == 0:
            print("❌ No models loaded successfully. Please train models first.")
            return False
        
        # Generate comprehensive report
        report = tracker.generate_comprehensive_report()
        
        # Print summary
        tracker.print_performance_summary(report)
        
        # Save report
        tracker.save_report(report)
        
        # Save performance data
        tracker.ai_manager.save_performance_data()
        
        print("\n✅ Performance tracking completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in performance tracking: {e}")
        return False
        
    finally:
        if tracker:
            tracker.cleanup()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
