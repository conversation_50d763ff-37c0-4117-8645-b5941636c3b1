#!/usr/bin/env python3
"""
Simple test to verify SL/TP values without full system dependencies.
"""

def test_sl_tp_values():
    """Test the exact SL/TP values for all timeframe groups."""
    
    print("🧪 TESTING TIMEFRAME-SPECIFIC SL/TP VALUES")
    print("=" * 60)
    
    # Point to dollar conversion (based on your calculation)
    POINTS_TO_DOLLAR = 0.0001  # 1 point = $0.0001, so 5000 points = $0.50
    
    print(f"📊 CONVERSION RATE: 1 point = ${POINTS_TO_DOLLAR:.4f}")
    print(f"📊 VERIFICATION: 5000 points = ${5000 * POINTS_TO_DOLLAR:.2f}")
    print(f"📊 CURRENT MEDIUM: 40000 points = ${40000 * POINTS_TO_DOLLAR:.2f}")
    print()
    
    # UPDATED TIMEFRAME-SPECIFIC SL/TP DISTANCES (in points)
    # Per user request: Much tighter SL/TP values
    timeframe_distances = {
        'short_term': {
            'sl_distance': 50.0,      # 50 points SL for quick scalping
            'tp_distance': 100.0,     # 100 points TP for quick profit
            'min_distance': 50.0      # Minimum required distance
        },
        'medium_term': {
            'sl_distance': 200.0,     # 200 points SL for swing trading
            'tp_distance': 400.0,     # 400 points TP for medium targets
            'min_distance': 200.0     # Minimum required distance
        },
        'long_term': {
            'sl_distance': 300.0,     # 300 points SL for trend following
            'tp_distance': 600.0,     # 600 points TP for larger targets
            'min_distance': 300.0     # Minimum required distance
        }
    }
    
    # Test current price (example)
    current_price = 54300.0  # Example DEX 900 DOWN price
    print(f"📈 Test Price: {current_price:.2f}")
    print()
    
    # Test all timeframe groups
    for timeframe_group, distances in timeframe_distances.items():
        print(f"🎯 {timeframe_group.upper()} TIMEFRAME")
        print("-" * 40)
        
        sl_distance = distances['sl_distance']
        tp_distance = distances['tp_distance']
        
        # Convert to dollars
        sl_dollars = sl_distance * POINTS_TO_DOLLAR
        tp_dollars = tp_distance * POINTS_TO_DOLLAR
        
        # Calculate risk/reward ratio
        risk_reward = tp_distance / sl_distance if sl_distance > 0 else 0
        
        # Calculate actual prices for BUY signal
        buy_stop_loss = current_price - sl_distance
        buy_take_profit = current_price + tp_distance
        
        # Calculate actual prices for SELL signal
        sell_stop_loss = current_price + sl_distance
        sell_take_profit = current_price - tp_distance
        
        print(f"📊 BASE DISTANCES:")
        print(f"   Stop Loss:     {sl_distance:.1f} points = ${sl_dollars:.2f}")
        print(f"   Take Profit:   {tp_distance:.1f} points = ${tp_dollars:.2f}")
        print(f"   Risk/Reward:   1:{risk_reward:.2f}")
        print()
        
        print(f"📈 BUY SIGNAL EXAMPLE:")
        print(f"   Entry Price:   {current_price:.2f}")
        print(f"   Stop Loss:     {buy_stop_loss:.2f} ({sl_distance:.1f} points below)")
        print(f"   Take Profit:   {buy_take_profit:.2f} ({tp_distance:.1f} points above)")
        print()
        
        print(f"📉 SELL SIGNAL EXAMPLE:")
        print(f"   Entry Price:   {current_price:.2f}")
        print(f"   Stop Loss:     {sell_stop_loss:.2f} ({sl_distance:.1f} points above)")
        print(f"   Take Profit:   {sell_take_profit:.2f} ({tp_distance:.1f} points below)")
        print()
        print()
    
    # Summary comparison
    print("📋 DOLLAR VALUE SUMMARY")
    print("=" * 50)
    
    for timeframe_group, distances in timeframe_distances.items():
        sl_dollars = distances['sl_distance'] * POINTS_TO_DOLLAR
        tp_dollars = distances['tp_distance'] * POINTS_TO_DOLLAR
        
        print(f"{timeframe_group.upper():12}: Risk=${sl_dollars:.2f}, Reward=${tp_dollars:.2f}")
    
    print()
    print("🔍 COMPARISON TO CURRENT MEDIUM TERM:")
    current_medium_tp = 40000  # Your current TP
    current_medium_tp_dollars = current_medium_tp * POINTS_TO_DOLLAR
    new_medium_tp = timeframe_distances['medium_term']['tp_distance']
    new_medium_tp_dollars = new_medium_tp * POINTS_TO_DOLLAR
    
    print(f"Current Medium TP: {current_medium_tp} points = ${current_medium_tp_dollars:.2f}")
    print(f"New Medium TP:     {new_medium_tp} points = ${new_medium_tp_dollars:.2f}")
    print(f"Difference:        {new_medium_tp - current_medium_tp:+.0f} points = ${(new_medium_tp - current_medium_tp) * POINTS_TO_DOLLAR:+.2f}")
    
    print()
    print("⚠️  IMPORTANT OBSERVATIONS:")
    print(f"• Short-term trades risk only ${timeframe_distances['short_term']['sl_distance'] * POINTS_TO_DOLLAR:.2f} for ${timeframe_distances['short_term']['tp_distance'] * POINTS_TO_DOLLAR:.2f} reward")
    print(f"• Medium-term trades risk ${timeframe_distances['medium_term']['sl_distance'] * POINTS_TO_DOLLAR:.2f} for ${timeframe_distances['medium_term']['tp_distance'] * POINTS_TO_DOLLAR:.2f} reward")
    print(f"• Long-term trades risk ${timeframe_distances['long_term']['sl_distance'] * POINTS_TO_DOLLAR:.2f} for ${timeframe_distances['long_term']['tp_distance'] * POINTS_TO_DOLLAR:.2f} reward")

if __name__ == "__main__":
    test_sl_tp_values()
    print("\n✅ SL/TP VALUE TEST COMPLETED")
