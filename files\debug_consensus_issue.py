#!/usr/bin/env python3
"""
Debug script to analyze why consensus signals aren't triggering trades.
"""

import sys
import os
import json
from datetime import datetime

def analyze_current_predictions():
    """Analyze the current predictions to see why consensus isn't working."""
    print("🔍 ANALYZING CURRENT CONSENSUS ISSUE")
    print("=" * 60)
    
    try:
        # Read the shared predictions cache
        cache_file = "data/shared_predictions_cache.json"
        if not os.path.exists(cache_file):
            print("❌ No shared predictions cache found")
            return False
        
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
        
        predictions = cache_data.get("predictions", {})
        ensemble_data = cache_data.get("ensemble_data", {})
        
        print("📊 CURRENT PREDICTIONS:")
        print("-" * 40)
        
        # Group by timeframe
        timeframe_groups = {
            "short_term": [],
            "medium_term": [],
            "long_term": []
        }
        
        for model_name, pred in predictions.items():
            if "short_term" in model_name:
                timeframe_groups["short_term"].append((model_name, pred))
            elif "medium_term" in model_name:
                timeframe_groups["medium_term"].append((model_name, pred))
            elif "long_term" in model_name:
                timeframe_groups["long_term"].append((model_name, pred))
        
        # Analyze each timeframe
        for timeframe, models in timeframe_groups.items():
            if not models:
                continue
                
            print(f"\n🎯 {timeframe.upper()} TIMEFRAME:")
            print("-" * 30)
            
            buy_signals = []
            sell_signals = []
            hold_signals = []
            
            for model_name, pred in models:
                signal = pred.get("signal", 0)
                confidence = pred.get("confidence", 0)
                
                signal_str = "HOLD"
                if signal == 1:
                    signal_str = "WEAK_BUY"
                    buy_signals.append((model_name, confidence))
                elif signal == -1:
                    signal_str = "WEAK_SELL"
                    sell_signals.append((model_name, confidence))
                elif signal == 2:
                    signal_str = "STRONG_BUY"
                elif signal == -2:
                    signal_str = "STRONG_SELL"
                else:
                    hold_signals.append((model_name, confidence))
                
                print(f"   {model_name}: {signal_str} (conf: {confidence:.3f})")
            
            # Check consensus requirements
            print(f"\n   📈 BUY Signals: {len(buy_signals)}")
            if buy_signals:
                for model, conf in buy_signals:
                    threshold_met = "✅" if conf >= 0.7 else "❌"
                    print(f"      {model}: {conf:.3f} {threshold_met}")
                
                if len(buy_signals) >= 2:
                    avg_confidence = sum(conf for _, conf in buy_signals) / len(buy_signals)
                    all_above_threshold = all(conf >= 0.7 for _, conf in buy_signals)
                    
                    print(f"   🎯 CONSENSUS CHECK:")
                    print(f"      Models with BUY: {len(buy_signals)} (need ≥2) {'✅' if len(buy_signals) >= 2 else '❌'}")
                    print(f"      All above 70%: {all_above_threshold} {'✅' if all_above_threshold else '❌'}")
                    print(f"      Average confidence: {avg_confidence:.3f}")
                    
                    if len(buy_signals) >= 2 and all_above_threshold:
                        print(f"   🚀 SHOULD TRIGGER BUY TRADE!")
                    else:
                        print(f"   ⏸️  CONSENSUS NOT MET")
                        if not all_above_threshold:
                            print(f"      💡 Issue: Some models below 70% confidence threshold")
            
            print(f"   📉 SELL Signals: {len(sell_signals)}")
            if sell_signals:
                for model, conf in sell_signals:
                    threshold_met = "✅" if conf >= 0.7 else "❌"
                    print(f"      {model}: {conf:.3f} {threshold_met}")
                
                if len(sell_signals) >= 2:
                    avg_confidence = sum(conf for _, conf in sell_signals) / len(sell_signals)
                    all_above_threshold = all(conf >= 0.7 for _, conf in sell_signals)
                    
                    print(f"   🎯 CONSENSUS CHECK:")
                    print(f"      Models with SELL: {len(sell_signals)} (need ≥2) {'✅' if len(sell_signals) >= 2 else '❌'}")
                    print(f"      All above 70%: {all_above_threshold} {'✅' if all_above_threshold else '❌'}")
                    print(f"      Average confidence: {avg_confidence:.3f}")
                    
                    if len(sell_signals) >= 2 and all_above_threshold:
                        print(f"   🚀 SHOULD TRIGGER SELL TRADE!")
                    else:
                        print(f"   ⏸️  CONSENSUS NOT MET")
                        if not all_above_threshold:
                            print(f"      💡 Issue: Some models below 70% confidence threshold")
        
        # Check ensemble data
        print(f"\n🎯 ENSEMBLE DATA:")
        print("-" * 30)
        strong_signals = ensemble_data.get("strong_signals", [])
        consensus_signals = ensemble_data.get("timeframe_consensus_signals", [])
        
        print(f"Strong signals (±2): {len(strong_signals)}")
        for signal in strong_signals:
            print(f"   {signal}")
        
        print(f"Consensus signals (±1): {len(consensus_signals)}")
        for signal in consensus_signals:
            print(f"   {signal}")
        
        if not strong_signals and not consensus_signals:
            print("❌ NO TRADING SIGNALS DETECTED")
            print("💡 This explains why no trades are being opened")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing predictions: {e}")
        return False

def show_trading_rules():
    """Show the current trading rules."""
    print(f"\n📋 CURRENT TRADING RULES:")
    print("=" * 40)
    
    print("⚡ ULTRA-STRONG SIGNALS (±2):")
    print("   • Single model with signal ±2")
    print("   • Confidence ≥ 60%")
    print("   • Triggers IMMEDIATE trade")
    print()
    
    print("🤝 CONSENSUS SIGNALS (±1):")
    print("   • 2+ models in same timeframe")
    print("   • All models with signal ±1")
    print("   • ALL models with confidence ≥ 70%")
    print("   • Triggers consensus trade")
    print()
    
    print("💡 POSSIBLE SOLUTIONS:")
    print("   1. Lower consensus confidence threshold from 70% to 60%")
    print("   2. Use average confidence instead of requiring all models ≥70%")
    print("   3. Wait for models to generate higher confidence signals")

def main():
    """Main analysis function."""
    success = analyze_current_predictions()
    show_trading_rules()
    
    if success:
        print(f"\n✅ ANALYSIS COMPLETE")
        print("💡 Check the consensus requirements above to see why trades aren't opening")
    else:
        print(f"\n❌ ANALYSIS FAILED")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
