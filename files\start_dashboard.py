#!/usr/bin/env python3
"""
AI Trading Dashboard Launcher
Quick launcher for the AI trading system dashboard.
"""

import sys
import os
import subprocess
import webbrowser
import time
from threading import Timer

def open_browser():
    """Open the dashboard in the default browser."""
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Dashboard opened in browser: http://localhost:5000")
    except Exception as e:
        print(f"⚠️ Could not open browser automatically: {e}")
        print("📱 Please manually open: http://localhost:5000")

def main():
    """Main launcher function."""
    print("🚀 AI TRADING DASHBOARD LAUNCHER")
    print("=" * 50)
    print("🎯 DEX 900 DOWN Index - 9 Model Ensemble")
    print("📊 Real-time monitoring and analysis")
    print("🔄 Auto-refresh every 3 minutes")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('dashboard_server.py'):
        print("❌ Error: dashboard_server.py not found!")
        print("💡 Please run this script from the AI Trading Bot directory")
        return False
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️ Warning: Virtual environment not detected")
        print("💡 Consider activating venv: .\\venv\\Scripts\\Activate.ps1")
        
        response = input("Continue anyway? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ Dashboard launch cancelled")
            return False
    
    print("\n🔧 Starting dashboard server...")
    
    try:
        # Schedule browser opening after 3 seconds
        Timer(3.0, open_browser).start()
        
        # Start the dashboard server
        subprocess.run([sys.executable, 'dashboard_server.py'], check=True)
        
    except KeyboardInterrupt:
        print("\n⚠️ Dashboard stopped by user")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting dashboard: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
