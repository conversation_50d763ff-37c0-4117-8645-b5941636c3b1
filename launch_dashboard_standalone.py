#!/usr/bin/env python3
"""
Standalone dashboard launcher that runs independently of the trading system.
This ensures the dashboard is always available even if the trading system shuts down.
"""

import sys
import time
import logging
import subprocess
import requests
import webbrowser
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StandaloneDashboardLauncher:
    def __init__(self):
        self.dashboard_process = None
        self.dashboard_port = 5000
        self.max_startup_time = 300  # 5 minutes for full initialization
        
    def is_dashboard_running(self):
        """Check if dashboard is already running."""
        try:
            response = requests.get(f'http://localhost:{self.dashboard_port}', timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_dashboard_server(self):
        """Start the dashboard server process."""
        try:
            logger.info("Starting dashboard server...")
            
            # Check if already running
            if self.is_dashboard_running():
                logger.info("Dashboard is already running!")
                return True
            
            # Start dashboard server process
            dashboard_script = Path("dashboard_server.py")
            if not dashboard_script.exists():
                logger.error("dashboard_server.py not found!")
                return False
            
            # Start the process
            self.dashboard_process = subprocess.Popen(
                [sys.executable, str(dashboard_script)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            logger.info(f"Dashboard server started with PID: {self.dashboard_process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start dashboard server: {e}")
            return False
    
    def wait_for_dashboard_ready(self):
        """Wait for dashboard to be fully ready."""
        logger.info("Waiting for dashboard to initialize...")
        logger.info("This may take 2-3 minutes while AI models load...")
        
        start_time = time.time()
        check_interval = 10  # Check every 10 seconds
        
        while time.time() - start_time < self.max_startup_time:
            # Check if process is still running
            if self.dashboard_process and self.dashboard_process.poll() is not None:
                logger.error("Dashboard process terminated unexpectedly")
                return False
            
            # Check if dashboard is responding
            if self.is_dashboard_running():
                elapsed = time.time() - start_time
                logger.info(f"✅ Dashboard ready after {elapsed:.1f} seconds!")
                return True
            
            # Show progress
            elapsed = time.time() - start_time
            remaining = self.max_startup_time - elapsed
            logger.info(f"Still initializing... ({elapsed:.0f}s elapsed, ~{remaining:.0f}s remaining)")
            
            time.sleep(check_interval)
        
        logger.error("Dashboard failed to start within timeout period")
        return False
    
    def open_dashboard_browser(self):
        """Open dashboard in browser."""
        try:
            dashboard_url = f"http://localhost:{self.dashboard_port}"
            logger.info(f"Opening dashboard in browser: {dashboard_url}")
            webbrowser.open(dashboard_url)
            return True
        except Exception as e:
            logger.error(f"Failed to open browser: {e}")
            return False
    
    def launch_dashboard(self):
        """Complete dashboard launch sequence."""
        logger.info("🚀 STANDALONE DASHBOARD LAUNCHER")
        logger.info("=" * 50)
        
        # Step 1: Start dashboard server
        if not self.start_dashboard_server():
            logger.error("❌ Failed to start dashboard server")
            return False
        
        # Step 2: Wait for dashboard to be ready
        if not self.wait_for_dashboard_ready():
            logger.error("❌ Dashboard failed to initialize")
            return False
        
        # Step 3: Open browser
        if not self.open_dashboard_browser():
            logger.warning("⚠️ Could not open browser, but dashboard is running")
        
        # Success
        logger.info("✅ DASHBOARD LAUNCHED SUCCESSFULLY!")
        logger.info("=" * 50)
        logger.info(f"Dashboard URL: http://localhost:{self.dashboard_port}")
        logger.info("The dashboard will continue running independently")
        logger.info("You can close this window - the dashboard will keep running")
        logger.info("=" * 50)
        
        return True
    
    def keep_dashboard_running(self):
        """Keep dashboard running and monitor it."""
        logger.info("Monitoring dashboard...")
        
        try:
            while True:
                # Check if process is still running
                if self.dashboard_process and self.dashboard_process.poll() is not None:
                    logger.warning("Dashboard process stopped, attempting restart...")
                    if self.start_dashboard_server():
                        logger.info("Dashboard restarted successfully")
                    else:
                        logger.error("Failed to restart dashboard")
                        break
                
                # Check if dashboard is responding
                if not self.is_dashboard_running():
                    logger.warning("Dashboard not responding, checking process...")
                
                time.sleep(30)  # Check every 30 seconds
                
        except KeyboardInterrupt:
            logger.info("Shutdown requested...")
            self.cleanup()
    
    def cleanup(self):
        """Clean up dashboard process."""
        if self.dashboard_process:
            logger.info("Stopping dashboard server...")
            self.dashboard_process.terminate()
            try:
                self.dashboard_process.wait(timeout=10)
                logger.info("Dashboard server stopped")
            except subprocess.TimeoutExpired:
                logger.warning("Dashboard server did not stop gracefully, forcing...")
                self.dashboard_process.kill()

def main():
    """Main function."""
    launcher = StandaloneDashboardLauncher()
    
    try:
        # Launch dashboard
        if launcher.launch_dashboard():
            # Keep it running
            launcher.keep_dashboard_running()
        else:
            logger.error("Failed to launch dashboard")
            return False
            
    except KeyboardInterrupt:
        logger.info("Shutdown requested by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        launcher.cleanup()
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
