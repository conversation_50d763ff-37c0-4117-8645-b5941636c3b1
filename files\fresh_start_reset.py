#!/usr/bin/env python3
"""
Fresh Start Reset Script
This script sets a fresh start time so the AI trading system only considers
trades from this moment forward, ignoring all historical P&L data.
"""

import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/fresh_start_reset.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("FreshStartReset")

def fresh_start_reset():
    """Reset system to start fresh from this moment - ignore all historical P&L."""
    print("🔄 FRESH START RESET - IGNORE HISTORICAL P&L")
    print("=" * 60)
    
    try:
        # Import required components
        from synthetic_data_collector import SyntheticDataCollector
        from order_execution_system import OrderExecutionSystem
        
        print("🔧 Initializing components...")
        
        # Initialize data collector
        data_collector = SyntheticDataCollector()
        
        # Initialize order executor
        order_executor = OrderExecutionSystem(data_collector)
        
        if not order_executor.mt5_connected:
            print("❌ MT5 connection failed")
            return False
            
        print("✅ MT5 connected")
        
        # Set fresh start time - THIS IS THE KEY CHANGE
        fresh_start_time = datetime.now()
        order_executor.set_fresh_start_time(fresh_start_time)
        
        # Also reset daily counters
        print("🔄 Resetting daily counters...")
        order_executor.daily_trade_count = 0
        order_executor.daily_pnl = 0.0
        order_executor.last_reset_date = datetime.now().date()

        # Reset timeframe-specific daily trade counters
        if hasattr(order_executor, 'timeframe_daily_trades'):
            order_executor.timeframe_daily_trades = {
                'short_term': 0,
                'medium_term': 0,
                'long_term': 0
            }
            print("✅ Timeframe daily trade counters reset")

        # Clear timeframe position tracking
        if hasattr(order_executor, 'timeframe_positions'):
            order_executor.timeframe_positions = {
                'short_term': None,
                'medium_term': None,
                'long_term': None
            }
            print("✅ Timeframe position tracking cleared")

        print("✅ Fresh start reset completed:")
        print(f"   Fresh start time: {fresh_start_time}")
        print(f"   Daily trade count: {order_executor.daily_trade_count}")
        print(f"   Daily P&L: ${order_executor.daily_pnl:.2f}")
        print(f"   Reset date: {order_executor.last_reset_date}")
        
        # Test the fresh start P&L calculation
        print("\n🧪 Testing fresh start P&L calculation...")
        test_pnl = order_executor._get_daily_closed_trades_pnl()
        print(f"   P&L from fresh start time: ${test_pnl:.2f}")
        print("   (Should be $0.00 since no trades after fresh start)")
        
        # Show timeframe daily trade counts
        if hasattr(order_executor, 'timeframe_daily_trades'):
            print("✅ Timeframe daily trade counts (unlimited):")
            for timeframe, count in order_executor.timeframe_daily_trades.items():
                print(f"   {timeframe}: {count} trades")

        # Show timeframe monthly trade counts
        if hasattr(order_executor, 'timeframe_monthly_trades'):
            print("✅ Timeframe monthly trade counts:")
            for timeframe, count in order_executor.timeframe_monthly_trades.items():
                print(f"   {timeframe}: {count} trades this month")

        # Show current account status
        print("\n💰 Current account status:")
        try:
            import MetaTrader5 as mt5
            account_info = mt5.account_info()
            
            if account_info:
                print(f"   Balance: ${account_info.balance:.2f}")
                print(f"   Equity: ${account_info.equity:.2f}")
                print(f"   Margin: ${account_info.margin:.2f}")
                print(f"   Free margin: ${account_info.margin_free:.2f}")
            else:
                print("   Could not retrieve account info")
        except Exception as e:
            print(f"   Error getting account info: {e}")

        # Check for open positions
        print("\n📊 Checking open positions...")
        try:
            positions = mt5.positions_get()
            if positions:
                print(f"   Open positions: {len(positions)}")
                for pos in positions:
                    print(f"   - {pos.symbol}: {pos.type_str} {pos.volume} lots, P&L: ${pos.profit:.2f}")
            else:
                print("   No open positions")
        except Exception as e:
            print(f"   Error checking positions: {e}")

        print("\n✅ Fresh start reset successfully!")
        print("🚀 You can now restart the trading system")
        
        return True
        
    except Exception as e:
        logger.error(f"Fresh start reset failed: {e}")
        print(f"❌ Fresh start reset failed: {e}")
        return False

def main():
    """Main function."""
    print("🔄 AI TRADING SYSTEM - FRESH START RESET")
    print("=" * 70)
    print("This script will set a fresh start time for the AI trading system.")
    print("The system will IGNORE all historical P&L and only consider trades")
    print("from this moment forward.")
    print("")
    print("Use this when:")
    print("  - You want to start completely fresh")
    print("  - Dashboard shows old P&L data you want to ignore")
    print("  - You want daily P&L to start from $0.00 right now")
    print("=" * 70)
    
    # Confirm with user
    response = input("\nDo you want to set fresh start time? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Fresh start reset cancelled by user")
        return False
        
    # Perform fresh start reset
    success = fresh_start_reset()
    
    if success:
        print("\n🎉 FRESH START RESET COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ Fresh start time has been set")
        print("✅ Daily P&L will now ignore all historical trades")
        print("✅ System will start calculating P&L from this moment")
        print("✅ Dashboard should show $0.00 daily P&L after restart")
        print("\n🚀 You can now restart the AI trading system:")
        print("   start_complete_ai_trading_system.bat")
        print("\n💡 The dashboard will show:")
        print("   - Daily P&L: $0.00 (fresh start)")
        print("   - Total P&L: Still shows all historical data")
        print("=" * 50)
    else:
        print("\n❌ FRESH START RESET FAILED!")
        print("Check the logs for detailed error information")
        
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
