#!/usr/bin/env python3
"""
Test script to verify that all refresh cycle configurations are properly set to 3 minutes.
"""

import sys
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_refresh_configuration():
    """Test that all components are configured for 3-minute refresh cycles."""
    try:
        print("🔧 TESTING REFRESH CYCLE CONFIGURATION")
        print("=" * 60)
        print("⚠️  Verifying all components use consistent 3-minute refresh timing")
        print("=" * 60)
        
        config_issues = []
        config_checks = []
        
        # Test 1: Check trading engine configuration
        print("\n🎯 TEST 1: TRADING ENGINE CONFIGURATION")
        try:
            with open('trading_engine.py', 'r') as f:
                content = f.read()
                
            # Check for 3-minute cycle references
            if 'Trading cycle: 3 minutes' in content:
                print("   ✅ Trading cycle log message: 3 minutes")
                config_checks.append("Trading cycle log: ✅")
            else:
                print("   ❌ Trading cycle log message not updated")
                config_issues.append("Trading cycle log not updated to 3 minutes")
                
            if 'sleep_time = 180 - cycle_duration' in content:
                print("   ✅ Sleep time calculation: 180 seconds (3 minutes)")
                config_checks.append("Sleep time: ✅")
            else:
                print("   ❌ Sleep time calculation not updated")
                config_issues.append("Sleep time not updated to 180 seconds")
                
            if 'longer than 3 minutes' in content:
                print("   ✅ Warning message: 3 minutes")
                config_checks.append("Warning message: ✅")
            else:
                print("   ❌ Warning message not updated")
                config_issues.append("Warning message not updated to 3 minutes")
                
        except Exception as e:
            print(f"   ❌ Error checking trading_engine.py: {e}")
            config_issues.append(f"Error reading trading_engine.py: {e}")
        
        # Test 2: Check config.py dashboard settings
        print("\n🎯 TEST 2: DASHBOARD CONFIGURATION")
        try:
            import config
            
            update_interval = config.DASHBOARD_SETTINGS.get("update_interval_seconds", 0)
            if update_interval == 30:
                print(f"   ✅ Dashboard update interval: {update_interval} seconds")
                config_checks.append("Dashboard interval: ✅")
            else:
                print(f"   ❌ Dashboard update interval: {update_interval} seconds (should be 30)")
                config_issues.append(f"Dashboard interval is {update_interval}s, should be 30s")
                
        except Exception as e:
            print(f"   ❌ Error checking config.py: {e}")
            config_issues.append(f"Error reading config.py: {e}")
        
        # Test 3: Check dashboard JavaScript
        print("\n🎯 TEST 3: DASHBOARD JAVASCRIPT CONFIGURATION")
        try:
            with open('dashboard/static/dashboard.js', 'r') as f:
                js_content = f.read()
                
            if 'this.updateInterval = 180000' in js_content:
                print("   ✅ JavaScript update interval: 180000ms (3 minutes)")
                config_checks.append("JavaScript interval: ✅")
            else:
                print("   ❌ JavaScript update interval not set to 180000ms")
                config_issues.append("JavaScript interval not set to 3 minutes")
                
        except Exception as e:
            print(f"   ❌ Error checking dashboard.js: {e}")
            config_issues.append(f"Error reading dashboard.js: {e}")
        
        # Test 4: Check live trading dashboard
        print("\n🎯 TEST 4: LIVE TRADING DASHBOARD CONFIGURATION")
        try:
            with open('live_trading_dashboard.py', 'r') as f:
                content = f.read()
                
            if 'self.stop_event.wait(30)  # Update every 30 seconds (aligned with 3-min cycles)' in content:
                print("   ✅ Live dashboard update: 30 seconds (aligned with 3-min cycles)")
                config_checks.append("Live dashboard: ✅")
            else:
                print("   ❌ Live dashboard update interval not aligned")
                config_issues.append("Live dashboard not aligned with 3-min cycles")
                
        except Exception as e:
            print(f"   ❌ Error checking live_trading_dashboard.py: {e}")
            config_issues.append(f"Error reading live_trading_dashboard.py: {e}")
        
        # Test 5: Check dashboard server
        print("\n🎯 TEST 5: DASHBOARD SERVER CONFIGURATION")
        try:
            with open('dashboard_server.py', 'r') as f:
                content = f.read()
                
            if 'time.sleep(30)  # Update every 30 seconds' in content:
                print("   ✅ Dashboard server update: 30 seconds")
                config_checks.append("Dashboard server: ✅")
            else:
                print("   ❌ Dashboard server update interval not set")
                config_issues.append("Dashboard server update interval not set to 30s")
                
        except Exception as e:
            print(f"   ❌ Error checking dashboard_server.py: {e}")
            config_issues.append(f"Error reading dashboard_server.py: {e}")
        
        # Summary
        print(f"\n📋 CONFIGURATION SUMMARY:")
        print(f"   Total checks performed: {len(config_checks) + len(config_issues)}")
        print(f"   Successful configurations: {len(config_checks)}")
        print(f"   Configuration issues: {len(config_issues)}")
        
        if config_checks:
            print(f"\n✅ WORKING CONFIGURATIONS:")
            for check in config_checks:
                print(f"   - {check}")
        
        if config_issues:
            print(f"\n❌ CONFIGURATION ISSUES:")
            for issue in config_issues:
                print(f"   - {issue}")
        
        # Overall result
        success_rate = len(config_checks) / (len(config_checks) + len(config_issues)) * 100
        
        print(f"\n🎯 OVERALL CONFIGURATION STATUS:")
        print(f"   Success rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print(f"   ✅ EXCELLENT: All major components configured for 3-minute cycles")
            result = True
        elif success_rate >= 70:
            print(f"   ⚠️  GOOD: Most components configured, minor issues remain")
            result = True
        else:
            print(f"   ❌ POOR: Significant configuration issues need attention")
            result = False
        
        print(f"\n💡 EXPECTED BEHAVIOR:")
        print(f"   - Trading engine: Analyze market every 3 minutes (180s)")
        print(f"   - Dashboard: Update data every 30 seconds")
        print(f"   - JavaScript: Refresh display every 3 minutes (180s)")
        print(f"   - Models: Reanalyze and generate new predictions every 3 minutes")
        
        return result
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 REFRESH CYCLE CONFIGURATION TEST")
    print("=" * 60)
    print("⚠️  This verifies all components use 3-minute refresh cycles")
    print("=" * 60)
    
    success = test_refresh_configuration()
    
    if success:
        print("\n🎉 REFRESH CONFIGURATION TEST PASSED!")
        print("✅ All components configured for 3-minute refresh cycles!")
        print("✅ System will now refresh models and dashboard every 3 minutes!")
    else:
        print("\n❌ REFRESH CONFIGURATION TEST FAILED!")
        print("🔧 Some components still have incorrect refresh timing.")
        
    sys.exit(0 if success else 1)
