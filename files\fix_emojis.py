#!/usr/bin/env python3
"""
Quick script to fix emoji issues in system_orchestrator.py
"""

import re

def fix_emojis():
    """Replace all emojis with safe text equivalents."""
    
    # Read the file
    with open('system_orchestrator.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define emoji replacements
    emoji_replacements = {
        '🔄': '',
        '✅': 'SUCCESS:',
        '❌': 'ERROR:',
        '⚠️': 'WARNING:',
        '💥': 'CRITICAL:',
        '📊': '',
        '🤖': '',
        '🎯': '',
        '🎉': 'SUCCESS:',
        '⏳': '',
        '🚀': '',
        '📋': '',
        '🔍': '',
        '⚡': '',
        '🧹': '',
        '🛑': '',
        '⏱️': '',
        '📈': '',
        '🛡️': '',
        '💡': '',
        '🔧': '',
        '📝': '',
        '🎊': 'SUCCESS:',
        '🌟': 'SUCCESS:',
        '💯': 'SUCCESS:',
        '🎪': 'SUCCESS:',
        '🎭': 'SUCCESS:',
        '🎨': 'SUCCESS:',
        '🎬': 'SUCCESS:',
        '🎮': 'SUCCESS:',
        '🎲': 'SUCCESS:',
        '🎸': 'SUCCESS:',
        '🎺': 'SUCCESS:',
        '🎻': 'SUCCESS:',
        '🎼': 'SUCCESS:',
        '🎵': 'SUCCESS:',
        '🎶': 'SUCCESS:',
        '🎤': 'SUCCESS:',
        '🎧': 'SUCCESS:',
        '🎙️': 'SUCCESS:',
        '🎚️': 'SUCCESS:',
        '🎛️': 'SUCCESS:',
        '📻': 'SUCCESS:',
        '📺': 'SUCCESS:',
        '📹': 'SUCCESS:',
        '📷': 'SUCCESS:',
        '📸': 'SUCCESS:',
        '📼': 'SUCCESS:',
        '💿': 'SUCCESS:',
        '💾': 'SUCCESS:',
        '💽': 'SUCCESS:',
        '🖥️': 'SUCCESS:',
        '💻': 'SUCCESS:',
        '⌨️': 'SUCCESS:',
        '🖱️': 'SUCCESS:',
        '🖲️': 'SUCCESS:',
        '💱': 'SUCCESS:',
        '💲': 'SUCCESS:',
        '💰': 'SUCCESS:',
        '💴': 'SUCCESS:',
        '💵': 'SUCCESS:',
        '💶': 'SUCCESS:',
        '💷': 'SUCCESS:',
        '💸': 'SUCCESS:',
        '💳': 'SUCCESS:',
        '💎': 'SUCCESS:',
        '⚖️': 'SUCCESS:',
        '🔨': 'SUCCESS:',
        '⛏️': 'SUCCESS:',
        '🛠️': 'SUCCESS:',
        '⚙️': 'SUCCESS:',
        '🔩': 'SUCCESS:',
        '⚗️': 'SUCCESS:',
        '🔬': 'SUCCESS:',
        '🔭': 'SUCCESS:',
        '📡': 'SUCCESS:',
        '💉': 'SUCCESS:',
        '💊': 'SUCCESS:',
        '🩹': 'SUCCESS:',
        '🩺': 'SUCCESS:',
        '🚪': 'SUCCESS:',
        '🛏️': 'SUCCESS:',
        '🛋️': 'SUCCESS:',
        '🪑': 'SUCCESS:',
        '🚽': 'SUCCESS:',
        '🚿': 'SUCCESS:',
        '🛁': 'SUCCESS:',
        '🪒': 'SUCCESS:',
        '🧴': 'SUCCESS:',
        '🧷': 'SUCCESS:',
        '🧹': 'SUCCESS:',
        '🧺': 'SUCCESS:',
        '🧻': 'SUCCESS:',
        '🪣': 'SUCCESS:',
        '🧽': 'SUCCESS:',
        '🧯': 'SUCCESS:',
        '🛒': 'SUCCESS:',
        '🚬': 'SUCCESS:',
        '⚰️': 'SUCCESS:',
        '⚱️': 'SUCCESS:',
        '🗿': 'SUCCESS:',
    }
    
    # Apply replacements
    for emoji, replacement in emoji_replacements.items():
        content = content.replace(emoji, replacement)
    
    # Clean up double spaces and extra colons
    content = re.sub(r'SUCCESS:\s*SUCCESS:', 'SUCCESS:', content)
    content = re.sub(r'ERROR:\s*ERROR:', 'ERROR:', content)
    content = re.sub(r'WARNING:\s*WARNING:', 'WARNING:', content)
    content = re.sub(r'CRITICAL:\s*CRITICAL:', 'CRITICAL:', content)
    content = re.sub(r'\s+', ' ', content)
    content = re.sub(r'SUCCESS:\s*"', 'SUCCESS: "', content)
    content = re.sub(r'ERROR:\s*"', 'ERROR: "', content)
    content = re.sub(r'WARNING:\s*"', 'WARNING: "', content)
    
    # Write back to file
    with open('system_orchestrator.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("SUCCESS: All emojis replaced with safe text equivalents")

if __name__ == "__main__":
    fix_emojis()
