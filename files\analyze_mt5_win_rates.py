#!/usr/bin/env python3
"""
Analyze MT5 trade history to calculate win rates by timeframe.
"""

import sys
import os
from datetime import datetime, timedelta
import MetaTrader5 as mt5
import pandas as pd

def connect_to_mt5():
    """Connect to MT5."""
    print("🔌 Connecting to MT5...")
    
    if not mt5.initialize():
        print("❌ MT5 initialization failed")
        return False
    
    account_info = mt5.account_info()
    if account_info is None:
        print("❌ Could not get account information")
        return False
    
    print(f"✅ Connected to MT5 Account: {account_info.login}")
    return True

def get_ai_bot_trades():
    """Get all AI bot trades from MT5 history."""
    print("\n📊 Retrieving AI bot trade history...")
    
    try:
        # Get trades from the last 30 days
        from_date = datetime.now() - timedelta(days=30)
        to_date = datetime.now()
        
        # Get all deals (completed trades)
        deals = mt5.history_deals_get(from_date, to_date)
        
        if deals is None or len(deals) == 0:
            print("❌ No trade history found")
            return []
        
        print(f"📈 Found {len(deals)} total deals in last 30 days")
        
        # Filter for AI bot trades (look for comment patterns)
        ai_bot_deals = []
        
        for deal in deals:
            comment = deal.comment if hasattr(deal, 'comment') else ""
            
            # Look for AI bot comment patterns
            if any(pattern in comment for pattern in [
                "AI_BOT_SHORT_", "AI_BOT_MEDIUM_", "AI_BOT_LONG_",
                "short_term", "medium_term", "long_term"
            ]):
                ai_bot_deals.append(deal)
        
        print(f"🤖 Found {len(ai_bot_deals)} AI bot deals")
        return ai_bot_deals
        
    except Exception as e:
        print(f"❌ Error retrieving trades: {e}")
        return []

def analyze_trades_by_timeframe(deals):
    """Analyze trades by timeframe and calculate win rates."""
    print("\n🎯 ANALYZING TRADES BY TIMEFRAME...")
    print("=" * 60)
    
    # Group trades by timeframe
    timeframe_trades = {
        "short_term": [],
        "medium_term": [],
        "long_term": [],
        "unknown": []
    }
    
    for deal in deals:
        comment = deal.comment if hasattr(deal, 'comment') else ""
        profit = deal.profit if hasattr(deal, 'profit') else 0
        
        # Determine timeframe from comment
        timeframe = "unknown"
        if "AI_BOT_SHORT_" in comment or "short_term" in comment:
            timeframe = "short_term"
        elif "AI_BOT_MEDIUM_" in comment or "medium_term" in comment:
            timeframe = "medium_term"
        elif "AI_BOT_LONG_" in comment or "long_term" in comment:
            timeframe = "long_term"
        
        timeframe_trades[timeframe].append({
            "time": datetime.fromtimestamp(deal.time),
            "profit": profit,
            "volume": deal.volume if hasattr(deal, 'volume') else 0,
            "comment": comment,
            "symbol": deal.symbol if hasattr(deal, 'symbol') else "",
            "type": "WIN" if profit > 0 else "LOSS" if profit < 0 else "BREAKEVEN"
        })
    
    # Calculate statistics for each timeframe
    results = {}
    
    for timeframe, trades in timeframe_trades.items():
        if not trades:
            continue
        
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t["profit"] > 0])
        losing_trades = len([t for t in trades if t["profit"] < 0])
        breakeven_trades = len([t for t in trades if t["profit"] == 0])
        
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        total_profit = sum(t["profit"] for t in trades)
        avg_profit = total_profit / total_trades if total_trades > 0 else 0
        
        avg_win = sum(t["profit"] for t in trades if t["profit"] > 0) / winning_trades if winning_trades > 0 else 0
        avg_loss = sum(t["profit"] for t in trades if t["profit"] < 0) / losing_trades if losing_trades > 0 else 0
        
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 and avg_loss != 0 else float('inf')
        
        results[timeframe] = {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "breakeven_trades": breakeven_trades,
            "win_rate": win_rate,
            "total_profit": total_profit,
            "avg_profit": avg_profit,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
            "profit_factor": profit_factor,
            "trades": trades
        }
    
    return results

def display_results(results):
    """Display the analysis results."""
    print("\n📊 TIMEFRAME WIN RATE ANALYSIS:")
    print("=" * 70)
    
    for timeframe, stats in results.items():
        if stats["total_trades"] == 0:
            continue
        
        print(f"\n🎯 {timeframe.upper().replace('_', ' ')} TIMEFRAME:")
        print("-" * 50)
        print(f"   📈 Total Trades: {stats['total_trades']}")
        print(f"   🏆 Winning Trades: {stats['winning_trades']}")
        print(f"   📉 Losing Trades: {stats['losing_trades']}")
        print(f"   ⚖️  Breakeven Trades: {stats['breakeven_trades']}")
        print(f"   🎯 Win Rate: {stats['win_rate']:.1f}%")
        print(f"   💰 Total P&L: ${stats['total_profit']:.2f}")
        print(f"   📊 Average P&L: ${stats['avg_profit']:.2f}")
        print(f"   📈 Average Win: ${stats['avg_win']:.2f}")
        print(f"   📉 Average Loss: ${stats['avg_loss']:.2f}")
        
        if stats['profit_factor'] != float('inf'):
            print(f"   ⚡ Profit Factor: {stats['profit_factor']:.2f}")
        else:
            print(f"   ⚡ Profit Factor: ∞ (no losses)")
        
        # Show recent trades
        if stats['trades']:
            print(f"\n   📋 Recent Trades:")
            recent_trades = sorted(stats['trades'], key=lambda x: x['time'], reverse=True)[:5]
            for trade in recent_trades:
                result_emoji = "🟢" if trade['profit'] > 0 else "🔴" if trade['profit'] < 0 else "🟡"
                print(f"      {result_emoji} {trade['time'].strftime('%Y-%m-%d %H:%M')} | ${trade['profit']:.2f} | {trade['comment'][:30]}...")

def main():
    """Main analysis function."""
    print("🔍 MT5 TRADE HISTORY ANALYSIS - WIN RATES BY TIMEFRAME")
    print("=" * 70)
    
    # Connect to MT5
    if not connect_to_mt5():
        return False
    
    try:
        # Get AI bot trades
        deals = get_ai_bot_trades()
        
        if not deals:
            print("❌ No AI bot trades found")
            print("💡 Make sure:")
            print("   • AI trading system has been running")
            print("   • Trades have AI_BOT_ comment patterns")
            print("   • Check the last 30 days of history")
            return False
        
        # Analyze by timeframe
        results = analyze_trades_by_timeframe(deals)
        
        # Display results
        display_results(results)
        
        # Summary
        print(f"\n📋 SUMMARY:")
        print("=" * 30)
        
        short_stats = results.get("short_term", {})
        medium_stats = results.get("medium_term", {})
        
        if short_stats:
            print(f"🔥 SHORT TERM Win Rate: {short_stats.get('win_rate', 0):.1f}% ({short_stats.get('total_trades', 0)} trades)")
        else:
            print(f"🔥 SHORT TERM: No trades found")
        
        if medium_stats:
            print(f"⚡ MEDIUM TERM Win Rate: {medium_stats.get('win_rate', 0):.1f}% ({medium_stats.get('total_trades', 0)} trades)")
        else:
            print(f"⚡ MEDIUM TERM: No trades found")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return False
    
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
