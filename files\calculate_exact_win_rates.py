#!/usr/bin/env python3
"""
Diagnose timeframe position tracking and check why MEDIUM TERM trades are being blocked.
"""

import sys
import os
from datetime import datetime, timedelta
import MetaTrader5 as mt5
import pandas as pd

def connect_to_mt5():
    """Connect to MT5."""
    print("🔌 Connecting to MT5...")
    
    if not mt5.initialize():
        print("❌ MT5 initialization failed")
        return False
    
    account_info = mt5.account_info()
    if account_info is None:
        print("❌ Could not get account information")
        return False
    
    print(f"✅ Connected to MT5 Account: {account_info.login}")
    return True

def get_closed_positions():
    """Get closed positions (not deals) from MT5 history."""
    print("\n📊 Retrieving closed positions from MT5...")
    
    try:
        # Get positions from the last 7 days (adjust as needed)
        from_date = datetime.now() - timedelta(days=7)
        to_date = datetime.now()
        
        # Get history of closed positions
        positions = mt5.history_orders_get(from_date, to_date)
        
        if positions is None:
            print("❌ No position history found")
            return []
        
        print(f"📈 Found {len(positions)} total orders in last 7 days")
        
        # Also try to get deals and group them by position
        deals = mt5.history_deals_get(from_date, to_date)
        
        if deals is None:
            print("❌ No deals found")
            return []
        
        print(f"📊 Found {len(deals)} deals")
        
        # Filter for AI bot deals and group by position
        ai_positions = {}
        
        for deal in deals:
            if not hasattr(deal, 'comment') or not deal.comment:
                continue
                
            comment = deal.comment
            
            # Look for AI bot comment patterns
            if any(pattern in comment for pattern in [
                "AI_BOT_SHORT_STR", "AI_BOT_MEDIUM_", "AI_BOT_LONG_"
            ]):
                position_id = deal.position_id if hasattr(deal, 'position_id') else deal.order
                
                if position_id not in ai_positions:
                    ai_positions[position_id] = []
                
                ai_positions[position_id].append({
                    'time': datetime.fromtimestamp(deal.time),
                    'type': deal.type,
                    'volume': deal.volume,
                    'price': deal.price,
                    'profit': deal.profit,
                    'comment': comment,
                    'symbol': deal.symbol
                })
        
        print(f"🤖 Found {len(ai_positions)} AI bot positions")
        return ai_positions
        
    except Exception as e:
        print(f"❌ Error retrieving positions: {e}")
        return {}

def calculate_position_results(ai_positions):
    """Calculate results for each position."""
    print("\n🎯 CALCULATING POSITION RESULTS...")
    
    timeframe_results = {
        "short_term": {"wins": 0, "losses": 0, "breakeven": 0, "total_profit": 0, "trades": []},
        "medium_term": {"wins": 0, "losses": 0, "breakeven": 0, "total_profit": 0, "trades": []},
        "long_term": {"wins": 0, "losses": 0, "breakeven": 0, "total_profit": 0, "trades": []}
    }
    
    for position_id, deals in ai_positions.items():
        if not deals:
            continue
        
        # Calculate total profit for this position
        total_profit = sum(deal['profit'] for deal in deals)
        
        # Get timeframe from comment
        comment = deals[0]['comment']
        timeframe = "unknown"
        
        if "AI_BOT_SHORT_STR" in comment:
            timeframe = "short_term"
        elif "AI_BOT_MEDIUM_" in comment:
            timeframe = "medium_term"
        elif "AI_BOT_LONG_" in comment:
            timeframe = "long_term"
        
        if timeframe == "unknown":
            continue
        
        # Classify result
        if total_profit > 0.01:  # Win (accounting for small rounding)
            timeframe_results[timeframe]["wins"] += 1
            result_type = "WIN"
        elif total_profit < -0.01:  # Loss
            timeframe_results[timeframe]["losses"] += 1
            result_type = "LOSS"
        else:  # Breakeven
            timeframe_results[timeframe]["breakeven"] += 1
            result_type = "BREAKEVEN"
        
        timeframe_results[timeframe]["total_profit"] += total_profit
        timeframe_results[timeframe]["trades"].append({
            "position_id": position_id,
            "profit": total_profit,
            "result": result_type,
            "time": deals[0]['time'],
            "comment": comment
        })
    
    return timeframe_results

def display_win_rates(results):
    """Display win rate analysis."""
    print("\n📊 EXACT WIN RATE ANALYSIS:")
    print("=" * 70)
    
    for timeframe, data in results.items():
        total_trades = data["wins"] + data["losses"] + data["breakeven"]
        
        if total_trades == 0:
            print(f"\n🎯 {timeframe.upper().replace('_', ' ')} TIMEFRAME:")
            print("   ❌ No trades found")
            continue
        
        # Calculate win rate (excluding breakeven)
        trading_trades = data["wins"] + data["losses"]  # Exclude breakeven
        win_rate = (data["wins"] / trading_trades * 100) if trading_trades > 0 else 0
        
        # Calculate overall win rate (including breakeven as neutral)
        overall_win_rate = (data["wins"] / total_trades * 100) if total_trades > 0 else 0
        
        print(f"\n🎯 {timeframe.upper().replace('_', ' ')} TIMEFRAME:")
        print("-" * 50)
        print(f"   📈 Total Trades: {total_trades}")
        print(f"   🏆 Wins: {data['wins']}")
        print(f"   📉 Losses: {data['losses']}")
        print(f"   ⚖️  Breakeven: {data['breakeven']}")
        print(f"   🎯 Win Rate (W/L only): {win_rate:.1f}%")
        print(f"   📊 Overall Win Rate: {overall_win_rate:.1f}%")
        print(f"   💰 Total P&L: ${data['total_profit']:.2f}")
        
        if trading_trades > 0:
            avg_win = sum(t['profit'] for t in data['trades'] if t['profit'] > 0) / data['wins'] if data['wins'] > 0 else 0
            avg_loss = sum(t['profit'] for t in data['trades'] if t['profit'] < 0) / data['losses'] if data['losses'] > 0 else 0
            
            print(f"   📈 Average Win: ${avg_win:.2f}")
            print(f"   📉 Average Loss: ${avg_loss:.2f}")
            
            if data['losses'] > 0 and avg_loss != 0:
                profit_factor = abs(avg_win * data['wins'] / (avg_loss * data['losses']))
                print(f"   ⚡ Profit Factor: {profit_factor:.2f}")
        
        # Show recent trades
        if data['trades']:
            print(f"\n   📋 Recent Trades:")
            recent = sorted(data['trades'], key=lambda x: x['time'], reverse=True)[:5]
            for trade in recent:
                emoji = "🟢" if trade['profit'] > 0 else "🔴" if trade['profit'] < 0 else "🟡"
                print(f"      {emoji} {trade['time'].strftime('%m-%d %H:%M')} | ${trade['profit']:.2f} | {trade['result']}")

def main():
    """Main function."""
    print("🔍 EXACT WIN RATE CALCULATION FROM MT5 HISTORY")
    print("=" * 60)
    
    if not connect_to_mt5():
        return False
    
    try:
        # Get closed positions
        ai_positions = get_closed_positions()
        
        if not ai_positions:
            print("❌ No AI bot positions found")
            print("💡 This might be because:")
            print("   • Trades are very recent (last few hours)")
            print("   • Position IDs not properly tracked")
            print("   • Need to check longer time period")
            return False
        
        # Calculate results
        results = calculate_position_results(ai_positions)
        
        # Display results
        display_win_rates(results)
        
        # Summary
        print(f"\n📋 SUMMARY:")
        print("=" * 30)
        
        short_data = results["short_term"]
        medium_data = results["medium_term"]
        
        short_total = short_data["wins"] + short_data["losses"]
        medium_total = medium_data["wins"] + medium_data["losses"]
        
        short_wr = (short_data["wins"] / short_total * 100) if short_total > 0 else 0
        medium_wr = (medium_data["wins"] / medium_total * 100) if medium_total > 0 else 0
        
        print(f"🔥 SHORT TERM Win Rate: {short_wr:.1f}% ({short_total} trades)")
        print(f"⚡ MEDIUM TERM Win Rate: {medium_wr:.1f}% ({medium_total} trades)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
