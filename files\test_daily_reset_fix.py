#!/usr/bin/env python3
"""
Test script to verify that the daily reset fix is working correctly.
This tests that emergency_stop_triggered flag is cleared during daily reset.
"""

import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

def test_emergency_stop_reset():
    """Test that emergency stop flag is reset during daily reset."""
    print("🔍 TESTING DAILY RESET FIX FOR EMERGENCY STOP")
    print("=" * 60)
    
    try:
        # Import components
        from trading_engine import TradingEngine
        from synthetic_data_collector import SyntheticDataCollector
        from order_execution_system import OrderExecutionSystem
        
        print("✅ Components imported successfully")
        
        # Test 1: Create trading engine and verify emergency flag reset method exists
        print("\n🧪 TEST 1: Trading Engine Emergency Reset Method")
        print("-" * 50)
        
        trading_engine = TradingEngine()
        
        # Check if the reset method exists
        if hasattr(trading_engine, 'reset_daily_emergency_flags'):
            print("✅ reset_daily_emergency_flags method exists")
        else:
            print("❌ reset_daily_emergency_flags method missing")
            return False
        
        # Test the method
        trading_engine.emergency_stop_triggered = True
        print(f"   Emergency stop before reset: {trading_engine.emergency_stop_triggered}")
        
        trading_engine.reset_daily_emergency_flags()
        print(f"   Emergency stop after reset: {trading_engine.emergency_stop_triggered}")
        
        if not trading_engine.emergency_stop_triggered:
            print("✅ Emergency stop flag correctly reset")
        else:
            print("❌ Emergency stop flag not reset")
            return False
        
        # Test 2: Order Execution System with Trading Engine Reference
        print("\n🧪 TEST 2: Order Execution System Integration")
        print("-" * 50)
        
        data_collector = SyntheticDataCollector()
        order_executor = OrderExecutionSystem(data_collector, trading_engine=trading_engine)
        
        # Check if trading engine reference is stored
        if order_executor.trading_engine is not None:
            print("✅ Trading engine reference stored in order executor")
        else:
            print("❌ Trading engine reference not stored")
            return False
        
        # Test 3: Simulate Daily Reset Scenario
        print("\n🧪 TEST 3: Simulated Daily Reset Scenario")
        print("-" * 50)
        
        # Set up emergency stop condition
        trading_engine.emergency_stop_triggered = True
        print(f"   1. Emergency stop triggered: {trading_engine.emergency_stop_triggered}")
        
        # Simulate daily reset by changing the last reset date
        yesterday = datetime.now().date() - timedelta(days=1)
        order_executor.last_reset_date = yesterday
        print(f"   2. Set last reset date to yesterday: {yesterday}")
        
        # Call the daily reset method (this should trigger emergency flag reset)
        order_executor._reset_daily_counters()
        print(f"   3. Called daily reset counters")
        
        # Check if emergency stop was cleared
        print(f"   4. Emergency stop after daily reset: {trading_engine.emergency_stop_triggered}")
        
        if not trading_engine.emergency_stop_triggered:
            print("✅ Emergency stop flag cleared during daily reset")
        else:
            print("❌ Emergency stop flag NOT cleared during daily reset")
            return False
        
        # Test 4: Verify Daily Counters Also Reset
        print("\n🧪 TEST 4: Daily Counters Reset Verification")
        print("-" * 50)
        
        # Set some values
        order_executor.daily_trade_count = 5
        order_executor.daily_pnl = -15.0
        print(f"   Before reset - Trade count: {order_executor.daily_trade_count}, P&L: {order_executor.daily_pnl}")
        
        # Reset again (should reset counters)
        order_executor.last_reset_date = yesterday  # Reset the date again
        order_executor._reset_daily_counters()
        
        print(f"   After reset - Trade count: {order_executor.daily_trade_count}, P&L: {order_executor.daily_pnl}")
        
        if order_executor.daily_trade_count == 0 and order_executor.daily_pnl == 0.0:
            print("✅ Daily counters correctly reset")
        else:
            print("❌ Daily counters not reset properly")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_scenario():
    """Test the complete integration scenario."""
    print("\n🎯 INTEGRATION SCENARIO TEST")
    print("=" * 60)
    print("Simulating: System hits -$10 limit → Emergency stop → Midnight reset → Trading resumes")
    print()
    
    try:
        from trading_engine import TradingEngine
        
        # Initialize trading engine (this will create order executor with reference)
        trading_engine = TradingEngine()
        
        # Don't fully initialize components (to avoid MT5 connection issues in test)
        # Just test the emergency stop reset mechanism
        
        print("📊 SCENARIO STEPS:")
        print("1. System hits daily loss limit...")
        trading_engine.emergency_stop_triggered = True
        print(f"   Emergency stop triggered: {trading_engine.emergency_stop_triggered}")
        
        print("2. Midnight arrives - daily reset occurs...")
        trading_engine.reset_daily_emergency_flags()
        print(f"   Emergency stop after reset: {trading_engine.emergency_stop_triggered}")
        
        print("3. Trading loop check...")
        can_trade = trading_engine.running and not trading_engine.emergency_stop_triggered
        print(f"   Can resume trading: {can_trade}")
        
        if not trading_engine.emergency_stop_triggered:
            print("✅ INTEGRATION TEST PASSED")
            print("   System will resume trading after daily reset")
            return True
        else:
            print("❌ INTEGRATION TEST FAILED")
            print("   System will NOT resume trading after daily reset")
            return False
            
    except Exception as e:
        print(f"❌ Integration test error: {e}")
        return False

def main():
    """Run all tests for the daily reset fix."""
    print("🎯 TESTING DAILY RESET FIX FOR EMERGENCY STOP ISSUE")
    print("=" * 80)
    print("Issue: System hits -$10 loss limit, stops trading, but doesn't resume at midnight")
    print("Fix: Clear emergency_stop_triggered flag during daily reset")
    print("=" * 80)
    
    tests = [
        ("Emergency Stop Reset", test_emergency_stop_reset),
        ("Integration Scenario", test_integration_scenario),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 TEST RESULTS SUMMARY:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Daily reset fix is working correctly.")
        print("💡 The AI trading system should now resume trading after midnight reset.")
        print("🔄 Next steps:")
        print("   1. Restart the AI trading system")
        print("   2. Test with actual daily limit scenario")
        print("   3. Verify system resumes trading at midnight")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
