#!/usr/bin/env python3
"""
Fix stuck positions that can't be closed normally.
"""

import sys
import logging
import MetaTrader5 as mt5

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_stuck_positions():
    """Fix stuck positions by force closing them."""
    try:
        logger.info("🔧 FIXING STUCK POSITIONS")
        logger.info("=" * 50)
        
        # Initialize MT5
        if not mt5.initialize():
            logger.error("MT5 initialization failed")
            return False
        
        # Get all current positions
        positions = mt5.positions_get(symbol="DEX 900 DOWN Index")
        
        if not positions:
            logger.info("No positions found")
            mt5.shutdown()
            return True
        
        logger.info(f"Found {len(positions)} positions:")

        # Include the new stuck position ID from the current error
        stuck_position_ids = [5394647405, 5394647411, 5394674933, 5394717004]  # From the error logs

        for pos in positions:
            # Get position type string
            pos_type_str = "BUY" if pos.type == mt5.ORDER_TYPE_BUY else "SELL"
            logger.info(f"Position {pos.ticket}: {pos_type_str} {pos.volume} lots, "
                       f"profit: {pos.profit:.2f}, time: {pos.time}")

            # Check if this is one of the stuck positions OR close any position that exists
            # (since we want to clear all positions to reset the system)
            if pos.ticket in stuck_position_ids or True:  # Close all positions
                logger.info(f"🎯 Found stuck position {pos.ticket}, attempting to close...")
                
                # Prepare close request with proper filling mode
                close_type = mt5.ORDER_TYPE_SELL if pos.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY
                
                request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": pos.symbol,
                    "volume": pos.volume,
                    "type": close_type,
                    "position": pos.ticket,
                    "deviation": 20,
                    "magic": 12345,
                    "comment": "Manual_Fix_Stuck_Position",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_FOK,
                }
                
                logger.info(f"Closing position {pos.ticket} with request: {request}")
                
                # Send close request
                result = mt5.order_send(request)
                
                if result:
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        logger.info(f"✅ Successfully closed position {pos.ticket}")
                    else:
                        logger.error(f"❌ Failed to close position {pos.ticket}: "
                                   f"{result.retcode} - {result.comment}")
                        
                        # Try alternative filling modes
                        for filling_mode, filling_name in [
                            (mt5.ORDER_FILLING_IOC, "Immediate or Cancel"),
                            (mt5.ORDER_FILLING_RETURN, "Return")
                        ]:
                            logger.info(f"Trying {filling_name} filling mode...")
                            request["type_filling"] = filling_mode
                            result = mt5.order_send(request)
                            
                            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                                logger.info(f"✅ Successfully closed position {pos.ticket} with {filling_name}")
                                break
                            else:
                                logger.warning(f"⚠️ {filling_name} also failed: "
                                             f"{result.retcode if result else 'No result'} - "
                                             f"{result.comment if result else 'No comment'}")
                else:
                    logger.error(f"❌ No result from close request for position {pos.ticket}")
        
        # Check remaining positions
        remaining_positions = mt5.positions_get(symbol="DEX 900 DOWN Index")
        remaining_stuck = [pos for pos in remaining_positions if pos.ticket in stuck_position_ids] if remaining_positions else []
        
        if remaining_stuck:
            logger.warning(f"⚠️ {len(remaining_stuck)} stuck positions still remain:")
            for pos in remaining_stuck:
                logger.warning(f"  Position {pos.ticket}: {pos.type_str} {pos.volume} lots, profit: {pos.profit:.2f}")
            logger.warning("These positions may need manual intervention in MT5")
        else:
            logger.info("✅ All stuck positions have been resolved!")
        
        mt5.shutdown()
        return len(remaining_stuck) == 0
        
    except Exception as e:
        logger.error(f"Error fixing stuck positions: {e}")
        return False

def clear_position_tracking():
    """Clear position tracking files to reset the system."""
    try:
        logger.info("\n🧹 CLEARING POSITION TRACKING")
        logger.info("=" * 50)
        
        import os
        import json
        
        # Files that might contain position tracking data
        tracking_files = [
            "shared_counters.json",
            "position_tracking.json",
            "active_positions.json"
        ]
        
        for filename in tracking_files:
            if os.path.exists(filename):
                try:
                    # Backup the file first
                    backup_name = f"{filename}.backup"
                    if os.path.exists(backup_name):
                        os.remove(backup_name)
                    os.rename(filename, backup_name)
                    logger.info(f"✅ Backed up and cleared {filename}")
                except Exception as e:
                    logger.warning(f"⚠️ Could not clear {filename}: {e}")
            else:
                logger.info(f"📁 {filename} not found (already clear)")
        
        # Reset shared counters
        try:
            reset_counters = {
                "timeframe_daily_trades": {"short_term": 0, "medium_term": 0, "long_term": 0},
                "timeframe_monthly_trades": {"short_term": 0, "medium_term": 0, "long_term": 0},
                "timeframe_positions": {},
                "last_reset_date": None,
                "last_monthly_reset": None
            }
            
            with open("shared_counters.json", "w") as f:
                json.dump(reset_counters, f, indent=2)
            
            logger.info("✅ Reset shared counters")
            
        except Exception as e:
            logger.warning(f"⚠️ Could not reset shared counters: {e}")
        
        logger.info("✅ Position tracking cleared!")
        return True
        
    except Exception as e:
        logger.error(f"Error clearing position tracking: {e}")
        return False

def main():
    """Main function to fix stuck positions."""
    logger.info("🚀 STUCK POSITION FIX UTILITY")
    logger.info("=" * 60)
    
    # Step 1: Try to close stuck positions
    logger.info("Step 1: Attempting to close stuck positions...")
    positions_fixed = fix_stuck_positions()
    
    # Step 2: Clear tracking data
    logger.info("\nStep 2: Clearing position tracking data...")
    tracking_cleared = clear_position_tracking()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("FIX SUMMARY")
    logger.info("=" * 60)
    
    if positions_fixed:
        logger.info("✅ Stuck positions: RESOLVED")
    else:
        logger.warning("⚠️ Stuck positions: NEED MANUAL INTERVENTION")
    
    if tracking_cleared:
        logger.info("✅ Position tracking: CLEARED")
    else:
        logger.warning("⚠️ Position tracking: PARTIAL CLEAR")
    
    if positions_fixed and tracking_cleared:
        logger.info("\n🎉 ALL ISSUES FIXED!")
        logger.info("The trading system should now work normally.")
        logger.info("You can restart the trading system.")
    else:
        logger.warning("\n⚠️ SOME ISSUES REMAIN")
        logger.warning("You may need to manually close positions in MT5.")
    
    return positions_fixed and tracking_cleared

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
