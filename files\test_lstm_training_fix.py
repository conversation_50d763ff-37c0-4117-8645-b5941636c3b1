#!/usr/bin/env python3
"""
Test the LSTM training fix to ensure it doesn't hang.
"""

import sys
import time
import numpy as np

def test_lstm_model_creation():
    """Test that LSTM model can be created without warnings."""
    print("🧪 TESTING LSTM MODEL CREATION")
    print("=" * 50)
    
    try:
        from ai_model_manager import AIModelManager
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        
        # Initialize components
        print("   Initializing components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        
        # Test LSTM model creation
        print("   Testing LSTM model creation...")
        input_features = 15  # Same as in training
        
        model = ai_manager._create_lstm_model(input_features)
        
        print(f"   ✅ LSTM model created successfully")
        print(f"      Input shape: (50, {input_features})")
        print(f"      Output classes: 5")
        print(f"      Model type: {type(model)}")
        
        # Test model summary (should not hang)
        print("   Testing model summary...")
        model.summary()
        print("   ✅ Model summary completed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_lstm_data_preparation():
    """Test LSTM data preparation with large dataset."""
    print(f"\n📊 TESTING LSTM DATA PREPARATION")
    print("=" * 50)
    
    try:
        from ai_model_manager import AIModelManager
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        
        # Create test data similar to training data
        print("   Creating test data...")
        
        # Simulate large dataset (similar to 29,255 samples)
        large_samples = 20000
        features = 15
        X_test = np.random.randn(large_samples, features)
        
        print(f"   Test data: {X_test.shape[0]} samples, {X_test.shape[1]} features")
        
        # Test data preparation
        print("   Testing LSTM data preparation...")
        start_time = time.time()
        
        X_lstm = ai_manager._prepare_lstm_data(X_test)
        
        prep_time = time.time() - start_time
        
        print(f"   ✅ Data preparation completed")
        print(f"      Preparation time: {prep_time:.2f} seconds")
        print(f"      Input shape: {X_test.shape}")
        print(f"      LSTM shape: {X_lstm.shape}")
        print(f"      Memory optimization: {'Applied' if X_lstm.shape[0] < X_test.shape[0] else 'Not needed'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_small_lstm_training():
    """Test actual LSTM training with small dataset."""
    print(f"\n🏋️ TESTING SMALL LSTM TRAINING")
    print("=" * 50)
    
    try:
        from ai_model_manager import AIModelManager
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        
        # Create small test dataset
        print("   Creating small test dataset...")
        samples = 1000  # Small dataset for quick test
        features = 15
        X_test = np.random.randn(samples, features)
        y_test = np.random.randint(0, 5, samples)  # 5 classes: 0,1,2,3,4
        
        print(f"   Test data: {X_test.shape[0]} samples, {X_test.shape[1]} features")
        
        # Create and prepare model
        print("   Creating LSTM model...")
        model = ai_manager._create_lstm_model(features)
        
        print("   Preparing LSTM data...")
        X_lstm = ai_manager._prepare_lstm_data(X_test)
        
        # Adjust y to match X_lstm length
        y_lstm = y_test[-len(X_lstm):]
        
        print(f"   Training data shape: {X_lstm.shape}")
        print(f"   Training labels shape: {y_lstm.shape}")
        
        # Test training (just 1 epoch for speed)
        print("   Testing LSTM training (1 epoch)...")
        start_time = time.time()
        
        history = model.fit(X_lstm, y_lstm, epochs=1, batch_size=32, verbose=1, validation_split=0.2)
        
        training_time = time.time() - start_time
        
        print(f"   ✅ Training completed successfully")
        print(f"      Training time: {training_time:.2f} seconds")
        print(f"      Final loss: {history.history['loss'][-1]:.4f}")
        print(f"      Final accuracy: {history.history['accuracy'][-1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔧 LSTM TRAINING FIX VERIFICATION")
    print("=" * 70)
    
    # Test model creation
    creation_test = test_lstm_model_creation()
    
    # Test data preparation
    data_prep_test = test_lstm_data_preparation()
    
    # Test small training
    training_test = test_small_lstm_training()
    
    print(f"\n🎯 TEST RESULTS:")
    print("=" * 40)
    
    if creation_test and data_prep_test and training_test:
        print("🎉 ALL TESTS PASSED!")
        print("\n💡 WHAT THIS MEANS:")
        print("✅ LSTM model creation fixed (no input_shape warning)")
        print("✅ Data preparation optimized for large datasets")
        print("✅ Training should not hang anymore")
        print("✅ Memory usage optimized")
        
        print(f"\n📋 IMPROVEMENTS MADE:")
        print("• Fixed Keras input_shape warning with Input layer")
        print("• Reduced model size for stability")
        print("• Added dataset size optimization")
        print("• Improved memory management")
        print("• Added progress logging")
        
        return True
    else:
        print("❌ SOME TESTS FAILED")
        print("Check the error messages above for details")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
