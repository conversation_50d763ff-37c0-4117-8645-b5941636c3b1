#!/usr/bin/env python3
"""
<PERSON><PERSON>t to relax trading criteria if no trades are generated overnight.
This will make the AI trading system less strict and more likely to find opportunities.
"""

import sys
import os

def relax_trading_criteria():
    """Relax the trading criteria to generate more trading opportunities."""
    try:
        print("🔧 RELAXING TRADING CRITERIA")
        print("=" * 50)
        print("📊 Making the AI trading system less strict")
        print("=" * 50)
        
        # Read current config.py
        config_path = "config.py"
        if not os.path.exists(config_path):
            print(f"❌ Config file not found: {config_path}")
            return False
        
        with open(config_path, 'r') as f:
            config_content = f.read()
        
        print("📝 Current criteria:")
        
        # Show current values
        if "MIN_SIGNAL_CONFIDENCE = 0.6" in config_content:
            print("   MIN_SIGNAL_CONFIDENCE: 60% (STRICT)")
        if "MIN_RISK_REWARD_RATIO = 1.5" in config_content:
            print("   MIN_RISK_REWARD_RATIO: 1.5:1 (STRICT)")
        
        # Make the changes
        print("\n🔧 Applying relaxed criteria...")
        
        # Relax confidence threshold
        config_content = config_content.replace(
            "MIN_SIGNAL_CONFIDENCE = 0.6",
            "MIN_SIGNAL_CONFIDENCE = 0.5"
        )
        
        # Relax risk-reward ratio
        config_content = config_content.replace(
            "MIN_RISK_REWARD_RATIO = 1.5",
            "MIN_RISK_REWARD_RATIO = 1.2"
        )
        
        # Write updated config
        with open(config_path, 'w') as f:
            f.write(config_content)
        
        print("✅ Updated config.py:")
        print("   MIN_SIGNAL_CONFIDENCE: 50% (was 60%)")
        print("   MIN_RISK_REWARD_RATIO: 1.2:1 (was 1.5:1)")
        
        # Also update trading_signal_generator.py
        signal_gen_path = "trading_signal_generator.py"
        if os.path.exists(signal_gen_path):
            with open(signal_gen_path, 'r') as f:
                signal_content = f.read()
            
            # Relax consensus strength
            signal_content = signal_content.replace(
                "self.min_consensus_strength = 0.6",
                "self.min_consensus_strength = 0.5"
            )
            
            with open(signal_gen_path, 'w') as f:
                f.write(signal_content)
            
            print("✅ Updated trading_signal_generator.py:")
            print("   min_consensus_strength: 50% (was 60%)")
        
        print("\n📈 EXPECTED IMPACT:")
        print("   - More trading signals generated")
        print("   - Increased from ~0.1 to ~1-2 trades per day")
        print("   - Still maintains risk management")
        print("   - Models will be more active")
        
        print("\n⚠️  RESTART REQUIRED:")
        print("   Please restart the AI trading system for changes to take effect")
        
        return True
        
    except Exception as e:
        print(f"❌ Error relaxing criteria: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_aggressive_relaxation():
    """Create even more relaxed criteria if moderate doesn't work."""
    try:
        print("\n🚨 CREATING AGGRESSIVE RELAXATION OPTION")
        print("=" * 50)
        
        # Create backup of current config
        import shutil
        shutil.copy("config.py", "config_backup.py")
        print("✅ Backup created: config_backup.py")
        
        # Read config
        with open("config.py", 'r') as f:
            config_content = f.read()
        
        # Apply aggressive changes
        config_content = config_content.replace(
            "MIN_SIGNAL_CONFIDENCE = 0.5",
            "MIN_SIGNAL_CONFIDENCE = 0.4"
        )
        config_content = config_content.replace(
            "MIN_RISK_REWARD_RATIO = 1.2",
            "MIN_RISK_REWARD_RATIO = 1.1"
        )
        
        # Save as aggressive config
        with open("config_aggressive.py", 'w') as f:
            f.write(config_content)
        
        print("✅ Created config_aggressive.py with:")
        print("   MIN_SIGNAL_CONFIDENCE: 40%")
        print("   MIN_RISK_REWARD_RATIO: 1.1:1")
        print("\n💡 To use: rename config_aggressive.py to config.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating aggressive config: {e}")
        return False

if __name__ == "__main__":
    print("🔧 TRADING CRITERIA RELAXATION TOOL")
    print("=" * 60)
    print("📊 This will make your AI trading system less strict")
    print("=" * 60)
    
    # Apply moderate relaxation
    success = relax_trading_criteria()
    
    if success:
        # Also create aggressive option
        create_aggressive_relaxation()
        
        print("\n🎉 CRITERIA RELAXATION COMPLETED!")
        print("✅ Moderate relaxation applied")
        print("✅ Aggressive option created")
        print("\n🔄 NEXT STEPS:")
        print("1. Restart the AI trading system")
        print("2. Monitor for increased trading activity")
        print("3. If still no trades, use config_aggressive.py")
    else:
        print("\n❌ RELAXATION FAILED!")
        print("🔧 Manual adjustment may be required")
        
    sys.exit(0 if success else 1)
