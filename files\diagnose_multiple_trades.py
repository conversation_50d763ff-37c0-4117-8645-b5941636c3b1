#!/usr/bin/env python3
"""
Diagnostic script to understand why multiple trades were opened.
"""

def analyze_trade_data():
    """Analyze the trade data to understand the multiple trade issue."""
    
    print("🔍 ANALYZING MULTIPLE TRADE ISSUE")
    print("=" * 50)
    
    # Your actual trade data
    trades = [
        {
            "ticket": 577571624,
            "time": "2025.06.02 15:58:04",
            "type": "buy",
            "volume": 0.01,
            "price": 54134.12,
            "sl": 52756.12,
            "tp": 59359.12,
            "comment": "AI_BOT_STRONG_BUY"
        },
        {
            "ticket": 577571624,  # Same ticket? Or different?
            "time": "2025.06.02 15:58:04",  # Same time?
            "type": "buy", 
            "volume": 0.01,
            "price": 54154.46,
            "sl": 53159.34,
            "tp": 59183.34,
            "comment": "AI_BOT_STRONG_BUY"
        }
    ]
    
    print("📊 TRADE ANALYSIS:")
    for i, trade in enumerate(trades, 1):
        print(f"\n🔸 TRADE {i}:")
        print(f"   Entry Price: {trade['price']:.2f}")
        print(f"   Stop Loss:   {trade['sl']:.2f}")
        print(f"   Take Profit: {trade['tp']:.2f}")
        print(f"   Comment:     {trade['comment']}")
        
        # Calculate SL/TP distances
        sl_distance = abs(trade['price'] - trade['sl'])
        tp_distance = abs(trade['tp'] - trade['price'])
        
        print(f"   SL Distance: {sl_distance:.2f} points")
        print(f"   TP Distance: {tp_distance:.2f} points")
        
        # Convert to dollars (5000 points = $0.50)
        sl_dollars = sl_distance * 0.0001
        tp_dollars = tp_distance * 0.0001
        
        print(f"   SL Risk:     ${sl_dollars:.2f}")
        print(f"   TP Reward:   ${tp_dollars:.2f}")
        
        # Determine likely timeframe based on SL/TP values
        if sl_distance < 10000:  # Less than $1.00
            likely_timeframe = "SHORT_TERM"
        elif sl_distance < 25000:  # Less than $2.50
            likely_timeframe = "MEDIUM_TERM"
        else:
            likely_timeframe = "LONG_TERM"
            
        print(f"   Likely TF:   {likely_timeframe}")
    
    # Compare the trades
    print(f"\n🔍 COMPARISON:")
    price_diff = abs(trades[0]['price'] - trades[1]['price'])
    sl_diff = abs(trades[0]['sl'] - trades[1]['sl'])
    tp_diff = abs(trades[0]['tp'] - trades[1]['tp'])
    
    print(f"   Price Difference: {price_diff:.2f} points")
    print(f"   SL Difference:    {sl_diff:.2f} points") 
    print(f"   TP Difference:    {tp_diff:.2f} points")
    
    # Analyze SL/TP patterns
    trade1_sl_dist = abs(trades[0]['price'] - trades[0]['sl'])
    trade1_tp_dist = abs(trades[0]['tp'] - trades[0]['price'])
    trade2_sl_dist = abs(trades[1]['price'] - trades[1]['sl'])
    trade2_tp_dist = abs(trades[1]['tp'] - trades[1]['price'])
    
    print(f"\n📏 SL/TP DISTANCE ANALYSIS:")
    print(f"   Trade 1: SL={trade1_sl_dist:.0f}pts, TP={trade1_tp_dist:.0f}pts")
    print(f"   Trade 2: SL={trade2_sl_dist:.0f}pts, TP={trade2_tp_dist:.0f}pts")
    
    # Check against our timeframe-specific values
    timeframe_distances = {
        'short_term': {'sl': 5000, 'tp': 10000},
        'medium_term': {'sl': 20000, 'tp': 40000},
        'long_term': {'sl': 30000, 'tp': 60000}
    }
    
    print(f"\n🎯 TIMEFRAME MATCHING:")
    for i, trade in enumerate([trade1_sl_dist, trade2_sl_dist], 1):
        closest_match = None
        min_diff = float('inf')
        
        for tf, distances in timeframe_distances.items():
            sl_diff = abs(trade - distances['sl'])
            if sl_diff < min_diff:
                min_diff = sl_diff
                closest_match = tf
                
        print(f"   Trade {i}: Closest match = {closest_match} (diff: {min_diff:.0f}pts)")
    
    print(f"\n🚨 POSSIBLE CAUSES:")
    
    # Same timeframe check
    if abs(trade1_sl_dist - trade2_sl_dist) < 1000:  # Within 1000 points
        print("   ❌ SAME TIMEFRAME: Both trades appear to be from same timeframe group!")
        print("      → Position limiting failed")
        print("      → Race condition in signal generation")
        print("      → Timeframe detection issue")
    else:
        print("   ✅ DIFFERENT TIMEFRAMES: Trades appear to be from different timeframe groups")
        print("      → This might be expected behavior")
        print("      → Check if you want to limit to ONE trade total vs ONE per timeframe")
    
    # Time analysis
    if trades[0]['time'] == trades[1]['time']:
        print("   ⚠️  SAME TIMESTAMP: Both trades at exact same time")
        print("      → Rapid signal generation")
        print("      → Multiple models triggered simultaneously")
    
    # Comment analysis
    if trades[0]['comment'] == trades[1]['comment']:
        print("   ⚠️  SAME COMMENT: Both trades have same signal type")
        print("      → Multiple strong signals generated")
        print("      → Check signal generation logic")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print("   1. Check if you want ONE total trade or ONE per timeframe")
    print("   2. Add signal generation position checking")
    print("   3. Implement signal deduplication")
    print("   4. Add minimum time between signals")
    print("   5. Review timeframe detection logic")

if __name__ == "__main__":
    analyze_trade_data()
    print("\n✅ ANALYSIS COMPLETED")
