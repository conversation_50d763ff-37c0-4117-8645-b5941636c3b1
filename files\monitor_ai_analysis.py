#!/usr/bin/env python3
"""
AI Model Analysis Monitor
Real-time monitoring of AI model re-analysis activity.
"""

import time
import logging
from datetime import datetime
from typing import Dict, List

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class AIAnalysisMonitor:
    """Monitor AI model analysis activity."""
    
    def __init__(self):
        self.last_analysis_time = None
        self.analysis_count = 0
        self.model_predictions = {}
        self.ensemble_history = []
        
    def log_model_analysis(self, model_name: str, prediction: Dict):
        """Log individual model analysis."""
        timestamp = datetime.now()
        
        print(f"🧠 MODEL ANALYSIS: {model_name}")
        print(f"   ⏰ Time: {timestamp.strftime('%H:%M:%S')}")
        print(f"   📊 Signal: {prediction.get('signal', 'N/A')}")
        print(f"   🎯 Confidence: {prediction.get('confidence', 0):.3f}")
        print(f"   🔧 Model Type: {prediction.get('model_type', 'Unknown')}")
        print(f"   📝 Purpose: {prediction.get('purpose', 'Unknown')}")
        print()
        
        # Store for tracking
        self.model_predictions[model_name] = {
            'timestamp': timestamp,
            'prediction': prediction
        }
        
    def log_ensemble_analysis(self, ensemble_result: Dict):
        """Log ensemble analysis."""
        timestamp = datetime.now()
        
        print("🎯 ENSEMBLE ANALYSIS")
        print(f"   ⏰ Time: {timestamp.strftime('%H:%M:%S')}")
        print(f"   🔄 Consensus: {ensemble_result.get('consensus', 'Unknown')}")
        print(f"   💪 Strength: {ensemble_result.get('confidence', 0):.3f}")
        print(f"   📊 Signal: {ensemble_result.get('ensemble_signal', 0)}")
        print(f"   🤖 Models: {ensemble_result.get('total_models', 0)}")
        
        # Show individual model contributions
        predictions = ensemble_result.get('predictions', {})
        if predictions:
            print("   📋 Model Contributions:")
            for model_name, pred_data in predictions.items():
                signal = pred_data.get('prediction', 0)
                confidence = pred_data.get('confidence', 0)
                print(f"      • {model_name}: {signal} ({confidence:.3f})")
        
        print("=" * 60)
        
        # Store for tracking
        self.ensemble_history.append({
            'timestamp': timestamp,
            'result': ensemble_result
        })
        
        # Keep only last 10 ensemble results
        if len(self.ensemble_history) > 10:
            self.ensemble_history = self.ensemble_history[-10:]
            
    def log_trading_cycle(self, cycle_number: int):
        """Log trading cycle start."""
        timestamp = datetime.now()
        
        if self.last_analysis_time:
            interval = (timestamp - self.last_analysis_time).total_seconds()
            print(f"🔄 TRADING CYCLE #{cycle_number}")
            print(f"   ⏰ Time: {timestamp.strftime('%H:%M:%S')}")
            print(f"   ⏱️  Interval: {interval:.1f} seconds since last cycle")
        else:
            print(f"🔄 TRADING CYCLE #{cycle_number} - FIRST CYCLE")
            print(f"   ⏰ Time: {timestamp.strftime('%H:%M:%S')}")
            
        self.last_analysis_time = timestamp
        self.analysis_count += 1
        
    def show_analysis_summary(self):
        """Show summary of AI analysis activity."""
        print("\n📊 AI ANALYSIS SUMMARY")
        print("=" * 40)
        print(f"Total Cycles: {self.analysis_count}")
        print(f"Models Analyzed: {len(self.model_predictions)}")
        print(f"Ensemble Predictions: {len(self.ensemble_history)}")
        
        if self.last_analysis_time:
            time_since = (datetime.now() - self.last_analysis_time).total_seconds()
            print(f"Last Analysis: {time_since:.1f} seconds ago")
            
        # Show recent model activity
        if self.model_predictions:
            print("\n🧠 Recent Model Activity:")
            for model_name, data in self.model_predictions.items():
                pred = data['prediction']
                age = (datetime.now() - data['timestamp']).total_seconds()
                print(f"   • {model_name}: Signal {pred.get('signal', 0)} ({age:.0f}s ago)")
                
        print("=" * 40)

def monitor_ai_system():
    """Monitor the AI trading system for analysis activity."""
    monitor = AIAnalysisMonitor()
    
    print("🔍 AI MODEL ANALYSIS MONITOR")
    print("=" * 60)
    print("Monitoring AI model re-analysis activity...")
    print("This will show when models analyze data every 3 minutes")
    print("Press Ctrl+C to stop monitoring")
    print("=" * 60)
    
    try:
        # Import trading components
        from ai_model_manager import AIModelManager
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from trading_signal_generator import TradingSignalGenerator
        
        # Initialize components
        print("🔧 Initializing AI components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        
        # Load models
        print("📚 Loading AI models...")
        model_status = ai_manager.get_model_status()
        loaded_models = model_status.get('loaded_models', [])
        
        if not loaded_models:
            print("⚠️ No models loaded, attempting to load saved models...")
            for model_name in ai_manager.model_configs.keys():
                if ai_manager.load_model(model_name):
                    loaded_models.append(model_name)
                    
        print(f"✅ {len(loaded_models)} models loaded: {loaded_models}")
        print("🔄 Starting monitoring loop...")
        print("=" * 60)
        
        cycle_count = 0
        
        while True:
            try:
                cycle_count += 1
                monitor.log_trading_cycle(cycle_count)
                
                # Get current price for analysis
                current_price = 53727.26  # Default price for testing
                
                # Generate signal (this triggers AI model analysis)
                print("⚡ Triggering AI model analysis...")
                signal = signal_generator.generate_signal(current_price)
                
                if signal:
                    print(f"📈 SIGNAL GENERATED: {signal.signal_type.name}")
                    print(f"   🎯 Confidence: {signal.confidence:.3f}")
                    print(f"   💰 Entry: {signal.entry_price:.2f}")
                    print(f"   🛡️ Stop Loss: {signal.stop_loss:.2f}")
                    print(f"   🎯 Take Profit: {signal.take_profit:.2f}")
                    print(f"   📊 R/R Ratio: {signal.risk_reward_ratio:.2f}")
                    print(f"   🧠 AI Predictions: {len(signal.ai_predictions.get('predictions', {}))}")
                else:
                    print("📊 No trading signal generated (normal behavior)")
                    
                print()
                
                # Show summary every 5 cycles
                if cycle_count % 5 == 0:
                    monitor.show_analysis_summary()
                    
                # Wait for next cycle (3 minutes = 180 seconds)
                print(f"⏳ Waiting 180 seconds for next analysis cycle...")
                time.sleep(180)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Error in monitoring cycle: {e}")
                time.sleep(30)  # Wait 30 seconds before retrying
                
    except Exception as e:
        print(f"❌ Error initializing monitor: {e}")
        return False
        
    finally:
        monitor.show_analysis_summary()
        print("\n🔍 AI Analysis monitoring stopped")
        
    return True

if __name__ == "__main__":
    monitor_ai_system()
