# Synthetic DEX 900 DOWN AI Trading System v2.0

A sophisticated AI trading system specifically designed for Deriv's Synthetic DEX 900 DOWN Index. This system focuses on algorithmic pattern recognition and ultra-fast execution optimized for synthetic market behavior.

## 🎯 System Overview

This system is built from the ground up to trade Deriv's synthetic algorithmic instrument, focusing on:

- **Pattern Recognition**: Detects jump/drop events and volatility regimes
- **Algorithmic Behavior Analysis**: Learns from Deriv's synthetic algorithm patterns
- **Ultra-Fast Execution**: Optimized for 179ms latency trading
- **Risk Management**: Ultra-aggressive position sizing and stop-loss management
- **Real-Time Monitoring**: Continuous analysis and decision making

## 🏗️ Architecture

### Phase 1: Data Collection & Management ✅
- **Synthetic Data Collector**: Specialized MT5 integration for synthetic data
- **Pattern Database**: SQLite storage for efficient data retrieval
- **Real-Time Streaming**: Continuous tick and OHLCV data collection
- **Historical Analysis**: Complete data from April 1, 2023

### Phase 2: AI Pattern Detection ✅
- **Regime Detection**: QUIET, PRE_JUMP, JUMPING, POST_JUMP, REVERTING
- **Jump Event Analysis**: Algorithmic volatility event detection
- **Synthetic Indicators**: Custom indicators for synthetic trading
- **Pattern Similarity**: Historical pattern matching

### Phase 3: Trading Engine (Next Phase)
- **Multi-Model Ensemble**: 9 specialized AI models
- **Risk Management**: Dynamic position sizing and stops
- **Order Execution**: MT5 integration with error handling
- **Performance Tracking**: Real-time P&L and metrics

### Phase 4: Dashboard & Monitoring (Next Phase)
- **Real-Time Visualization**: Live market data and analysis
- **Model Insights**: See what each AI model is thinking
- **Performance Dashboard**: Comprehensive trading metrics
- **Alert System**: Critical event notifications

## 🚀 Quick Start

### Prerequisites

1. **MetaTrader 5** installed and logged into your demo account
2. **Python 3.8+** installed
3. **DEX 900 DOWN Index** symbol available in MT5

### Installation

1. **Clone or download** this repository to your trading directory

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Create directories** (automatic on first run):
   ```
   data/
   logs/
   models/
   reports/
   dashboard/
   ```

### First Run - System Test

1. **Run the system test** to verify everything works:
   ```bash
   python test_system.py
   ```

   This will test:
   - MT5 connection
   - Symbol availability
   - Data collection
   - Pattern detection
   - Database operations

2. **Expected output**:
   ```
   ✓ Configuration: PASS
   ✓ Directories: PASS
   ✓ MT5 Connection: PASS
   ✓ Data Collection: PASS
   ✓ Pattern Detection: PASS
   ✓ Database Operations: PASS
   
   Overall: 6/6 tests passed
   🎉 All tests passed! System is ready for operation.
   ```

### Running the System

1. **Start the main system**:
   ```bash
   python synthetic_trading_system.py
   ```

2. **What happens**:
   - Connects to MT5
   - Collects historical data (may take 10-15 minutes first time)
   - Analyzes patterns in historical data
   - Starts real-time monitoring
   - Logs analysis every 5 seconds

3. **Monitor the logs**:
   - Console output shows real-time status
   - `logs/synthetic_trading_system.log` contains detailed logs
   - `logs/synthetic_data_collector.log` contains data collection logs

## 📊 Understanding the Output

### Regime States
- **QUIET**: Low volatility, range trading opportunities
- **PRE_JUMP**: Volatility compression, jump may be imminent
- **JUMPING**: High volatility event in progress
- **POST_JUMP**: After volatility event, potential reversion
- **REVERTING**: Mean reversion in progress

### Key Indicators
- **Jumpiness Score**: 0-1, likelihood of imminent jump (>0.7 = high)
- **Volatility Compression**: 0-1, how compressed volatility is
- **Price Acceleration**: Rate of price change acceleration
- **Tick Velocity**: Speed of price movements

### Sample Output
```
2024-01-15 10:30:15 - Current regime: QUIET, Active signals: 1
2024-01-15 10:30:20 - Current regime: PRE_JUMP, Active signals: 2
2024-01-15 10:30:25 - Current regime: JUMPING, Active signals: 3
```

## ⚙️ Configuration

### Key Settings in `config.py`

```python
# Risk Management
SYNTHETIC_RISK_RULES = {
    "position_sizing": {
        "base_risk_per_trade": 0.005,  # 0.5% per trade
        "max_position_size": 0.01      # 1% maximum
    },
    "circuit_breakers": {
        "max_consecutive_losses": 3,
        "max_daily_drawdown": 0.02     # 2% daily limit
    }
}

# Execution Settings
EXECUTION_SETTINGS = {
    "latency_ms": 179,                 # Your demo account latency
    "order_timeout_ms": 5000,          # 5 second timeout
    "slippage_tolerance": 0.0001       # 1 pip tolerance
}
```

## 🔧 Troubleshooting

### Common Issues

1. **"Symbol DEX 900 DOWN Index not found"**
   - Check the exact symbol name in MT5
   - Try variations like "DEX900DOWN", "DEX_900_DOWN", etc.
   - The system will show available symbols if not found

2. **"MT5 initialization failed"**
   - Ensure MT5 is running and logged in
   - Check if MT5 allows automated trading
   - Restart MT5 and try again

3. **"No data retrieved"**
   - Symbol might not have recent data
   - Check if markets are open
   - Verify symbol is active in MT5

4. **Database errors**
   - Delete `data/synthetic_cache/synthetic_data.db` to reset
   - Check disk space
   - Ensure write permissions

### Getting Help

1. **Check logs** in the `logs/` directory for detailed error messages
2. **Run test script** to isolate issues: `python test_system.py`
3. **Verify MT5 connection** manually before running the system

## 📈 Next Steps

### Phase 2: AI Models (Coming Next)
- Train 9 specialized AI models for different timeframes
- Implement ensemble decision making
- Add reinforcement learning trader

### Phase 3: Live Trading (Future)
- Real order execution
- Position management
- Live P&L tracking

### Phase 4: Dashboard (Future)
- Web-based real-time dashboard
- Visual pattern recognition
- Performance analytics

## ⚠️ Important Notes

1. **Demo Account Only**: This system is currently configured for demo trading only
2. **Synthetic Market**: Designed specifically for Deriv's synthetic instruments
3. **Pattern Learning**: The system learns from historical patterns - more data = better performance
4. **Risk Management**: Ultra-aggressive risk rules are built-in for synthetic trading
5. **Continuous Monitoring**: System is designed to run continuously during your trading hours

## 📝 System Status

- ✅ **Phase 1 Complete**: Data collection and pattern detection
- 🔄 **Phase 2 In Progress**: AI model development
- ⏳ **Phase 3 Planned**: Trading execution engine
- ⏳ **Phase 4 Planned**: Dashboard and monitoring

---

**Version**: 2.0  
**Last Updated**: January 2024  
**Designed for**: Deriv's Synthetic DEX 900 DOWN Index  
**Optimized for**: Demo account with 179ms latency
