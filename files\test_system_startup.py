#!/usr/bin/env python3
"""
Test script to verify the trading system starts correctly after removing daily limits.
"""

import sys
import os
from datetime import datetime

def test_config_import():
    """Test that config can be imported without errors."""
    print("🔧 TESTING CONFIG IMPORT...")
    print("-" * 40)
    
    try:
        import config
        print("✅ Config imported successfully")
        
        # Check that removed attributes don't exist
        if hasattr(config, 'MAX_DAILY_TRADES'):
            print("❌ MAX_DAILY_TRADES still exists")
            return False
        else:
            print("✅ MAX_DAILY_TRADES removed")
        
        if hasattr(config, 'MAX_DAILY_TRADES_PER_TIMEFRAME'):
            print("❌ MAX_DAILY_TRADES_PER_TIMEFRAME still exists")
            return False
        else:
            print("✅ MAX_DAILY_TRADES_PER_TIMEFRAME removed")
        
        # Check that kept attributes exist
        if hasattr(config, 'MAX_CONCURRENT_POSITIONS'):
            print(f"✅ MAX_CONCURRENT_POSITIONS: {config.MAX_CONCURRENT_POSITIONS}")
        else:
            print("❌ MAX_CONCURRENT_POSITIONS missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Config import failed: {e}")
        return False

def test_trading_engine_import():
    """Test that trading engine can be imported and initialized."""
    print("\n🚀 TESTING TRADING ENGINE IMPORT...")
    print("-" * 40)
    
    try:
        from trading_engine import TradingEngine
        print("✅ TradingEngine imported successfully")
        
        # Try to create instance (don't initialize components)
        engine = TradingEngine()
        print("✅ TradingEngine instance created")
        
        return True
        
    except Exception as e:
        print(f"❌ TradingEngine import/creation failed: {e}")
        return False

def test_order_execution_import():
    """Test that order execution system can be imported."""
    print("\n⚙️ TESTING ORDER EXECUTION IMPORT...")
    print("-" * 40)
    
    try:
        from order_execution_system import OrderExecutionSystem
        print("✅ OrderExecutionSystem imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ OrderExecutionSystem import failed: {e}")
        return False

def test_signal_generator_import():
    """Test that signal generator can be imported."""
    print("\n📡 TESTING SIGNAL GENERATOR IMPORT...")
    print("-" * 40)
    
    try:
        from trading_signal_generator import TradingSignalGenerator
        print("✅ TradingSignalGenerator imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ TradingSignalGenerator import failed: {e}")
        return False

def test_dashboard_server_import():
    """Test that dashboard server can be imported."""
    print("\n🖥️ TESTING DASHBOARD SERVER IMPORT...")
    print("-" * 40)
    
    try:
        from dashboard_server import DashboardServer
        print("✅ DashboardServer imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ DashboardServer import failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 AI TRADING SYSTEM - STARTUP TEST AFTER DAILY LIMITS REMOVAL")
    print("=" * 70)
    print("Testing: System can start without daily trade limit errors")
    print()
    
    all_tests_passed = True
    
    # Run all tests
    tests = [
        test_config_import,
        test_trading_engine_import,
        test_order_execution_import,
        test_signal_generator_import,
        test_dashboard_server_import
    ]
    
    for test_func in tests:
        if not test_func():
            all_tests_passed = False
    
    # Show results
    print("\n🎯 TEST RESULTS:")
    print("=" * 30)
    
    if all_tests_passed:
        print("✅ ALL TESTS PASSED!")
        print()
        print("🎉 SYSTEM STARTUP VERIFICATION:")
        print("   ✅ All components can be imported")
        print("   ✅ No config.MAX_DAILY_TRADES errors")
        print("   ✅ Daily trade limits successfully removed")
        print("   ✅ System ready to start without errors")
        print()
        print("💡 NEXT STEPS:")
        print("   1. Start the trading system normally")
        print("   2. Check logs for 'Daily trade limits: REMOVED' message")
        print("   3. Verify dashboard shows updated circuit breakers")
        print("   4. Monitor unlimited trading behavior")
        
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️  System may have startup errors")
        print("🔧 Please review the errors above")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
