#!/usr/bin/env python3
"""
Daily Cache Cleanup System for AI Trading System
Runs at 00:00 daily to maintain optimal performance while protecting AI models.
"""

import os
import sys
import gc
import sqlite3
import shutil
import psutil
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/daily_cleanup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('DailyCleanup')

class DailyCacheCleanupSystem:
    """Comprehensive daily cleanup system with AI model protection."""
    
    def __init__(self):
        self.cleanup_time = datetime.now()
        self.protected_files = [
            # AI Model files - NEVER TOUCH
            'models/saved/',
            'models/performance/',
            # Core data - PRESERVE
            'data/synthetic_cache/synthetic_data.db',
            'data/synthetic_cache/dex_900_down_data.db',
            # Configuration - KEEP
            'config.py',
            'ai_model_configs.json'
        ]
        
        self.cleanup_stats = {
            'tick_data_cleaned': 0,
            'logs_archived': 0,
            'cache_cleared': 0,
            'memory_freed_mb': 0,
            'total_time_seconds': 0
        }
    
    def is_protected_file(self, file_path: str) -> bool:
        """Check if file is protected from cleanup."""
        for protected in self.protected_files:
            if protected in file_path:
                return True
        return False
    
    def cleanup_old_tick_data(self) -> bool:
        """Clean old tick data while preserving recent data for AI models."""
        logger.info("🕐 Starting tick data cleanup...")
        
        try:
            db_path = 'data/synthetic_cache/synthetic_data.db'
            if not os.path.exists(db_path):
                logger.info("   No tick data database found")
                return True
            
            # Keep last 30 days of tick data
            cutoff_timestamp = (datetime.now() - timedelta(days=30)).timestamp()
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Count records before cleanup
            cursor.execute("SELECT COUNT(*) FROM tick_data WHERE timestamp < ?", (cutoff_timestamp,))
            old_records = cursor.fetchone()[0]
            
            if old_records > 0:
                # Delete old tick data
                cursor.execute("DELETE FROM tick_data WHERE timestamp < ?", (cutoff_timestamp,))
                conn.commit()
                
                # Vacuum database to reclaim space
                cursor.execute("VACUUM")
                
                logger.info(f"   ✅ Cleaned {old_records:,} old tick data records (>30 days)")
                self.cleanup_stats['tick_data_cleaned'] = old_records
            else:
                logger.info("   ✅ No old tick data to clean")
            
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"   ❌ Error cleaning tick data: {e}")
            return False
    
    def archive_old_logs(self) -> bool:
        """Archive old log files while keeping recent ones."""
        logger.info("📝 Starting log file archival...")
        
        try:
            logs_dir = Path('logs')
            if not logs_dir.exists():
                logger.info("   No logs directory found")
                return True
            
            # Create archive directory
            archive_dir = logs_dir / 'archived' / datetime.now().strftime('%Y-%m')
            archive_dir.mkdir(parents=True, exist_ok=True)
            
            archived_count = 0
            cutoff_date = datetime.now() - timedelta(days=7)
            
            for log_file in logs_dir.glob('*.log'):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    # Archive old log file
                    archive_path = archive_dir / f"{log_file.stem}_{datetime.fromtimestamp(log_file.stat().st_mtime).strftime('%Y%m%d')}.log"
                    shutil.move(str(log_file), str(archive_path))
                    archived_count += 1
            
            logger.info(f"   ✅ Archived {archived_count} old log files (>7 days)")
            self.cleanup_stats['logs_archived'] = archived_count
            return True
            
        except Exception as e:
            logger.error(f"   ❌ Error archiving logs: {e}")
            return False
    
    def clear_temporary_caches(self) -> bool:
        """Clear temporary calculation caches while preserving AI model data."""
        logger.info("💾 Starting temporary cache cleanup...")
        
        try:
            cache_cleared = 0
            
            # Clear Python __pycache__ directories
            for root, dirs, files in os.walk('.'):
                if '__pycache__' in dirs:
                    pycache_path = os.path.join(root, '__pycache__')
                    if not self.is_protected_file(pycache_path):
                        shutil.rmtree(pycache_path)
                        cache_cleared += 1
            
            # Clear temporary calculation files (if any exist)
            temp_patterns = [
                'temp_*.csv',
                'cache_*.tmp',
                '*.temp'
            ]
            
            for pattern in temp_patterns:
                for temp_file in Path('.').rglob(pattern):
                    if not self.is_protected_file(str(temp_file)):
                        temp_file.unlink()
                        cache_cleared += 1
            
            logger.info(f"   ✅ Cleared {cache_cleared} temporary cache files")
            self.cleanup_stats['cache_cleared'] = cache_cleared
            return True
            
        except Exception as e:
            logger.error(f"   ❌ Error clearing caches: {e}")
            return False
    
    def optimize_memory(self) -> bool:
        """Optimize memory usage with garbage collection."""
        logger.info("🧠 Starting memory optimization...")
        
        try:
            # Get memory before cleanup
            process = psutil.Process()
            memory_before = process.memory_info().rss / (1024 * 1024)  # MB
            
            # Force garbage collection
            collected = gc.collect()
            
            # Get memory after cleanup
            memory_after = process.memory_info().rss / (1024 * 1024)  # MB
            memory_freed = memory_before - memory_after
            
            logger.info(f"   ✅ Garbage collection: {collected} objects collected")
            logger.info(f"   ✅ Memory freed: {memory_freed:.2f} MB")
            
            self.cleanup_stats['memory_freed_mb'] = memory_freed
            return True
            
        except Exception as e:
            logger.error(f"   ❌ Error optimizing memory: {e}")
            return False
    
    def check_system_health(self) -> dict:
        """Check system health after cleanup."""
        logger.info("🔍 Checking system health...")
        
        try:
            # Memory usage
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('.')
            
            # Database sizes
            db_sizes = {}
            for db_file in ['data/synthetic_cache/synthetic_data.db', 'data/synthetic_cache/dex_900_down_data.db']:
                if os.path.exists(db_file):
                    db_sizes[db_file] = os.path.getsize(db_file) / (1024 * 1024)  # MB
            
            health_report = {
                'memory_usage_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_usage_percent': (disk.used / disk.total) * 100,
                'disk_free_gb': disk.free / (1024**3),
                'database_sizes_mb': db_sizes,
                'cleanup_timestamp': self.cleanup_time.isoformat()
            }
            
            logger.info(f"   📊 Memory usage: {memory.percent:.1f}%")
            logger.info(f"   📊 Disk usage: {health_report['disk_usage_percent']:.1f}%")
            logger.info(f"   📊 Database total: {sum(db_sizes.values()):.1f} MB")
            
            return health_report
            
        except Exception as e:
            logger.error(f"   ❌ Error checking system health: {e}")
            return {}
    
    def generate_cleanup_report(self, success: bool, health_report: dict) -> None:
        """Generate comprehensive cleanup report."""
        logger.info("📋 Generating cleanup report...")
        
        report_time = datetime.now()
        
        report = f"""
🧹 DAILY CACHE CLEANUP REPORT
{'='*60}
Cleanup Time: {self.cleanup_time.strftime('%Y-%m-%d %H:%M:%S')}
Duration: {self.cleanup_stats['total_time_seconds']:.2f} seconds
Status: {'✅ SUCCESS' if success else '❌ FAILED'}

📊 CLEANUP STATISTICS:
- Tick data records cleaned: {self.cleanup_stats['tick_data_cleaned']:,}
- Log files archived: {self.cleanup_stats['logs_archived']}
- Cache files cleared: {self.cleanup_stats['cache_cleared']}
- Memory freed: {self.cleanup_stats['memory_freed_mb']:.2f} MB

🔍 SYSTEM HEALTH (POST-CLEANUP):
- Memory usage: {health_report.get('memory_usage_percent', 0):.1f}%
- Memory available: {health_report.get('memory_available_gb', 0):.2f} GB
- Disk usage: {health_report.get('disk_usage_percent', 0):.1f}%
- Disk free: {health_report.get('disk_free_gb', 0):.2f} GB

🛡️ PROTECTED COMPONENTS (UNTOUCHED):
✅ All AI model files preserved
✅ Historical OHLCV data maintained
✅ Pattern events kept intact
✅ Model performance data preserved
✅ Training datasets protected

💡 NEXT CLEANUP: {(report_time + timedelta(days=1)).strftime('%Y-%m-%d 00:00:00')}
"""
        
        # Save report to file
        report_file = f"logs/cleanup_reports/cleanup_report_{report_time.strftime('%Y%m%d')}.txt"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w') as f:
            f.write(report)
        
        logger.info(f"   ✅ Cleanup report saved to {report_file}")

    def run_light_cleanup(self) -> bool:
        """Execute light cleanup routine (memory + temp files only)."""
        start_time = datetime.now()

        logger.info("🧽 STARTING LIGHT CACHE CLEANUP")
        logger.info("="*50)
        logger.info(f"Light cleanup time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        success = True

        try:
            # Execute light cleanup steps (no tick data or log archival)
            steps = [
                ("Temporary Cache Cleanup", self.clear_temporary_caches),
                ("Memory Optimization", self.optimize_memory)
            ]

            for step_name, step_func in steps:
                logger.info(f"\n🔄 {step_name}...")
                if not step_func():
                    success = False
                    logger.error(f"   ❌ {step_name} failed")
                else:
                    logger.info(f"   ✅ {step_name} completed")

            # Calculate total time
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds()

            # Log light cleanup summary
            logger.info("="*50)
            if success:
                logger.info("✅ LIGHT CLEANUP COMPLETED SUCCESSFULLY")
            else:
                logger.error("❌ LIGHT CLEANUP COMPLETED WITH ERRORS")

            logger.info(f"Light cleanup duration: {total_time:.2f} seconds")
            logger.info(f"Cache cleared: {self.cleanup_stats['cache_cleared']} items")
            logger.info(f"Memory freed: {self.cleanup_stats['memory_freed_mb']:.2f} MB")
            logger.info("="*50)

            return success

        except Exception as e:
            logger.error(f"❌ Critical error during light cleanup: {e}")
            return False

    def run_daily_cleanup(self) -> bool:
        """Execute complete daily cleanup routine."""
        start_time = datetime.now()
        
        logger.info("🧹 STARTING DAILY CACHE CLEANUP SYSTEM")
        logger.info("="*70)
        logger.info(f"Cleanup time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        success = True
        
        try:
            # Execute cleanup steps
            steps = [
                ("Tick Data Cleanup", self.cleanup_old_tick_data),
                ("Log File Archival", self.archive_old_logs),
                ("Temporary Cache Cleanup", self.clear_temporary_caches),
                ("Memory Optimization", self.optimize_memory)
            ]
            
            for step_name, step_func in steps:
                logger.info(f"\n🔄 {step_name}...")
                if not step_func():
                    success = False
                    logger.error(f"   ❌ {step_name} failed")
                else:
                    logger.info(f"   ✅ {step_name} completed")
            
            # Check system health
            health_report = self.check_system_health()
            
            # Calculate total time
            end_time = datetime.now()
            self.cleanup_stats['total_time_seconds'] = (end_time - start_time).total_seconds()
            
            # Generate report
            self.generate_cleanup_report(success, health_report)
            
            if success:
                logger.info(f"\n🎉 DAILY CLEANUP COMPLETED SUCCESSFULLY")
                logger.info(f"   Duration: {self.cleanup_stats['total_time_seconds']:.2f} seconds")
                logger.info(f"   System ready for optimal trading performance")
            else:
                logger.error(f"\n❌ DAILY CLEANUP COMPLETED WITH ERRORS")
                logger.error(f"   Check logs for details")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Critical error during cleanup: {e}")
            return False

def main():
    """Main cleanup execution."""
    try:
        cleanup_system = DailyCacheCleanupSystem()
        success = cleanup_system.run_daily_cleanup()
        return success
    except Exception as e:
        logger.error(f"❌ Fatal error in cleanup system: {e}")
        return False

def schedule_daily_cleanup():
    """Schedule daily cleanup to run at 00:00."""
    import schedule
    import time
    import threading

    def run_cleanup_job():
        """Run cleanup in separate thread to avoid blocking."""
        logger.info("🕐 Scheduled cleanup triggered at 00:00")
        success = main()
        if success:
            logger.info("✅ Scheduled cleanup completed successfully")
        else:
            logger.error("❌ Scheduled cleanup failed")

    # Schedule cleanup for 00:00 daily
    schedule.every().day.at("00:00").do(run_cleanup_job)

    logger.info("⏰ Daily cleanup scheduler started")
    logger.info("   Next cleanup: Today/Tomorrow at 00:00")

    # Keep scheduler running
    while True:
        schedule.run_pending()
        time.sleep(60)  # Check every minute

def run_manual_cleanup():
    """Run cleanup manually (for testing or immediate need)."""
    logger.info("🔧 Manual cleanup requested")
    success = main()
    return success

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "--schedule":
            # Run as scheduled service
            schedule_daily_cleanup()
        elif sys.argv[1] == "--manual":
            # Run manual cleanup
            success = run_manual_cleanup()
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "--test":
            # Test run (manual cleanup with detailed output)
            logger.info("🧪 Test cleanup run")
            success = run_manual_cleanup()
            input("Press Enter to exit...")
            sys.exit(0 if success else 1)
        else:
            print("Usage:")
            print("  python daily_cache_cleanup_system.py --schedule  # Run as scheduled service")
            print("  python daily_cache_cleanup_system.py --manual   # Run cleanup now")
            print("  python daily_cache_cleanup_system.py --test     # Test cleanup with pause")
            sys.exit(1)
    else:
        # Default: run manual cleanup
        success = run_manual_cleanup()
        sys.exit(0 if success else 1)
