#!/usr/bin/env python3
"""
Test script to investigate why the AI Trading System is not placing trades.
This will check all conditions and requirements for trade execution.
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_why_no_trades():
    """Investigate why no trades are being placed."""
    try:
        print("🔍 INVESTIGATING WHY NO TRADES ARE BEING PLACED")
        print("=" * 60)
        
        # Import required modules
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        from trading_signal_generator import TradingSignalGenerator
        from order_execution_system import OrderExecutionSystem
        
        print("📊 Initializing trading components...")
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Components initialized")
        
        # Check 1: MT5 Connection
        print("\n🔌 CHECKING MT5 CONNECTION:")
        if order_executor.mt5_connected:
            print("✅ MT5 connected successfully")
        else:
            print("❌ MT5 NOT connected - this would prevent trading!")
            return False
            
        # Check 2: Account Information
        print("\n👤 CHECKING ACCOUNT INFORMATION:")
        import MetaTrader5 as mt5
        account_info = mt5.account_info()
        if account_info:
            print(f"✅ Account Balance: ${account_info.balance:.2f}")
            print(f"✅ Account Equity: ${account_info.equity:.2f}")
            print(f"✅ Free Margin: ${account_info.margin_free:.2f}")
            
            if account_info.trade_allowed:
                print("✅ Trading is ALLOWED on this account")
            else:
                print("❌ Trading is NOT ALLOWED on this account!")
                return False
        else:
            print("❌ Could not get account information!")
            return False
            
        # Check 3: Symbol Trading Status
        print("\n📊 CHECKING SYMBOL TRADING STATUS:")
        symbol_info = mt5.symbol_info("DEX 900 DOWN Index")
        if symbol_info:
            print(f"✅ Symbol: {symbol_info.name}")
            print(f"✅ Trade Mode: {symbol_info.trade_mode}")
            
            if symbol_info.trade_mode == 4:  # Full trading
                print("✅ Symbol allows FULL TRADING")
            else:
                print(f"❌ Symbol trade mode {symbol_info.trade_mode} may restrict trading!")
                
            print(f"✅ Current Spread: {symbol_info.spread} points")
            print(f"✅ Min Volume: {symbol_info.volume_min}")
            print(f"✅ Max Volume: {symbol_info.volume_max}")
        else:
            print("❌ Could not get symbol information!")
            return False
            
        # Check 4: Market Hours
        print("\n⏰ CHECKING MARKET STATUS:")
        current_time = datetime.now()
        print(f"✅ Current Time: {current_time}")
        
        # DEX 900 DOWN Index is synthetic and trades 24/7
        print("✅ DEX 900 DOWN Index trades 24/7 (synthetic)")
        
        # Check 5: AI Models Status
        print("\n🧠 CHECKING AI MODELS:")
        try:
            # Check how many models are currently loaded
            loaded_models = len(ai_manager.models)
            total_models = len(ai_manager.model_configs)
            print(f"✅ Currently Loaded Models: {loaded_models}/{total_models}")

            if loaded_models == 0:
                print("⚠️  NO AI MODELS LOADED - attempting to load models...")
                # Try to load individual models
                for model_name in ai_manager.model_configs.keys():
                    try:
                        if ai_manager.load_model(model_name):
                            loaded_models += 1
                            print(f"✅ Loaded: {model_name}")
                        else:
                            print(f"❌ Failed to load: {model_name}")
                    except Exception as e:
                        print(f"❌ Error loading {model_name}: {e}")

                if loaded_models == 0:
                    print("❌ NO AI MODELS COULD BE LOADED - cannot generate signals!")
                    print("   Models may need to be trained first.")
                    return False
            elif loaded_models < total_models:
                print(f"⚠️  Only {loaded_models}/{total_models} models loaded")
            else:
                print("✅ ALL AI models loaded successfully")
        except Exception as e:
            print(f"❌ Error checking AI models: {e}")
            return False
            
        # Check 6: Data Availability
        print("\n📈 CHECKING DATA AVAILABILITY:")
        try:
            # Check if we have recent data
            latest_data = data_collector.get_latest_data(5, 50)  # 5min, 50 bars
            if latest_data is not None and not latest_data.empty:
                print(f"✅ Latest data available: {len(latest_data)} bars")
                print(f"✅ Latest timestamp: {latest_data.index[-1] if hasattr(latest_data.index[-1], 'strftime') else 'Unknown'}")
            else:
                print("❌ NO recent data available - cannot analyze market!")
                return False
        except Exception as e:
            print(f"❌ Error getting latest data: {e}")
            return False
            
        # Check 7: Signal Generation Test
        print("\n🎯 TESTING SIGNAL GENERATION:")
        try:
            # Get current price for signal generation
            import MetaTrader5 as mt5
            tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
            if tick is None:
                print("❌ Could not get current price for signal generation!")
                return False
            current_price = (tick.bid + tick.ask) / 2

            # Try to generate a signal
            signal = signal_generator.generate_signal(current_price)
            
            if signal:
                print(f"✅ Signal generated: {signal.signal_type.name}")
                print(f"✅ Confidence: {signal.confidence:.2%}")
                print(f"✅ Entry Price: {signal.entry_price:.2f}")
                print(f"✅ Stop Loss: {signal.stop_loss:.2f}")
                print(f"✅ Take Profit: {signal.take_profit:.2f}")
                print(f"✅ Position Size: {signal.position_size:.4f}")
                
                # Check if signal is tradeable
                if signal.signal_type.name == "NO_SIGNAL":
                    print("⚠️  Signal type is NO_SIGNAL - no trade will be placed")
                else:
                    print("✅ Signal is TRADEABLE")
                    
            else:
                print("❌ NO SIGNAL generated - this is why no trades!")
                print("   Possible reasons:")
                print("   - Market conditions don't meet AI criteria")
                print("   - Risk limits preventing signal generation")
                print("   - Insufficient data for analysis")
                return False
                
        except Exception as e:
            print(f"❌ Error generating signal: {e}")
            import traceback
            traceback.print_exc()
            return False
            
        # Check 8: Order Execution Test (if we have a signal)
        if signal and signal.signal_type.name != "NO_SIGNAL":
            print("\n🚀 TESTING ORDER EXECUTION:")
            try:
                # Check if order can be executed
                can_execute = order_executor._can_execute_order(signal)
                
                if can_execute:
                    print("✅ Order CAN be executed (all conditions met)")
                    
                    # Ask user if they want to place a test trade
                    print("\n⚠️  READY TO PLACE ACTUAL TRADE!")
                    print("   This will place a REAL trade on your account.")
                    print("   Signal details:")
                    print(f"   - Type: {signal.signal_type.name}")
                    print(f"   - Volume: {signal.position_size:.4f} lots")
                    print(f"   - Entry: {signal.entry_price:.2f}")
                    print(f"   - Stop Loss: {signal.stop_loss:.2f}")
                    print(f"   - Take Profit: {signal.take_profit:.2f}")
                    
                    # For testing, we'll just show what would happen
                    print("\n🧪 TEST MODE: Not placing actual trade")
                    print("   To enable real trading, the system needs to run continuously")
                    print("   and detect trading opportunities in real-time.")
                    
                else:
                    print("❌ Order CANNOT be executed")
                    print("   Checking specific restrictions...")
                    
                    # Check specific restrictions
                    exec_stats = order_executor.get_execution_statistics()
                    print(f"   Daily trades: {exec_stats['daily_trade_count']}")
                    print(f"   Active positions: {exec_stats['active_positions']}")
                    print(f"   Daily P&L: {exec_stats['daily_pnl']:.4f}")
                    
            except Exception as e:
                print(f"❌ Error testing order execution: {e}")
                return False
        
        # Summary
        print("\n📋 SUMMARY:")
        print("✅ MT5 connection working")
        print("✅ Account allows trading")
        print("✅ Symbol allows trading")
        print("✅ AI models loaded")
        print("✅ Data available")
        
        if signal and signal.signal_type.name != "NO_SIGNAL":
            print("✅ Signal generation working")
            print("✅ System is READY to trade")
            print("\n💡 CONCLUSION:")
            print("   The system CAN place trades when:")
            print("   1. Running continuously (not just tests)")
            print("   2. AI detects profitable opportunities")
            print("   3. Market conditions meet criteria")
            print("   4. Risk limits allow new positions")
        else:
            print("⚠️  No tradeable signal at this moment")
            print("\n💡 CONCLUSION:")
            print("   The system is working correctly but:")
            print("   1. Current market conditions don't meet AI criteria")
            print("   2. No profitable opportunities detected right now")
            print("   3. This is NORMAL - the AI is selective")
            
        return True
        
    except Exception as e:
        print(f"❌ Investigation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 AI TRADING SYSTEM - TRADE INVESTIGATION")
    print("=" * 60)
    print("⚠️  This investigates why no trades are being placed")
    print("=" * 60)
    
    success = test_why_no_trades()
    
    if success:
        print("\n🎉 INVESTIGATION COMPLETE!")
        print("✅ System is working correctly!")
    else:
        print("\n❌ INVESTIGATION FOUND ISSUES!")
        print("🔧 Please check the errors above.")
        
    sys.exit(0 if success else 1)
