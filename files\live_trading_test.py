"""
Live Trading Test for Synthetic DEX 900 DOWN Index Trading System.
Runs the system live with safety controls and monitoring.
"""

import os
import sys
import logging
import time
import signal
from datetime import datetime, timedelta
import threading

# Add current directory to path
sys.path.append(os.getcwd())

import config
from trading_engine import TradingEngine

# Set up logging for live trading
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/live_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("LiveTradingTest")

class LiveTradingTest:
    """
    Live trading test with comprehensive safety controls and monitoring.
    """
    
    def __init__(self):
        """Initialize live trading test."""
        self.trading_engine = None
        self.running = False
        self.start_time = None
        self.test_duration = 30 * 60  # 30 minutes default
        self.max_trades = 5  # Maximum trades for test
        self.max_loss = 0.02  # 2% maximum loss
        
        # Safety controls
        self.emergency_stop_triggered = False
        self.manual_stop_requested = False
        
        # Statistics tracking
        self.stats = {
            "start_time": None,
            "cycles_completed": 0,
            "signals_generated": 0,
            "trades_executed": 0,
            "current_pnl": 0.0,
            "max_drawdown": 0.0,
            "peak_pnl": 0.0,
            "last_signal_time": None,
            "last_trade_time": None
        }
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("Live Trading Test initialized")
        
    def configure_test(self, duration_minutes: int = 30, max_trades: int = 5, max_loss_pct: float = 2.0):
        """Configure test parameters."""
        self.test_duration = duration_minutes * 60
        self.max_trades = max_trades
        self.max_loss = max_loss_pct / 100
        
        logger.info(f"Test configured: {duration_minutes}min, max {max_trades} trades, {max_loss_pct}% max loss")
        
    def start_live_test(self) -> bool:
        """Start live trading test."""
        try:
            print("=" * 80)
            print("STARTING LIVE TRADING TEST")
            print("SYNTHETIC DEX 900 DOWN AI TRADING SYSTEM")
            print("=" * 80)
            print()

            # Safety confirmation
            print("LIVE TRADING WARNING")
            print("This will place REAL trades with REAL money!")
            print(f"Test Duration: {self.test_duration // 60} minutes")
            print(f"Max Trades: {self.max_trades}")
            print(f"Max Loss: {self.max_loss:.1%}")
            print()

            # Get user confirmation
            confirmation = input("Type 'START LIVE TRADING' to confirm: ")
            if confirmation != "START LIVE TRADING":
                print("Live trading cancelled by user")
                return False

            print()
            print("LIVE TRADING CONFIRMED - STARTING IN 5 SECONDS...")
            for i in range(5, 0, -1):
                print(f"   Starting in {i}...")
                time.sleep(1)
            print()
            
            # Initialize trading engine
            logger.info("Initializing trading engine for live trading...")
            self.trading_engine = TradingEngine()
            
            if not self.trading_engine.initialize_components():
                logger.error("Failed to initialize trading components")
                return False
                
            # Start live trading
            self.running = True
            self.start_time = datetime.now()
            self.stats["start_time"] = self.start_time
            
            logger.info("LIVE TRADING STARTED!")
            print("LIVE TRADING ACTIVE!")
            print("   Press Ctrl+C to stop safely")
            print("=" * 80)
            
            # Start monitoring thread
            monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            monitor_thread.start()
            
            # Start trading engine
            if not self.trading_engine.start_trading():
                logger.error("Failed to start trading engine")
                return False
                
            # Main control loop
            self._main_control_loop()
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting live test: {e}")
            return False
            
    def _main_control_loop(self):
        """Main control loop for live trading."""
        logger.info("Main control loop started")
        
        while self.running and not self.emergency_stop_triggered:
            try:
                # Check safety conditions
                if not self._check_safety_conditions():
                    break
                    
                # Check time limit
                if self._check_time_limit():
                    break
                    
                # Update statistics
                self._update_statistics()
                
                # Sleep for control interval
                time.sleep(10)  # Check every 10 seconds
                
            except KeyboardInterrupt:
                logger.info("Manual stop requested via Ctrl+C")
                self.manual_stop_requested = True
                break
            except Exception as e:
                logger.error(f"Error in main control loop: {e}")
                time.sleep(5)
                
        # Stop trading
        self._stop_live_trading()
        
    def _check_safety_conditions(self) -> bool:
        """Check safety conditions and stop if necessary."""
        if not self.trading_engine:
            return False
            
        try:
            # Get current system status
            status = self.trading_engine.get_system_status()
            
            # Check if trading engine is still running
            if not status.get("running", False):
                logger.warning("Trading engine stopped unexpectedly")
                return False
                
            # Check trade count limit
            exec_stats = status.get("execution_stats", {})
            daily_trades = exec_stats.get("daily_trade_count", 0)
            
            if daily_trades >= self.max_trades:
                logger.warning(f"Trade limit reached: {daily_trades}/{self.max_trades}")
                return False
                
            # Check loss limit
            daily_pnl = exec_stats.get("daily_pnl", 0)
            if daily_pnl < -self.max_loss:
                logger.critical(f"Loss limit exceeded: {daily_pnl:.3f} < -{self.max_loss:.3f}")
                self.emergency_stop_triggered = True
                return False
                
            # Update stats
            self.stats["trades_executed"] = daily_trades
            self.stats["current_pnl"] = daily_pnl
            
            # Track drawdown
            if daily_pnl > self.stats["peak_pnl"]:
                self.stats["peak_pnl"] = daily_pnl
                
            current_drawdown = self.stats["peak_pnl"] - daily_pnl
            if current_drawdown > self.stats["max_drawdown"]:
                self.stats["max_drawdown"] = current_drawdown
                
            return True
            
        except Exception as e:
            logger.error(f"Error checking safety conditions: {e}")
            return False
            
    def _check_time_limit(self) -> bool:
        """Check if time limit has been reached."""
        if not self.start_time:
            return False
            
        elapsed = (datetime.now() - self.start_time).total_seconds()
        if elapsed >= self.test_duration:
            logger.info(f"Time limit reached: {elapsed:.0f}s >= {self.test_duration:.0f}s")
            return True
            
        return False
        
    def _update_statistics(self):
        """Update trading statistics."""
        if not self.trading_engine:
            return
            
        try:
            status = self.trading_engine.get_system_status()
            trading_stats = status.get("trading_stats", {})
            
            self.stats["cycles_completed"] = trading_stats.get("cycles_completed", 0)
            self.stats["signals_generated"] = trading_stats.get("signals_generated", 0)
            
        except Exception as e:
            logger.error(f"Error updating statistics: {e}")
            
    def _monitoring_loop(self):
        """Monitoring loop for live trading."""
        logger.info("Monitoring loop started")
        
        last_status_time = datetime.now()
        
        while self.running:
            try:
                current_time = datetime.now()
                
                # Log status every 2 minutes
                if (current_time - last_status_time).total_seconds() >= 120:
                    self._log_live_status()
                    last_status_time = current_time
                    
                time.sleep(30)  # Monitor every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)
                
        logger.info("Monitoring loop ended")
        
    def _log_live_status(self):
        """Log current live trading status."""
        try:
            elapsed = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
            remaining = max(0, self.test_duration - elapsed)
            
            logger.info("=" * 60)
            logger.info("LIVE TRADING STATUS")
            logger.info("=" * 60)
            logger.info(f"Elapsed time: {elapsed/60:.1f} minutes")
            logger.info(f"Remaining time: {remaining/60:.1f} minutes")
            logger.info(f"Cycles completed: {self.stats['cycles_completed']}")
            logger.info(f"Signals generated: {self.stats['signals_generated']}")
            logger.info(f"Trades executed: {self.stats['trades_executed']}/{self.max_trades}")
            logger.info(f"Current P&L: {self.stats['current_pnl']:.4f}")
            logger.info(f"Max drawdown: {self.stats['max_drawdown']:.4f}")
            logger.info(f"Peak P&L: {self.stats['peak_pnl']:.4f}")
            
            if self.trading_engine:
                status = self.trading_engine.get_system_status()
                exec_stats = status.get("execution_stats", {})
                logger.info(f"Active positions: {exec_stats.get('active_positions', 0)}")
                logger.info(f"Fill rate: {exec_stats.get('fill_rate', 0):.1%}")
                logger.info(f"Avg execution time: {exec_stats.get('avg_execution_time_ms', 0):.1f}ms")
                
            logger.info("=" * 60)
            
        except Exception as e:
            logger.error(f"Error logging live status: {e}")
            
    def _stop_live_trading(self):
        """Stop live trading safely."""
        logger.info("Stopping live trading...")
        
        self.running = False
        
        if self.trading_engine:
            # Determine stop reason
            if self.emergency_stop_triggered:
                reason = "Emergency stop - loss limit exceeded"
            elif self.manual_stop_requested:
                reason = "Manual stop requested"
            elif self._check_time_limit():
                reason = "Time limit reached"
            elif self.stats["trades_executed"] >= self.max_trades:
                reason = "Trade limit reached"
            else:
                reason = "Unknown reason"
                
            logger.info(f"Stop reason: {reason}")
            
            # Stop trading engine
            self.trading_engine.stop_trading(reason)
            
        # Log final results
        self._log_final_results()
        
    def _log_final_results(self):
        """Log final trading results."""
        try:
            elapsed = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
            
            print()
            print("=" * 80)
            print("LIVE TRADING TEST COMPLETED")
            print("=" * 80)
            print(f"FINAL RESULTS:")
            print(f"   Duration: {elapsed/60:.1f} minutes")
            print(f"   Cycles completed: {self.stats['cycles_completed']}")
            print(f"   Signals generated: {self.stats['signals_generated']}")
            print(f"   Trades executed: {self.stats['trades_executed']}")
            print(f"   Final P&L: {self.stats['current_pnl']:.4f}")
            print(f"   Max drawdown: {self.stats['max_drawdown']:.4f}")
            print(f"   Peak P&L: {self.stats['peak_pnl']:.4f}")
            
            # Calculate performance metrics
            if elapsed > 0:
                signals_per_hour = (self.stats['signals_generated'] / elapsed) * 3600
                trades_per_hour = (self.stats['trades_executed'] / elapsed) * 3600
                print(f"   Signals per hour: {signals_per_hour:.1f}")
                print(f"   Trades per hour: {trades_per_hour:.1f}")
                
            # Determine success
            if self.stats['current_pnl'] > 0:
                print("RESULT: PROFITABLE")
            elif self.stats['current_pnl'] == 0:
                print("RESULT: BREAK-EVEN")
            else:
                print("RESULT: LOSS")

            if not self.emergency_stop_triggered:
                print("SAFETY: No emergency stops triggered")
            else:
                print("SAFETY: Emergency stop was triggered")
                
            print("=" * 80)
            
            # Log to file
            logger.info("LIVE TRADING TEST COMPLETED")
            logger.info(f"Duration: {elapsed/60:.1f} minutes")
            logger.info(f"Final P&L: {self.stats['current_pnl']:.4f}")
            logger.info(f"Trades executed: {self.stats['trades_executed']}")
            logger.info(f"Emergency stops: {self.emergency_stop_triggered}")
            
        except Exception as e:
            logger.error(f"Error logging final results: {e}")
            
    def _signal_handler(self, signum, frame):
        """Handle system signals."""
        logger.info(f"Received signal {signum}, stopping live trading...")
        self.manual_stop_requested = True
        self.running = False

def main():
    """Main function for live trading test."""
    try:
        # Create live trading test
        live_test = LiveTradingTest()
        
        # Configure test parameters
        print("LIVE TRADING TEST CONFIGURATION")
        print("=" * 50)
        
        # Get test duration
        try:
            duration = input("Test duration in minutes (default 30): ").strip()
            duration = int(duration) if duration else 30
        except ValueError:
            duration = 30
            
        # Get max trades
        try:
            max_trades = input("Maximum trades (default 5): ").strip()
            max_trades = int(max_trades) if max_trades else 5
        except ValueError:
            max_trades = 5
            
        # Get max loss
        try:
            max_loss = input("Maximum loss percentage (default 2.0): ").strip()
            max_loss = float(max_loss) if max_loss else 2.0
        except ValueError:
            max_loss = 2.0
            
        # Configure test
        live_test.configure_test(duration, max_trades, max_loss)
        
        # Start live test
        success = live_test.start_live_test()
        
        if success:
            print("Live trading test completed successfully")
        else:
            print("Live trading test failed")
            
        return success
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
