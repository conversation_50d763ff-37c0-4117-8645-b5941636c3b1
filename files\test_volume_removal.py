#!/usr/bin/env python3
"""
Test Volume Removal Implementation
Validate that all volume contamination has been successfully removed.
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime

def test_model_configurations():
    """Test that model configurations no longer contain volume features."""
    print("🧠 TESTING MODEL CONFIGURATIONS")
    print("=" * 60)
    
    try:
        from ai_model_manager import AIModelManager
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        
        # Check model configurations
        contaminated_features = ["volume", "volume_velocity", "volume_trend", "volume_breakout"]
        clean_features = ["volatility_regime", "volatility_adjusted_momentum", "trend_sustainability", "false_breakout_filter"]
        
        print("📋 Checking model configurations:")
        
        all_clean = True
        for model_name, config in ai_manager.model_configs.items():
            features = config.get("features", [])
            
            # Check for contamination
            contamination_found = any(cf in features for cf in contaminated_features)
            clean_features_found = any(cf in features for cf in clean_features)
            
            status = "❌ CONTAMINATED" if contamination_found else "✅ CLEAN"
            enhancement = "✅ ENHANCED" if clean_features_found else "⚠️  NO ENHANCEMENTS"
            
            print(f"   🧠 {model_name}: {status} | {enhancement}")
            print(f"      Features: {features}")
            
            if contamination_found:
                all_clean = False
                contaminated = [f for f in features if f in contaminated_features]
                print(f"      ❌ Contaminated with: {contaminated}")
            
            if clean_features_found:
                enhanced = [f for f in features if f in clean_features]
                print(f"      ✅ Enhanced with: {enhanced}")
            
            print()
        
        if all_clean:
            print("🎉 ALL MODEL CONFIGURATIONS ARE CLEAN!")
        else:
            print("❌ Some models still have volume contamination")
        
        return all_clean
        
    except Exception as e:
        print(f"❌ Error testing model configurations: {e}")
        return False

def test_feature_extraction_functions():
    """Test that new clean feature extraction functions exist."""
    print("🔧 TESTING FEATURE EXTRACTION FUNCTIONS")
    print("=" * 60)
    
    try:
        from trading_signal_generator import TradingSignalGenerator
        
        # Create test signal generator
        signal_gen = TradingSignalGenerator(None, None, None)
        
        # Test new clean functions exist
        clean_functions = [
            "_get_volatility_regime_features",
            "_get_volatility_adjusted_momentum_features", 
            "_get_trend_sustainability_features",
            "_get_false_breakout_filter_features"
        ]
        
        # Test old volume functions are removed
        volume_functions = [
            "_get_volume_features",
            "_get_volume_velocity_features",
            "_get_volume_trend_features", 
            "_get_volume_breakout_features"
        ]
        
        print("✅ Testing new clean functions:")
        all_clean_exist = True
        for func_name in clean_functions:
            if hasattr(signal_gen, func_name):
                print(f"   ✅ {func_name} - EXISTS")
            else:
                print(f"   ❌ {func_name} - MISSING")
                all_clean_exist = False
        
        print("\n❌ Testing volume functions are removed:")
        all_volume_removed = True
        for func_name in volume_functions:
            if hasattr(signal_gen, func_name):
                print(f"   ❌ {func_name} - STILL EXISTS")
                all_volume_removed = False
            else:
                print(f"   ✅ {func_name} - REMOVED")
        
        success = all_clean_exist and all_volume_removed
        
        if success:
            print("\n🎉 ALL FEATURE FUNCTIONS UPDATED SUCCESSFULLY!")
        else:
            print("\n❌ Some feature functions need attention")
        
        return success
        
    except Exception as e:
        print(f"❌ Error testing feature functions: {e}")
        return False

def test_feature_extraction_with_data():
    """Test feature extraction with actual data."""
    print("📊 TESTING FEATURE EXTRACTION WITH DATA")
    print("=" * 60)
    
    try:
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from trading_signal_generator import TradingSignalGenerator
        from ai_model_manager import AIModelManager
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_gen = TradingSignalGenerator(data_collector, ai_manager, pattern_detector)
        
        # Get test data
        print("📈 Getting test data...")
        test_data = data_collector.get_latest_data(5, 100)
        
        if test_data.empty:
            print("❌ No test data available")
            return False
        
        print(f"✅ Got {len(test_data)} data points")
        
        # Calculate indicators
        print("🔧 Calculating indicators...")
        df_indicators = pattern_detector.calculate_synthetic_indicators(test_data)
        
        if df_indicators.empty:
            print("❌ No indicators calculated")
            return False
        
        print(f"✅ Calculated indicators for {len(df_indicators)} points")
        
        # Test feature extraction for each model
        print("🧠 Testing feature extraction for each model:")
        
        all_successful = True
        for model_name, config in ai_manager.model_configs.items():
            try:
                features = signal_gen._extract_features_for_model(df_indicators, model_name, df_indicators.iloc[-1])
                
                if features is not None and len(features) > 0:
                    print(f"   ✅ {model_name}: {len(features)} features extracted")
                else:
                    print(f"   ❌ {model_name}: No features extracted")
                    all_successful = False
                    
            except Exception as e:
                print(f"   ❌ {model_name}: Error - {e}")
                all_successful = False
        
        if all_successful:
            print("\n🎉 ALL FEATURE EXTRACTIONS SUCCESSFUL!")
        else:
            print("\n❌ Some feature extractions failed")
        
        return all_successful
        
    except Exception as e:
        print(f"❌ Error testing feature extraction: {e}")
        return False

def test_no_volume_dependencies():
    """Test that no volume dependencies remain in the code."""
    print("🔍 TESTING FOR REMAINING VOLUME DEPENDENCIES")
    print("=" * 60)
    
    try:
        # Test files for volume references
        files_to_check = [
            "ai_model_manager.py",
            "trading_signal_generator.py",
            "dashboard_server.py"
        ]
        
        volume_keywords = [
            "volume_velocity",
            "volume_trend", 
            "volume_breakout",
            "_get_volume_features",
            "_get_volume_velocity_features",
            "_get_volume_trend_features",
            "_get_volume_breakout_features"
        ]
        
        print("🔍 Scanning files for volume dependencies:")
        
        all_clean = True
        for file_name in files_to_check:
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                found_dependencies = []
                for keyword in volume_keywords:
                    if keyword in content:
                        found_dependencies.append(keyword)
                
                if found_dependencies:
                    print(f"   ❌ {file_name}: Found {found_dependencies}")
                    all_clean = False
                else:
                    print(f"   ✅ {file_name}: Clean")
                    
            except Exception as e:
                print(f"   ⚠️  {file_name}: Could not check - {e}")
        
        if all_clean:
            print("\n🎉 NO VOLUME DEPENDENCIES FOUND!")
        else:
            print("\n❌ Some volume dependencies still exist")
        
        return all_clean
        
    except Exception as e:
        print(f"❌ Error checking volume dependencies: {e}")
        return False

def generate_volume_removal_report():
    """Generate a comprehensive report of the volume removal."""
    print("📋 GENERATING VOLUME REMOVAL REPORT")
    print("=" * 60)
    
    report = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "model_configurations": "Unknown",
        "feature_functions": "Unknown", 
        "feature_extraction": "Unknown",
        "volume_dependencies": "Unknown",
        "overall_status": "Unknown"
    }
    
    # Run all tests
    config_clean = test_model_configurations()
    functions_clean = test_feature_extraction_functions()
    extraction_works = test_feature_extraction_with_data()
    no_dependencies = test_no_volume_dependencies()
    
    # Update report
    report["model_configurations"] = "✅ CLEAN" if config_clean else "❌ CONTAMINATED"
    report["feature_functions"] = "✅ UPDATED" if functions_clean else "❌ INCOMPLETE"
    report["feature_extraction"] = "✅ WORKING" if extraction_works else "❌ FAILING"
    report["volume_dependencies"] = "✅ REMOVED" if no_dependencies else "❌ REMAINING"
    
    overall_success = config_clean and functions_clean and extraction_works and no_dependencies
    report["overall_status"] = "✅ SUCCESS" if overall_success else "❌ NEEDS WORK"
    
    print(f"\n📊 VOLUME REMOVAL REPORT")
    print("=" * 50)
    print(f"Timestamp: {report['timestamp']}")
    print(f"Model Configurations: {report['model_configurations']}")
    print(f"Feature Functions: {report['feature_functions']}")
    print(f"Feature Extraction: {report['feature_extraction']}")
    print(f"Volume Dependencies: {report['volume_dependencies']}")
    print(f"Overall Status: {report['overall_status']}")
    
    if overall_success:
        print(f"\n🎉 PHASE 1 VOLUME REMOVAL COMPLETE!")
        print("✅ All volume contamination successfully removed")
        print("✅ Clean feature functions implemented")
        print("✅ Feature extraction working properly")
        print("✅ Ready for model retraining")
    else:
        print(f"\n⚠️  PHASE 1 NEEDS ADDITIONAL WORK")
        print("❌ Some issues remain to be resolved")
    
    return overall_success

def main():
    """Main test function."""
    print("🔥 PHASE 1 VOLUME REMOVAL VALIDATION")
    print("=" * 80)
    print(f"Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Generate comprehensive report
    success = generate_volume_removal_report()
    
    print(f"\n🎯 VALIDATION COMPLETE")
    print("=" * 50)
    
    if success:
        print("🎉 PHASE 1 VOLUME REMOVAL SUCCESSFUL!")
        print("🚀 Ready to proceed with Phase 2 enhancements")
    else:
        print("❌ PHASE 1 NEEDS ADDITIONAL WORK")
        print("🔧 Please address the issues above")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
