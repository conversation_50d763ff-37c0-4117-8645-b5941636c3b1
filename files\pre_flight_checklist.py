"""
Pre-Flight Checklist for Live Trading
Comprehensive system check before going live.
"""

import os
import sys
import logging
import MetaTrader5 as mt5
from datetime import datetime

# Add current directory to path
sys.path.append(os.getcwd())

import config
from synthetic_data_collector import SyntheticData<PERSON>ollector
from synthetic_pattern_detector import SyntheticPatternDetector
from ai_model_manager import AIModelManager
from trading_signal_generator import TradingSignalGenerator
from order_execution_system import OrderExecutionSystem

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("PreFlightCheck")

def check_mt5_connection():
    """Check MT5 connection and account status."""
    print("🔌 CHECKING MT5 CONNECTION...")
    
    try:
        if not mt5.initialize():
            print("❌ MT5 initialization failed")
            return False
            
        # Check account info
        account_info = mt5.account_info()
        if account_info is None:
            print("❌ Could not get account information")
            return False
            
        print(f"✅ MT5 Connected")
        print(f"   Account: {account_info.login}")
        print(f"   Server: {account_info.server}")
        print(f"   Balance: {account_info.balance:.2f}")
        print(f"   Equity: {account_info.equity:.2f}")
        print(f"   Margin: {account_info.margin:.2f}")
        print(f"   Free Margin: {account_info.margin_free:.2f}")
        print(f"   Leverage: 1:{account_info.leverage}")
        
        # Check if demo or live
        if account_info.trade_mode == mt5.ACCOUNT_TRADE_MODE_DEMO:
            print("✅ Account Type: DEMO (Safe for testing)")
        elif account_info.trade_mode == mt5.ACCOUNT_TRADE_MODE_REAL:
            print("⚠️  Account Type: LIVE (Real money at risk!)")
        else:
            print("❓ Account Type: Unknown")
            
        return True
        
    except Exception as e:
        print(f"❌ MT5 connection error: {e}")
        return False

def check_symbol_availability():
    """Check DEX 900 DOWN Index availability."""
    print("\n📊 CHECKING SYMBOL AVAILABILITY...")
    
    try:
        symbol = config.SYMBOL
        symbol_info = mt5.symbol_info(symbol)
        
        if symbol_info is None:
            print(f"❌ Symbol {symbol} not found")
            return False
            
        print(f"✅ Symbol: {symbol}")
        print(f"   Description: {symbol_info.description}")
        print(f"   Point: {symbol_info.point}")
        print(f"   Digits: {symbol_info.digits}")
        print(f"   Spread: {symbol_info.spread}")
        print(f"   Min Volume: {symbol_info.volume_min}")
        print(f"   Max Volume: {symbol_info.volume_max}")
        print(f"   Volume Step: {symbol_info.volume_step}")
        
        # Check if trading is allowed
        if symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_DISABLED:
            print("❌ Trading disabled for this symbol")
            return False
        elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_LONGONLY:
            print("⚠️  Long positions only")
        elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_SHORTONLY:
            print("⚠️  Short positions only")
        elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_CLOSEONLY:
            print("❌ Close only mode - no new positions allowed")
            return False
        else:
            print("✅ Full trading allowed")
            
        # Get current price
        tick = mt5.symbol_info_tick(symbol)
        if tick:
            print(f"   Current Bid: {tick.bid}")
            print(f"   Current Ask: {tick.ask}")
            print(f"   Spread: {tick.ask - tick.bid:.5f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Symbol check error: {e}")
        return False

def check_ai_models():
    """Check AI model availability and status."""
    print("\n🤖 CHECKING AI MODELS...")
    
    try:
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        
        # Try to load existing models
        print("   Loading existing models...")
        available_models = []
        model_dir = "models/saved"

        if os.path.exists(model_dir):
            for model_name in os.listdir(model_dir):
                model_path = os.path.join(model_dir, model_name)
                if os.path.isdir(model_path):
                    try:
                        success = ai_manager.load_model(model_name)
                        if success:
                            available_models.append(model_name)
                            print(f"   ✅ Loaded: {model_name}")
                        else:
                            print(f"   ❌ Failed to load: {model_name}")
                    except Exception as e:
                        print(f"   ❌ Error loading {model_name}: {e}")

        # Check model status after loading
        model_status = ai_manager.get_model_status()
        loaded_models = model_status.get("loaded_models", [])
        total_configured = model_status.get("total_configured", 0)

        print(f"✅ AI Models: {len(loaded_models)}/{total_configured} loaded")

        if len(loaded_models) == 0:
            print("❌ No AI models loaded - training required")
            return False
            
        # Check individual models
        for model_name in loaded_models:
            performance = model_status.get("model_performance", {}).get(model_name, {})
            accuracy = performance.get("accuracy", 0)
            print(f"   {model_name}: {accuracy:.1%} accuracy")
            
        # Test prediction capability
        print("\n   Testing prediction capability...")
        test_data = data_collector.get_latest_data(timeframe=15, count=100)
        if not test_data.empty:
            df_indicators = pattern_detector.calculate_synthetic_indicators(test_data)
            signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
            
            current_price = test_data['close'].iloc[-1]
            test_signal = signal_generator.generate_signal(current_price)
            
            if test_signal:
                print(f"✅ Test signal generated: {test_signal.signal_type.name} ({test_signal.confidence:.1%})")
            else:
                print("⚠️  No test signal generated (may be normal)")
        else:
            print("❌ No data available for testing")
            return False
            
        # Cleanup
        ai_manager.cleanup()
        data_collector.stop_real_time_collection()
        
        return True
        
    except Exception as e:
        print(f"❌ AI model check error: {e}")
        return False

def check_risk_settings():
    """Check risk management settings."""
    print("\n🛡️ CHECKING RISK SETTINGS...")
    
    try:
        print(f"✅ Risk Settings:")
        print(f"   Base risk per trade: {config.BASE_RISK_PER_TRADE:.1%}")
        print(f"   Max position size: {config.MAX_POSITION_SIZE:.1%}")
        print(f"   Min signal confidence: {config.MIN_SIGNAL_CONFIDENCE:.1%}")
        print(f"   Min risk/reward ratio: {config.MIN_RISK_REWARD_RATIO}")
        print(f"   Max daily trades: REMOVED (unlimited trades)")
        print(f"   Max concurrent positions: {config.MAX_CONCURRENT_POSITIONS}")
        
        # Check synthetic risk rules
        synthetic_rules = config.SYNTHETIC_RISK_RULES
        print(f"\n   Synthetic Risk Rules:")
        position_rules = synthetic_rules.get("position_sizing", {})
        print(f"   Max position size: {position_rules.get('max_position_size', 0):.1%}")
        
        circuit_breakers = synthetic_rules.get("circuit_breakers", {})
        print(f"   Max daily drawdown: {circuit_breakers.get('max_daily_drawdown', 0):.1%}")
        print(f"   Max consecutive losses: {circuit_breakers.get('max_consecutive_losses', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Risk settings check error: {e}")
        return False

def check_system_resources():
    """Check system resources and requirements."""
    print("\n💻 CHECKING SYSTEM RESOURCES...")
    
    try:
        import psutil
        
        # Check CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"✅ CPU Usage: {cpu_percent:.1f}%")
        
        # Check Memory
        memory = psutil.virtual_memory()
        print(f"✅ Memory Usage: {memory.percent:.1f}% ({memory.used/1024/1024/1024:.1f}GB used)")
        
        # Check Disk Space
        disk = psutil.disk_usage('.')
        print(f"✅ Disk Space: {disk.percent:.1f}% used ({disk.free/1024/1024/1024:.1f}GB free)")
        
        # Check if logs directory exists
        if not os.path.exists('logs'):
            os.makedirs('logs')
            print("✅ Created logs directory")
        else:
            print("✅ Logs directory exists")
            
        # Check if models directory exists
        if not os.path.exists('models/saved'):
            os.makedirs('models/saved', exist_ok=True)
            print("✅ Created models directory")
        else:
            print("✅ Models directory exists")
            
        return True
        
    except ImportError:
        print("⚠️  psutil not available - skipping resource check")
        return True
    except Exception as e:
        print(f"❌ System resources check error: {e}")
        return False

def run_pre_flight_checklist():
    """Run complete pre-flight checklist."""
    print("=" * 80)
    print("🚀 PRE-FLIGHT CHECKLIST FOR LIVE TRADING")
    print("SYNTHETIC DEX 900 DOWN AI TRADING SYSTEM")
    print("=" * 80)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    checks = [
        ("MT5 Connection", check_mt5_connection),
        ("Symbol Availability", check_symbol_availability),
        ("AI Models", check_ai_models),
        ("Risk Settings", check_risk_settings),
        ("System Resources", check_system_resources)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
        except Exception as e:
            print(f"❌ {check_name} check failed: {e}")
            results[check_name] = False
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 PRE-FLIGHT CHECKLIST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(checks)
    
    for check_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 OVERALL RESULT: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 ALL CHECKS PASSED - SYSTEM READY FOR LIVE TRADING!")
        print("\n⚠️  FINAL WARNING:")
        print("   - This will place REAL trades with REAL money")
        print("   - Monitor the system closely during live trading")
        print("   - Be prepared to stop trading if needed")
        print("   - Start with small position sizes")
        ready = True
    elif passed >= total - 1:
        print("⚠️  MOSTLY READY - Minor issues detected")
        print("   Review failed checks before proceeding")
        ready = False
    else:
        print("❌ NOT READY - Major issues detected")
        print("   Fix failed checks before attempting live trading")
        ready = False
    
    print("=" * 80)
    
    # Cleanup MT5
    try:
        mt5.shutdown()
    except:
        pass
    
    return ready

if __name__ == "__main__":
    ready = run_pre_flight_checklist()
    
    if ready:
        print("\n🚀 Ready to proceed with live trading test!")
        print("   Run: python live_trading_test.py")
    else:
        print("\n🔧 Please fix the issues above before proceeding")
    
    exit(0 if ready else 1)
