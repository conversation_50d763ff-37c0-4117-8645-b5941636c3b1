#!/usr/bin/env python3
"""
Analyze training data to estimate how many trades the models would have made during training.
This helps determine if the current criteria are too strict.
"""

import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sqlite3

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_training_trade_frequency():
    """Analyze historical data to estimate trade frequency during training period."""
    try:
        print("🔍 ANALYZING TRAINING DATA TRADE FREQUENCY")
        print("=" * 70)
        print("📊 Estimating how many trades models would have made during training")
        print("=" * 70)
        
        # Connect to the database
        db_path = "data/synthetic_cache/dex_900_down_data.db"
        try:
            conn = sqlite3.connect(db_path)
            print(f"✅ Connected to database: {db_path}")
        except Exception as e:
            print(f"❌ Could not connect to database: {e}")
            return False
        
        # Get data statistics
        print("\n📈 DATA STATISTICS:")
        cursor = conn.cursor()
        
        # Check available timeframes
        cursor.execute("SELECT DISTINCT timeframe FROM ohlcv_data ORDER BY timeframe")
        timeframes = [row[0] for row in cursor.fetchall()]
        print(f"   Available timeframes: {timeframes}")
        
        # Get data range for each timeframe
        total_signals = 0
        total_days = 0
        
        for tf in timeframes:
            cursor.execute("""
                SELECT COUNT(*), MIN(timestamp), MAX(timestamp) 
                FROM ohlcv_data 
                WHERE timeframe = ?
            """, (tf,))
            
            count, min_ts, max_ts = cursor.fetchone()
            
            if count > 0:
                start_date = datetime.fromtimestamp(min_ts)
                end_date = datetime.fromtimestamp(max_ts)
                duration = end_date - start_date
                
                print(f"\n   📊 {tf}-minute timeframe:")
                print(f"      Records: {count:,}")
                print(f"      Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
                print(f"      Duration: {duration.days} days")
                
                if tf == 15:  # Use 15-minute as primary analysis timeframe
                    total_days = duration.days
                    
                    # Simulate signal generation based on price movements
                    print(f"\n   🎯 SIMULATING SIGNAL GENERATION (15-min timeframe):")
                    
                    # Get the data
                    cursor.execute("""
                        SELECT timestamp, open, high, low, close, volume 
                        FROM ohlcv_data 
                        WHERE timeframe = ? 
                        ORDER BY timestamp
                    """, (tf,))
                    
                    data = cursor.fetchall()
                    df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                    
                    # Calculate basic indicators for signal simulation
                    df['returns'] = df['close'].pct_change()
                    df['volatility'] = df['returns'].rolling(20).std()
                    df['sma_20'] = df['close'].rolling(20).mean()
                    df['sma_50'] = df['close'].rolling(50).mean()
                    
                    # Simulate different signal criteria
                    signal_scenarios = {
                        "Current Strict (0.2% + 1.5:1 R/R)": simulate_strict_signals(df),
                        "Moderate (0.15% + 1.3:1 R/R)": simulate_moderate_signals(df),
                        "Relaxed (0.1% + 1.2:1 R/R)": simulate_relaxed_signals(df),
                        "Very Relaxed (0.05% + 1.1:1 R/R)": simulate_very_relaxed_signals(df)
                    }
                    
                    print(f"\n   📊 SIGNAL FREQUENCY ANALYSIS:")
                    for scenario, signals in signal_scenarios.items():
                        signals_per_day = signals / total_days if total_days > 0 else 0
                        signals_per_week = signals_per_day * 7
                        signals_per_month = signals_per_day * 30
                        
                        print(f"\n      🎯 {scenario}:")
                        print(f"         Total Signals: {signals:,}")
                        print(f"         Per Day: {signals_per_day:.2f}")
                        print(f"         Per Week: {signals_per_week:.1f}")
                        print(f"         Per Month: {signals_per_month:.0f}")
                        
                        if signals_per_day < 0.1:
                            frequency_rating = "❌ TOO RARE"
                        elif signals_per_day < 0.5:
                            frequency_rating = "⚠️ VERY LOW"
                        elif signals_per_day < 1.0:
                            frequency_rating = "✅ LOW"
                        elif signals_per_day < 3.0:
                            frequency_rating = "✅ MODERATE"
                        elif signals_per_day < 5.0:
                            frequency_rating = "✅ ACTIVE"
                        else:
                            frequency_rating = "⚠️ TOO FREQUENT"
                        
                        print(f"         Rating: {frequency_rating}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        print(f"=" * 50)
        
        current_signals = signal_scenarios.get("Current Strict (0.2% + 1.5:1 R/R)", 0)
        current_per_day = current_signals / total_days if total_days > 0 else 0
        
        if current_per_day < 0.1:
            print(f"❌ CURRENT CRITERIA TOO STRICT!")
            print(f"   Current: {current_per_day:.3f} signals/day")
            print(f"   Recommendation: Use 'Relaxed' or 'Moderate' criteria")
            print(f"   Suggested changes:")
            print(f"   - Reduce minimum profit from 0.2% to 0.1%")
            print(f"   - Reduce R/R ratio from 1.5:1 to 1.2:1")
            print(f"   - Reduce confidence threshold from 60% to 50%")
        elif current_per_day < 0.5:
            print(f"⚠️ CURRENT CRITERIA QUITE STRICT")
            print(f"   Current: {current_per_day:.3f} signals/day")
            print(f"   Consider slight relaxation for more opportunities")
        else:
            print(f"✅ CURRENT CRITERIA REASONABLE")
            print(f"   Current: {current_per_day:.3f} signals/day")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_strict_signals(df):
    """Simulate signals with current strict criteria."""
    signals = 0
    
    for i in range(50, len(df)):  # Start after indicators are calculated
        # Check for strong price movements (0.2% threshold)
        price_change = abs(df.iloc[i]['returns'])
        volatility = df.iloc[i]['volatility']
        
        if pd.isna(price_change) or pd.isna(volatility):
            continue
            
        # Strong movement criteria
        if price_change > 0.002 and volatility > 0.01:  # 0.2% move + high volatility
            # Check risk-reward (simplified)
            if price_change * 1.5 > 0.003:  # 1.5:1 R/R with 0.3% target
                signals += 1
    
    return signals

def simulate_moderate_signals(df):
    """Simulate signals with moderate criteria."""
    signals = 0
    
    for i in range(50, len(df)):
        price_change = abs(df.iloc[i]['returns'])
        volatility = df.iloc[i]['volatility']
        
        if pd.isna(price_change) or pd.isna(volatility):
            continue
            
        # Moderate criteria
        if price_change > 0.0015 and volatility > 0.008:  # 0.15% move
            if price_change * 1.3 > 0.002:  # 1.3:1 R/R
                signals += 1
    
    return signals

def simulate_relaxed_signals(df):
    """Simulate signals with relaxed criteria."""
    signals = 0
    
    for i in range(50, len(df)):
        price_change = abs(df.iloc[i]['returns'])
        volatility = df.iloc[i]['volatility']
        
        if pd.isna(price_change) or pd.isna(volatility):
            continue
            
        # Relaxed criteria
        if price_change > 0.001 and volatility > 0.005:  # 0.1% move
            if price_change * 1.2 > 0.0015:  # 1.2:1 R/R
                signals += 1
    
    return signals

def simulate_very_relaxed_signals(df):
    """Simulate signals with very relaxed criteria."""
    signals = 0
    
    for i in range(50, len(df)):
        price_change = abs(df.iloc[i]['returns'])
        volatility = df.iloc[i]['volatility']
        
        if pd.isna(price_change) or pd.isna(volatility):
            continue
            
        # Very relaxed criteria
        if price_change > 0.0005 and volatility > 0.003:  # 0.05% move
            if price_change * 1.1 > 0.001:  # 1.1:1 R/R
                signals += 1
    
    return signals

if __name__ == "__main__":
    print("🔍 TRAINING DATA TRADE FREQUENCY ANALYSIS")
    print("=" * 70)
    print("📊 Analyzing historical data to estimate trade opportunities")
    print("=" * 70)
    
    success = analyze_training_trade_frequency()
    
    if success:
        print("\n🎉 ANALYSIS COMPLETED!")
        print("✅ Check recommendations above for criteria adjustments")
    else:
        print("\n❌ ANALYSIS FAILED!")
        print("🔧 Check database connection and data availability")
        
    sys.exit(0 if success else 1)
