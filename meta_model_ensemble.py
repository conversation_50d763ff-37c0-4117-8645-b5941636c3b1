"""
Meta-Model Ensemble Stacking for AI Trading System.
Implements a second-level AI model that learns from base model outputs to make final trading decisions.
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import pickle
import os
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import joblib

# Set up logging
logger = logging.getLogger("MetaModelEnsemble")

class MetaModelEnsemble:
    """
    Meta-model that learns from base model predictions to make final trading decisions.
    Uses ensemble stacking to combine predictions from all 9 base models.
    """
    
    def __init__(self, model_names: List[str]):
        """Initialize the meta-model ensemble."""
        self.model_names = model_names
        self.meta_model = None
        self.is_trained = False
        self.training_data = []
        self.performance_history = []
        
        # Meta-model configuration
        self.meta_model_type = "random_forest"  # Options: random_forest, gradient_boosting, logistic_regression
        self.min_training_samples = 100
        self.retrain_interval_hours = 24
        self.last_training_time = None
        
        # Feature engineering parameters
        self.feature_window = 10  # Number of recent predictions to use as features
        self.confidence_threshold = 0.6
        
        # Model storage
        self.model_dir = "models/meta_model"
        os.makedirs(self.model_dir, exist_ok=True)
        
        # Load existing model if available
        self._load_meta_model()
    
    def collect_training_data(self, base_predictions: Dict, market_features: Dict,
                            actual_outcome: Optional[int] = None, prediction_time: Optional[datetime] = None):
        """
        Collect training data from base model predictions and market features.
        
        Args:
            base_predictions: Dict with model_name -> {signal, confidence} predictions
            market_features: Dict with additional market context features
            actual_outcome: Actual market outcome (for training, None for prediction)
            prediction_time: Timestamp of the prediction
        """
        try:
            if prediction_time is None:
                prediction_time = datetime.now()
            
            # Create feature vector from base model predictions
            features = self._create_feature_vector(base_predictions, market_features)
            
            # Store training sample
            training_sample = {
                'timestamp': prediction_time,
                'features': features,
                'base_predictions': base_predictions.copy(),
                'market_features': market_features.copy(),
                'actual_outcome': actual_outcome
            }
            
            self.training_data.append(training_sample)
            
            # Keep only recent training data (last 1000 samples)
            if len(self.training_data) > 1000:
                self.training_data = self.training_data[-1000:]
            
            # Check if we should retrain the meta-model
            if self._should_retrain():
                self._train_meta_model()
                
        except Exception as e:
            logger.error(f"Error collecting training data: {e}")
    
    def predict(self, base_predictions: Dict, market_features: Dict) -> Dict:
        """
        Make meta-model prediction based on base model outputs.
        
        Args:
            base_predictions: Dict with model_name -> {signal, confidence} predictions
            market_features: Dict with additional market context features
            
        Returns:
            Dict with meta-model prediction and confidence
        """
        try:
            if not self.is_trained:
                # Fallback to weighted ensemble if meta-model not trained
                return self._fallback_weighted_ensemble(base_predictions)
            
            # Create feature vector
            features = self._create_feature_vector(base_predictions, market_features)
            
            # Make prediction
            prediction = self.meta_model.predict([features])[0]
            
            # Get prediction probabilities for confidence
            if hasattr(self.meta_model, 'predict_proba'):
                probabilities = self.meta_model.predict_proba([features])[0]
                confidence = np.max(probabilities)
            else:
                confidence = 0.7  # Default confidence
            
            # Apply confidence threshold
            if confidence < self.confidence_threshold:
                prediction = 0  # No signal if confidence too low
            
            result = {
                'signal': int(prediction),
                'confidence': float(confidence),
                'meta_model_used': True,
                'base_model_count': len(base_predictions),
                'feature_vector_size': len(features)
            }
            
            logger.debug(f"Meta-model prediction: {prediction} (confidence: {confidence:.3f})")
            return result
            
        except Exception as e:
            logger.error(f"Error in meta-model prediction: {e}")
            return self._fallback_weighted_ensemble(base_predictions)
    
    def _create_feature_vector(self, base_predictions: Dict, market_features: Dict) -> List[float]:
        """Create feature vector from base model predictions and market features."""
        features = []
        
        # Base model signals and confidences
        for model_name in self.model_names:
            if model_name in base_predictions:
                pred = base_predictions[model_name]
                features.append(pred.get('signal', 0))
                features.append(pred.get('confidence', 0))
            else:
                features.extend([0, 0])  # Missing model
        
        # Agreement metrics
        signals = [base_predictions[m].get('signal', 0) for m in base_predictions.keys()]
        confidences = [base_predictions[m].get('confidence', 0) for m in base_predictions.keys()]
        
        if signals:
            features.extend([
                np.mean(signals),           # Average signal
                np.std(signals),            # Signal disagreement
                np.mean(confidences),       # Average confidence
                np.std(confidences),        # Confidence spread
                len([s for s in signals if s > 0]),  # Buy signals count
                len([s for s in signals if s < 0]),  # Sell signals count
                len([s for s in signals if s == 0]), # Neutral signals count
            ])
        else:
            features.extend([0] * 7)
        
        # Market context features
        market_feature_keys = ['volatility', 'trend_strength', 'volume_ratio', 'price_momentum']
        for key in market_feature_keys:
            features.append(market_features.get(key, 0))
        
        # Timeframe-specific features
        timeframe_signals = {'short_term': 0, 'medium_term': 0, 'long_term': 0}
        for model_name, pred in base_predictions.items():
            if 'short_term' in model_name:
                timeframe_signals['short_term'] += pred.get('signal', 0)
            elif 'medium_term' in model_name:
                timeframe_signals['medium_term'] += pred.get('signal', 0)
            elif 'long_term' in model_name:
                timeframe_signals['long_term'] += pred.get('signal', 0)
        
        features.extend(timeframe_signals.values())
        
        return features
    
    def _should_retrain(self) -> bool:
        """Check if meta-model should be retrained."""
        # Check if we have enough training data
        labeled_samples = [s for s in self.training_data if s['actual_outcome'] is not None]
        if len(labeled_samples) < self.min_training_samples:
            return False
        
        # Check if enough time has passed since last training
        if self.last_training_time is None:
            return True
        
        time_since_training = datetime.now() - self.last_training_time
        if time_since_training.total_seconds() > self.retrain_interval_hours * 3600:
            return True
        
        return False
    
    def _train_meta_model(self):
        """Train the meta-model using collected data."""
        try:
            # Filter training data with actual outcomes
            labeled_data = [s for s in self.training_data if s['actual_outcome'] is not None]
            
            if len(labeled_data) < self.min_training_samples:
                logger.warning(f"Not enough labeled data for training: {len(labeled_data)}")
                return
            
            # Prepare training data
            X = np.array([sample['features'] for sample in labeled_data])
            y = np.array([sample['actual_outcome'] for sample in labeled_data])
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Create and train meta-model
            if self.meta_model_type == "random_forest":
                self.meta_model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42,
                    class_weight='balanced'
                )
            elif self.meta_model_type == "gradient_boosting":
                self.meta_model = GradientBoostingClassifier(
                    n_estimators=100,
                    max_depth=6,
                    random_state=42
                )
            else:  # logistic_regression
                self.meta_model = LogisticRegression(
                    random_state=42,
                    class_weight='balanced',
                    max_iter=1000
                )
            
            # Train the model
            self.meta_model.fit(X_train, y_train)
            
            # Evaluate performance
            train_accuracy = accuracy_score(y_train, self.meta_model.predict(X_train))
            test_accuracy = accuracy_score(y_test, self.meta_model.predict(X_test))
            
            # Store performance
            performance = {
                'timestamp': datetime.now(),
                'train_accuracy': train_accuracy,
                'test_accuracy': test_accuracy,
                'training_samples': len(labeled_data),
                'model_type': self.meta_model_type
            }
            self.performance_history.append(performance)
            
            self.is_trained = True
            self.last_training_time = datetime.now()
            
            # Save the model
            self._save_meta_model()
            
            logger.info(f"Meta-model trained successfully: "
                       f"Train accuracy: {train_accuracy:.3f}, "
                       f"Test accuracy: {test_accuracy:.3f}, "
                       f"Samples: {len(labeled_data)}")
            
        except Exception as e:
            logger.error(f"Error training meta-model: {e}")
    
    def _fallback_weighted_ensemble(self, base_predictions: Dict) -> Dict:
        """Fallback to weighted ensemble when meta-model is not available."""
        if not base_predictions:
            return {'signal': 0, 'confidence': 0.0, 'meta_model_used': False}
        
        # Simple weighted voting
        total_weight = 0
        weighted_signal = 0
        
        for model_name, pred in base_predictions.items():
            signal = pred.get('signal', 0)
            confidence = pred.get('confidence', 0)
            weight = confidence
            
            weighted_signal += signal * weight
            total_weight += weight
        
        if total_weight > 0:
            final_signal = weighted_signal / total_weight
            final_signal = int(np.round(np.clip(final_signal, -2, 2)))
            confidence = min(total_weight / len(base_predictions), 1.0)
        else:
            final_signal = 0
            confidence = 0.0
        
        return {
            'signal': final_signal,
            'confidence': confidence,
            'meta_model_used': False,
            'fallback_method': 'weighted_ensemble'
        }
    
    def _save_meta_model(self):
        """Save the trained meta-model to disk."""
        try:
            if self.meta_model is not None:
                model_path = os.path.join(self.model_dir, "meta_model.pkl")
                joblib.dump(self.meta_model, model_path)
                
                # Save metadata
                metadata = {
                    'model_type': self.meta_model_type,
                    'is_trained': self.is_trained,
                    'last_training_time': self.last_training_time,
                    'performance_history': self.performance_history[-10:]  # Keep last 10 records
                }
                
                metadata_path = os.path.join(self.model_dir, "meta_model_metadata.pkl")
                with open(metadata_path, 'wb') as f:
                    pickle.dump(metadata, f)
                
                logger.info("Meta-model saved successfully")
                
        except Exception as e:
            logger.error(f"Error saving meta-model: {e}")
    
    def _load_meta_model(self):
        """Load existing meta-model from disk."""
        try:
            model_path = os.path.join(self.model_dir, "meta_model.pkl")
            metadata_path = os.path.join(self.model_dir, "meta_model_metadata.pkl")
            
            if os.path.exists(model_path) and os.path.exists(metadata_path):
                # Load model
                self.meta_model = joblib.load(model_path)
                
                # Load metadata
                with open(metadata_path, 'rb') as f:
                    metadata = pickle.load(f)
                
                self.meta_model_type = metadata.get('model_type', self.meta_model_type)
                self.is_trained = metadata.get('is_trained', False)
                self.last_training_time = metadata.get('last_training_time')
                self.performance_history = metadata.get('performance_history', [])
                
                logger.info("Meta-model loaded successfully")
                
        except Exception as e:
            logger.warning(f"Could not load existing meta-model: {e}")
    
    def get_performance_report(self) -> Dict:
        """Get performance report for the meta-model."""
        if not self.performance_history:
            return {'status': 'No training history available'}
        
        latest = self.performance_history[-1]
        
        return {
            'is_trained': self.is_trained,
            'model_type': self.meta_model_type,
            'last_training': self.last_training_time,
            'latest_performance': latest,
            'training_samples_collected': len(self.training_data),
            'labeled_samples': len([s for s in self.training_data if s['actual_outcome'] is not None]),
            'performance_trend': self.performance_history[-5:] if len(self.performance_history) >= 5 else self.performance_history
        }
