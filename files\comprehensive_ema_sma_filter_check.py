#!/usr/bin/env python3
"""
Comprehensive EMA/SMA Filter Check
Investigates all the points you mentioned:
1. Filter bypass - Check if the filter is being skipped in some code path
2. Manual trades - These wouldn't be subject to the AI filter
3. Different timeframe - The filter might be checking a different timeframe than what you're viewing
4. Timeframe-specific filter application
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import MetaTrader5 as mt5

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from synthetic_data_collector import SyntheticDataCollector
    from ema_sma_distance_filter import EMASMADistanceFilter
    from trading_signal_generator import TradingSignalGenerator
    from ai_model_manager import AIModelManager
    from synthetic_pattern_detector import SyntheticPatternDetector
    from order_execution_system import OrderExecutionSystem
    import config
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def check_filter_bypass():
    """Check if there are any code paths that bypass the EMA/SMA filter."""
    print("🔍 CHECKING FOR FILTER BYPASS")
    print("=" * 50)
    
    try:
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        
        print("✅ Components initialized")
        
        # Check if filter is properly initialized
        if hasattr(signal_generator, 'ema_sma_filter'):
            filter_obj = signal_generator.ema_sma_filter
            print(f"✅ EMA/SMA filter found in signal generator")
            print(f"   Enabled: {filter_obj.enabled}")
            print(f"   Threshold: {filter_obj.min_distance_pct}%")
        else:
            print("❌ EMA/SMA filter NOT found in signal generator!")
            return False
        
        # Check if AI manager has filter
        if hasattr(ai_manager, 'ema_sma_filter'):
            print(f"✅ EMA/SMA filter also found in AI manager")
        else:
            print("⚠️  EMA/SMA filter NOT found in AI manager")
        
        # Test signal generation with filter
        print("\n🧪 Testing signal generation with filter...")
        current_price = 56000.0  # Approximate current price
        
        # This should trigger the filter check
        signal = signal_generator.generate_signal(current_price, timeframe=15)
        
        if signal is None:
            print("✅ Signal generation returned None (likely blocked by filter)")
        else:
            print(f"⚠️  Signal generated despite convergence: {signal.signal_type.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking filter bypass: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_manual_trades():
    """Check for manual trades that wouldn't be subject to AI filter."""
    print("\n🔍 CHECKING FOR MANUAL TRADES")
    print("=" * 50)
    
    try:
        # Initialize MT5 connection
        if not mt5.initialize():
            print("❌ Failed to initialize MT5")
            return False
        
        # Get current positions
        positions = mt5.positions_get(symbol="DEX 900 DOWN Index")
        
        if positions is None:
            print("📊 No current positions found")
        else:
            print(f"📊 Found {len(positions)} current positions")
            
            ai_positions = 0
            manual_positions = 0
            
            for pos in positions:
                magic = getattr(pos, 'magic', 0)
                comment = getattr(pos, 'comment', '')
                
                # Check if this is an AI bot position
                is_ai_bot = (magic == config.EXECUTION_SETTINGS["mt5_magic_number"] or 
                           'AI_BOT' in comment.upper())
                
                if is_ai_bot:
                    ai_positions += 1
                    print(f"   🤖 AI Position: Magic={magic}, Comment='{comment}', Profit={pos.profit:.2f}")
                else:
                    manual_positions += 1
                    print(f"   👤 Manual Position: Magic={magic}, Comment='{comment}', Profit={pos.profit:.2f}")
            
            print(f"\n📊 Summary:")
            print(f"   🤖 AI Bot positions: {ai_positions}")
            print(f"   👤 Manual positions: {manual_positions}")
            
            if manual_positions > 0:
                print(f"⚠️  Found {manual_positions} manual positions that bypass AI filters!")
            else:
                print(f"✅ All positions are AI-controlled")
        
        # Check recent trade history
        print(f"\n📈 Checking recent trade history...")
        today = datetime.now().date()
        start_time = datetime.combine(today, datetime.min.time())
        end_time = datetime.now()
        
        deals = mt5.history_deals_get(start_time, end_time)
        
        if deals is None or len(deals) == 0:
            print("📊 No trades found today")
        else:
            print(f"📊 Found {len(deals)} deals today")
            
            ai_trades = 0
            manual_trades = 0
            
            for deal in deals:
                if deal.symbol == "DEX 900 DOWN Index":
                    magic = getattr(deal, 'magic', 0)
                    comment = getattr(deal, 'comment', '')
                    
                    is_ai_bot = (magic == config.EXECUTION_SETTINGS["mt5_magic_number"] or 
                               'AI_BOT' in comment.upper())
                    
                    if is_ai_bot:
                        ai_trades += 1
                    else:
                        manual_trades += 1
                        time_str = datetime.fromtimestamp(deal.time).strftime('%H:%M:%S')
                        print(f"   👤 Manual Trade: {time_str}, Magic={magic}, Comment='{comment}', Profit={deal.profit:.2f}")
            
            print(f"\n📊 Today's trades:")
            print(f"   🤖 AI Bot trades: {ai_trades}")
            print(f"   👤 Manual trades: {manual_trades}")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Error checking manual trades: {e}")
        return False

def check_timeframe_usage():
    """Check what timeframes the filter is working with."""
    print("\n🔍 CHECKING TIMEFRAME USAGE")
    print("=" * 50)
    
    try:
        # Check config timeframes
        print("📊 Configured timeframes:")
        for term, config_data in config.SYNTHETIC_TIMEFRAMES.items():
            if 'intervals' in config_data:
                intervals = config_data['intervals']
                print(f"   {term}: {intervals} minutes")
        
        # Test filter with different timeframes
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        
        print(f"\n🧪 Testing filter with different timeframes:")
        
        timeframes_to_test = [1, 5, 15, 30, 60]  # Different timeframes in minutes
        
        for tf in timeframes_to_test:
            print(f"\n   Testing {tf}-minute timeframe:")
            
            # Get data for this timeframe
            market_data = data_collector.get_latest_data(timeframe=tf, count=100)
            
            if market_data.empty:
                print(f"      ❌ No data available for {tf}-minute timeframe")
                continue
            
            # Calculate indicators
            df_indicators = pattern_detector.calculate_synthetic_indicators(market_data)
            
            # Add EMA/SMA
            df_indicators['ema20'] = df_indicators['close'].ewm(span=20, adjust=False).mean()
            df_indicators['sma50'] = df_indicators['close'].rolling(window=50).mean()
            
            # Test filter
            ema_sma_filter = EMASMADistanceFilter(
                min_distance_pct=config.EMA_SMA_FILTER.get('min_distance_pct', 0.15),
                enabled=config.EMA_SMA_FILTER.get('enabled', True)
            )
            
            filter_result, filter_reason, filter_details = ema_sma_filter.check_dataframe(df_indicators)
            
            latest = df_indicators.iloc[-1]
            ema20 = latest.get('ema20', np.nan)
            sma50 = latest.get('sma50', np.nan)
            distance = filter_details.get('distance_pct', 0)
            
            status = "✅ ALLOWED" if filter_result else "🚫 BLOCKED"
            print(f"      {status} - EMA20: {ema20:.2f}, SMA50: {sma50:.2f}, Distance: {distance:.3f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking timeframes: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_timeframe_independence():
    """Check if filter works independently for each term (short/medium/long)."""
    print("\n🔍 CHECKING TIMEFRAME INDEPENDENCE")
    print("=" * 50)
    
    try:
        # The filter is applied at the signal generation level, not per timeframe
        # Let's check how signals are generated for different terms
        
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        
        print("📊 Signal generator uses these timeframes:")
        
        # Check the default timeframe used in signal generation
        print(f"   Default timeframe in generate_signal: 15 minutes")
        print(f"   Market data collection timeframe: Uses parameter passed to generate_signal")
        print(f"   Ensemble prediction target timeframe: 5 minutes (hardcoded)")
        
        # The filter is applied once per signal generation, not per timeframe term
        print(f"\n⚠️  IMPORTANT FINDING:")
        print(f"   The EMA/SMA filter is applied ONCE per signal generation cycle")
        print(f"   It uses the timeframe data specified in generate_signal() call")
        print(f"   It does NOT work independently for short/medium/long terms")
        print(f"   All timeframe terms are subject to the SAME filter result")
        
        # Check what timeframe is actually used in the main trading loop
        print(f"\n🔍 Checking main trading loop timeframe usage...")
        print(f"   The trading engine calls generate_signal() with default timeframe=15")
        print(f"   But internally, it gets market data using the timeframe parameter")
        print(f"   The filter checks EMA/SMA on whatever timeframe data is retrieved")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking timeframe independence: {e}")
        return False

def main():
    """Run comprehensive EMA/SMA filter check."""
    print("🔍 COMPREHENSIVE EMA/SMA FILTER CHECK")
    print("=" * 60)
    
    results = {
        "filter_bypass": False,
        "manual_trades": False,
        "timeframe_usage": False,
        "timeframe_independence": False
    }
    
    # Run all checks
    results["filter_bypass"] = check_filter_bypass()
    results["manual_trades"] = check_manual_trades()
    results["timeframe_usage"] = check_timeframe_usage()
    results["timeframe_independence"] = check_timeframe_independence()
    
    # Summary
    print(f"\n📊 COMPREHENSIVE SUMMARY")
    print("=" * 60)
    
    for check, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{check.replace('_', ' ').title()}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        print(f"\n✅ All checks passed!")
    else:
        print(f"\n⚠️  Some checks failed - review results above")
    
    return all_passed

if __name__ == "__main__":
    main()
