#!/usr/bin/env python3
"""
Show only the opening trades that should match your MT5 history view.
"""

import MetaTrader5 as mt5
from datetime import datetime

def show_mt5_opening_trades():
    """Show only opening trades to match with MT5 history."""
    print("📊 MT5 OPENING TRADES (What you should see in MT5 History)")
    print("=" * 70)
    
    # Initialize MT5
    if not mt5.initialize():
        print('❌ MT5 initialization failed')
        return
    
    # Get today's trades
    today = datetime.now().date()
    start_time = datetime.combine(today, datetime.min.time())
    end_time = datetime.now()
    
    print(f"📅 Checking opening trades from {start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%H:%M')}")
    
    # Get deals from MT5
    deals = mt5.history_deals_get(start_time, end_time, group="*")
    
    if deals is None:
        print("❌ No deals found")
        mt5.shutdown()
        return
    
    # Filter for AI bot opening trades only
    opening_trades = []
    timeframe_counts = {'short_term': 0, 'medium_term': 0, 'long_term': 0}
    
    for deal in deals:
        if (hasattr(deal, 'magic') and deal.magic == 54321 and
            hasattr(deal, 'symbol') and deal.symbol == 'DEX 900 DOWN Index' and
            hasattr(deal, 'entry') and deal.entry == 0):  # Entry = 0 means opening trade
            
            comment = getattr(deal, 'comment', '')
            time_str = datetime.fromtimestamp(deal.time).strftime('%H:%M:%S')
            
            # Determine timeframe
            timeframe = 'unknown'
            comment_upper = comment.upper()
            if 'AI_BOT_SHORT' in comment_upper:
                timeframe = 'short_term'
                timeframe_counts['short_term'] += 1
            elif 'AI_BOT_MEDIUM' in comment_upper:
                timeframe = 'medium_term'
                timeframe_counts['medium_term'] += 1
            elif 'AI_BOT_LONG' in comment_upper:
                timeframe = 'long_term'
                timeframe_counts['long_term'] += 1
            
            opening_trades.append({
                'time': time_str,
                'timeframe': timeframe,
                'comment': comment,
                'volume': deal.volume,
                'price': deal.price
            })
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Total Opening Trades: {len(opening_trades)}")
    print(f"   Short Term: {timeframe_counts['short_term']}")
    print(f"   Medium Term: {timeframe_counts['medium_term']}")
    print(f"   Long Term: {timeframe_counts['long_term']}")
    
    print(f"\n📋 DETAILED OPENING TRADES:")
    print(f"{'Time':>8} | {'Timeframe':>10} | {'Volume':>6} | {'Price':>8} | Comment")
    print("-" * 70)
    
    for trade in opening_trades:
        print(f"{trade['time']:>8} | {trade['timeframe']:>10} | {trade['volume']:>6} | {trade['price']:>8.2f} | {trade['comment']}")
    
    print(f"\n💡 TO VERIFY IN MT5:")
    print(f"   1. Open MT5 Terminal")
    print(f"   2. Go to 'History' tab")
    print(f"   3. Right-click → Select 'Deals'")
    print(f"   4. Look for Magic Number: 54321")
    print(f"   5. Look for Entry Type: 'in' (opening trades)")
    print(f"   6. Count trades with comments starting with 'AI_BOT_'")
    
    mt5.shutdown()

if __name__ == "__main__":
    show_mt5_opening_trades()
