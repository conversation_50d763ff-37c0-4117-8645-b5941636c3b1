#!/usr/bin/env python3
"""
Enable Detailed Logging for AI Trading System
Shows AI model re-analysis activity in real-time.
"""

import logging
import sys
import os

def setup_detailed_logging():
    """Configure detailed logging to show AI model activity."""
    
    # Set up root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create detailed formatter
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler with detailed output
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler for detailed logs
    os.makedirs('logs', exist_ok=True)
    file_handler = logging.FileHandler('logs/detailed_ai_analysis.log')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(file_handler)
    
    # Set specific logger levels for AI components
    ai_loggers = [
        'AIModelManager',
        'TradingSignalGenerator', 
        'TradingEngine',
        'SyntheticPatternDetector',
        'SyntheticDataCollector',
        'OrderExecutionSystem'
    ]
    
    for logger_name in ai_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)
    
    print("🔍 DETAILED LOGGING ENABLED")
    print("=" * 60)
    print("📊 AI Model Analysis Activity Will Now Be Visible")
    print("🧠 Ensemble Predictions: DEBUG level enabled")
    print("⚡ Signal Generation: DEBUG level enabled") 
    print("🔄 Trading Cycles: DEBUG level enabled")
    print("📁 Logs saved to: logs/detailed_ai_analysis.log")
    print("=" * 60)

if __name__ == "__main__":
    setup_detailed_logging()
    
    # Import and start the trading system with detailed logging
    try:
        from trading_engine import TradingEngine
        
        print("🚀 Starting AI Trading System with Detailed Logging...")
        
        engine = TradingEngine()
        
        if engine.initialize_system():
            print("✅ System initialized - AI model activity will be shown below:")
            print("=" * 60)
            
            # Start trading with detailed logs
            engine.start_trading()
            
        else:
            print("❌ System initialization failed")
            
    except KeyboardInterrupt:
        print("\n⚠️ System stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")
