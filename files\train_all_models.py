#!/usr/bin/env python3
"""
AI Model Training Script
Pre-trains all 9 AI models for the DEX 900 DOWN trading system.
Run this once to train and save all models to disk.
"""

import os
import sys
import logging
import time
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from ai_model_manager import AIModelManager
from synthetic_data_collector import SyntheticDataCollector
from synthetic_pattern_detector import SyntheticPatternDetector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/model_training.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ModelTrainer:
    """Dedicated model training class."""
    
    def __init__(self):
        """Initialize the model trainer."""
        logger.info("Initializing Model Trainer...")
        
        # Create necessary directories
        os.makedirs("models/saved", exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        os.makedirs("data/synthetic_cache", exist_ok=True)
        
        # Initialize components
        self.data_collector = SyntheticDataCollector()
        self.pattern_detector = SyntheticPatternDetector(self.data_collector)
        self.ai_manager = AIModelManager(self.data_collector, self.pattern_detector)
        
        logger.info("Model Trainer initialized successfully")
    
    def train_all_models(self):
        """Train all 9 AI models and save them to disk."""
        logger.info("=" * 80)
        logger.info("STARTING COMPREHENSIVE AI MODEL TRAINING")
        logger.info("DEX 900 DOWN AI Trading System - 9 Model Ensemble")
        logger.info("=" * 80)
        
        start_time = time.time()
        
        try:
            # Step 1: Ensure we have sufficient historical data
            logger.info("Step 1: Ensuring comprehensive historical data...")
            self._ensure_comprehensive_data()
            
            # Step 2: Train all models
            logger.info("Step 2: Training all 9 AI models...")
            print("Starting training of 9 AI models...")
            results = self._train_models_with_progress()
            
            # Step 3: Validate all models
            logger.info("Step 3: Validating trained models...")
            validation_results = self._validate_models(results)
            
            # Step 4: Generate training report
            logger.info("Step 4: Generating training report...")
            self._generate_training_report(results, validation_results, start_time)
            
            return results
            
        except Exception as e:
            logger.error(f"Error during model training: {e}")
            raise
    
    def _ensure_comprehensive_data(self):
        """Ensure we have comprehensive historical data for training."""
        try:
            from datetime import datetime, timedelta
            
            logger.info("Collecting comprehensive historical data...")
            
            # Collect 60 days of data for robust training
            end_date = datetime.now()
            start_date = end_date - timedelta(days=60)
            
            logger.info(f"Data collection period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            
            # Collect data for all required timeframes
            timeframes = [1, 5, 15, 30, 60, 240, 1440]  # 1min to daily
            
            for timeframe in timeframes:
                logger.info(f"Collecting {timeframe}min historical data...")
                self.data_collector.collect_timeframe_data(timeframe, start_date, end_date)
                
                # Verify data collection
                df = self.data_collector.get_latest_data(timeframe, count=100)
                if not df.empty:
                    logger.info(f"[SUCCESS] {timeframe}min data: {len(df)} records available")
                else:
                    logger.warning(f"[FAILED] {timeframe}min data: No records collected")
            
            logger.info("Historical data collection completed")

        except Exception as e:
            logger.error(f"Error collecting historical data: {e}")
            raise

    def _train_models_with_progress(self):
        """Train all models with progress display."""
        model_configs = self.ai_manager.model_configs
        results = {}

        total_models = len(model_configs)
        completed_models = 0

        print(f"Training {total_models} AI models for DEX 900 DOWN Index...")
        print("=" * 60)

        for model_name, config in model_configs.items():
            try:
                print(f"Training model: {model_name}")
                print(f"Progress: {completed_models + 1}/{total_models}")

                # Train the model
                success = self.ai_manager.train_model(model_name)

                if success:
                    print(f"Model saved: {model_name}")
                    print(f"Training completed for {model_name}")
                    completed_models += 1
                    print(f"SUCCESS: {model_name} ({completed_models}/{total_models} completed)")
                else:
                    print(f"ERROR: Failed to train {model_name}")
                    print(f"FAILED: {model_name}")

                results[model_name] = success
                print("-" * 40)

            except Exception as e:
                print(f"ERROR: Exception training {model_name}: {e}")
                results[model_name] = False
                print("-" * 40)

        print("=" * 60)
        print(f"Training completed: {completed_models}/{total_models} models successful")

        return results
    
    def _validate_models(self, training_results):
        """Validate that all models were trained successfully."""
        validation_results = {}
        
        logger.info("Validating trained models...")
        
        for model_name, success in training_results.items():
            if success:
                # Check if model files exist
                model_dir = f"models/saved/{model_name}"
                required_files = ["model.pkl", "scaler.pkl", "label_encoder.pkl", "config.pkl"]
                
                # For TensorFlow models, check for .keras file instead of .pkl
                config = self.ai_manager.model_configs.get(model_name, {})
                if config.get("model_class") in ["lstm", "deep_nn"]:
                    required_files[0] = "model.keras"
                
                all_files_exist = all(
                    os.path.exists(os.path.join(model_dir, file)) 
                    for file in required_files
                )
                
                if all_files_exist:
                    # Test model loading
                    try:
                        load_success = self.ai_manager.load_model(model_name)
                        validation_results[model_name] = {
                            "trained": True,
                            "files_saved": True,
                            "loads_successfully": load_success,
                            "performance": self.ai_manager.model_performance.get(model_name, {})
                        }
                        logger.info(f"[SUCCESS] {model_name}: Validated successfully")
                    except Exception as e:
                        validation_results[model_name] = {
                            "trained": True,
                            "files_saved": True,
                            "loads_successfully": False,
                            "error": str(e)
                        }
                        logger.error(f"[FAILED] {model_name}: Failed to load - {e}")
                else:
                    validation_results[model_name] = {
                        "trained": True,
                        "files_saved": False,
                        "loads_successfully": False,
                        "missing_files": [f for f in required_files if not os.path.exists(os.path.join(model_dir, f))]
                    }
                    logger.error(f"[FAILED] {model_name}: Missing saved files")
            else:
                validation_results[model_name] = {
                    "trained": False,
                    "files_saved": False,
                    "loads_successfully": False
                }
                logger.error(f"[FAILED] {model_name}: Training failed")
        
        return validation_results
    
    def _generate_training_report(self, training_results, validation_results, start_time):
        """Generate comprehensive training report."""
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info("=" * 80)
        logger.info("AI MODEL TRAINING REPORT")
        logger.info("=" * 80)
        
        # Training summary
        successful_training = sum(training_results.values())
        total_models = len(training_results)
        
        logger.info(f"Training Duration: {duration:.1f} seconds ({duration/60:.1f} minutes)")
        logger.info(f"Models Trained: {successful_training}/{total_models}")
        
        # Validation summary
        successful_validation = sum(
            1 for result in validation_results.values() 
            if result.get("loads_successfully", False)
        )
        
        logger.info(f"Models Validated: {successful_validation}/{total_models}")
        
        # Detailed results
        logger.info("\nDETAILED RESULTS:")
        logger.info("-" * 50)
        
        for model_name in training_results.keys():
            training_success = training_results[model_name]
            validation = validation_results.get(model_name, {})
            performance = validation.get("performance", {})
            accuracy = performance.get("accuracy", 0)
            
            status = "[SUCCESS]" if (training_success and validation.get("loads_successfully", False)) else "[FAILED]"
            
            logger.info(f"{model_name:25} | {status:10} | Accuracy: {accuracy:.3f}")
        
        # Final status
        if successful_validation == total_models:
            logger.info("\n[SUCCESS] ALL 9 MODELS TRAINED AND VALIDATED SUCCESSFULLY!")
            logger.info("The AI trading system is ready for live deployment.")
        else:
            logger.warning(f"\n[WARNING] {total_models - successful_validation} models failed validation.")
            logger.warning("Please review the errors above and retry training.")
        
        logger.info("=" * 80)

def main():
    """Main training function."""
    print("AI MODEL TRAINING SYSTEM")
    print("=" * 50)
    print("This will train all 9 AI models for the DEX 900 DOWN trading system.")
    print("Training may take 5-15 minutes depending on your hardware.")
    print("=" * 50)

    # Skip confirmation if called from orchestrator
    if len(sys.argv) > 1 and sys.argv[1] == "--auto":
        print("Auto-training mode: Starting immediately...")
    else:
        # Confirm training
        response = input("Start comprehensive model training? (y/N): ").strip().lower()
        if response != 'y':
            print("Training cancelled.")
            return
    
    try:
        # Initialize trainer
        trainer = ModelTrainer()
        
        # Train all models
        results = trainer.train_all_models()
        
        # Success message
        successful = sum(results.values())
        total = len(results)
        
        if successful == total:
            print(f"\nSUCCESS! All {total} models trained successfully!")
            print("The AI trading system is now ready for live deployment.")
        else:
            print(f"\nWARNING: {successful}/{total} models trained successfully.")
            print("Some models may need attention. Check the logs for details.")
            
    except KeyboardInterrupt:
        print("\n\nTraining interrupted by user.")
    except Exception as e:
        print(f"\nERROR: Training failed: {e}")
        logger.error(f"Training failed: {e}")

if __name__ == "__main__":
    main()
