#!/usr/bin/env python3
"""
Test script to check why the dashboard is showing stale prices instead of live MT5 prices.
"""

import sys
import logging
import time
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_live_price_update():
    """Test live price update mechanism."""
    try:
        print("🔍 TESTING LIVE PRICE UPDATE MECHANISM")
        print("=" * 60)
        print("⚠️  Checking why dashboard shows stale prices")
        print("=" * 60)
        
        # Import required modules
        from synthetic_data_collector import SyntheticDataCollector
        import MetaTrader5 as mt5
        
        print("📊 Initializing data collector...")
        data_collector = SyntheticDataCollector()
        
        if not data_collector.mt5_connected:
            print("❌ MT5 not connected!")
            return False
            
        print("✅ MT5 connected")
        
        # Test 1: Get live tick data directly from MT5
        print("\n🎯 TEST 1: DIRECT MT5 TICK DATA")
        tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
        if tick:
            live_price = (tick.bid + tick.ask) / 2
            print(f"✅ Live MT5 Price: {live_price:.2f}")
            print(f"   Bid: {tick.bid:.2f}")
            print(f"   Ask: {tick.ask:.2f}")
            print(f"   Time: {datetime.fromtimestamp(tick.time)}")
        else:
            print("❌ Could not get live tick data!")
            return False
        
        # Test 2: Get latest data from database
        print("\n🎯 TEST 2: DATABASE LATEST DATA")
        try:
            latest_data = data_collector.get_latest_data(1, 1)  # Same call as dashboard
            if latest_data is not None and not latest_data.empty:
                db_price = float(latest_data.iloc[-1]['close'])
                db_timestamp = latest_data.iloc[-1]['timestamp']
                print(f"✅ Database Price: {db_price:.2f}")
                print(f"   Timestamp: {db_timestamp}")
                
                # Compare prices
                price_diff = abs(live_price - db_price)
                time_diff = datetime.now() - pd.to_datetime(db_timestamp)
                
                print(f"\n📊 COMPARISON:")
                print(f"   Price difference: {price_diff:.2f} points")
                print(f"   Time difference: {time_diff}")
                
                if price_diff > 10:
                    print(f"❌ SIGNIFICANT PRICE DIFFERENCE! Database is stale")
                else:
                    print(f"✅ Prices are close")
                    
                if time_diff.total_seconds() > 300:  # 5 minutes
                    print(f"❌ DATABASE DATA IS OLD! ({time_diff})")
                else:
                    print(f"✅ Database data is recent")
                    
            else:
                print("❌ No data in database!")
                return False
                
        except Exception as e:
            print(f"❌ Error getting database data: {e}")
            return False
        
        # Test 3: Check if real-time collection is running
        print("\n🎯 TEST 3: REAL-TIME COLLECTION STATUS")
        print(f"   Is collecting: {data_collector.is_collecting}")
        print(f"   Real-time thread: {data_collector.real_time_thread}")
        
        if not data_collector.is_collecting:
            print("❌ REAL-TIME COLLECTION NOT RUNNING!")
            print("🔧 Starting real-time collection...")
            data_collector.start_real_time_collection()
            time.sleep(2)
            print(f"   Is collecting now: {data_collector.is_collecting}")
        else:
            print("✅ Real-time collection is running")
        
        # Test 4: Monitor real-time updates for 30 seconds
        print("\n🎯 TEST 4: MONITORING REAL-TIME UPDATES")
        print("   Monitoring for 30 seconds...")
        
        start_time = datetime.now()
        last_price = live_price
        update_count = 0
        
        for i in range(30):  # 30 seconds
            try:
                # Get current tick
                current_tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
                if current_tick:
                    current_price = (current_tick.bid + current_tick.ask) / 2
                    
                    if abs(current_price - last_price) > 0.01:  # Price changed
                        update_count += 1
                        print(f"   Update {update_count}: {current_price:.2f} (change: {current_price - last_price:+.2f})")
                        last_price = current_price
                
                # Check database update
                if i % 10 == 0:  # Every 10 seconds
                    try:
                        latest_db = data_collector.get_latest_data(1, 1)
                        if latest_db is not None and not latest_db.empty:
                            db_price_now = float(latest_db.iloc[-1]['close'])
                            db_time_now = latest_db.iloc[-1]['timestamp']
                            print(f"   DB Check: {db_price_now:.2f} at {db_time_now}")
                    except:
                        pass
                
                time.sleep(1)
                
            except Exception as e:
                print(f"   Error in monitoring: {e}")
        
        print(f"\n📊 MONITORING RESULTS:")
        print(f"   Price updates detected: {update_count}")
        print(f"   Monitoring duration: 30 seconds")
        
        # Test 5: Fix the dashboard price update
        print("\n🎯 TEST 5: DASHBOARD PRICE UPDATE FIX")
        print("   Testing direct MT5 price for dashboard...")
        
        # Create a fixed price update function
        def get_live_dashboard_price():
            """Get live price for dashboard (bypassing stale database)."""
            try:
                tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
                if tick:
                    price = (tick.bid + tick.ask) / 2
                    return {
                        "price": float(price),
                        "change": 0.0,  # Would need previous price to calculate
                        "change_pct": 0.0,  # Would need previous price to calculate
                        "timestamp": datetime.fromtimestamp(tick.time).isoformat()
                    }
            except Exception as e:
                logger.error(f"Error getting live price: {e}")
            return None
        
        live_dashboard_price = get_live_dashboard_price()
        if live_dashboard_price:
            print(f"✅ Live dashboard price: {live_dashboard_price['price']:.2f}")
            print(f"   Timestamp: {live_dashboard_price['timestamp']}")
        else:
            print("❌ Could not get live dashboard price")
        
        # Summary
        print(f"\n📋 DIAGNOSIS SUMMARY:")
        print(f"   Live MT5 Price: {live_price:.2f}")
        print(f"   Database Price: {db_price:.2f}")
        print(f"   Price Difference: {price_diff:.2f} points")
        print(f"   Real-time Collection: {'Running' if data_collector.is_collecting else 'NOT RUNNING'}")
        
        if price_diff > 10:
            print(f"\n🔧 RECOMMENDED FIX:")
            print(f"   1. Dashboard should get live MT5 prices directly")
            print(f"   2. Real-time data collection needs to be active")
            print(f"   3. Database updates should be more frequent")
            return False
        else:
            print(f"\n✅ PRICE UPDATE MECHANISM WORKING")
            return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 LIVE PRICE UPDATE TEST")
    print("=" * 60)
    print("⚠️  This checks why dashboard shows stale prices")
    print("=" * 60)
    
    success = test_live_price_update()
    
    if success:
        print("\n🎉 LIVE PRICE UPDATE TEST PASSED!")
        print("✅ Price update mechanism is working correctly!")
    else:
        print("\n❌ LIVE PRICE UPDATE TEST FAILED!")
        print("🔧 Dashboard price update mechanism needs fixing.")
        
    sys.exit(0 if success else 1)
