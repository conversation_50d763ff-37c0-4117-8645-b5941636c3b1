#!/usr/bin/env python3
"""
Diagnose EMA/SMA Data Issue
Check actual EMA/SMA values being calculated for different timeframes
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime
import os

def main():
    # Connect to the database
    db_path = 'synthetic_data.db'
    if not os.path.exists(db_path):
        print(f"Database not found: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    
    try:
        # Get latest data for 5-minute timeframe
        query_5min = "SELECT * FROM ohlcv_data WHERE timeframe = 5 ORDER BY timestamp DESC LIMIT 100"
        df_5min = pd.read_sql_query(query_5min, conn)
        
        # Get latest data for 30-minute timeframe
        query_30min = "SELECT * FROM ohlcv_data WHERE timeframe = 30 ORDER BY timestamp DESC LIMIT 100"
        df_30min = pd.read_sql_query(query_30min, conn)
        
        print('=== 5-MINUTE TIMEFRAME DATA ===')
        print(f'Records available: {len(df_5min)}')
        if len(df_5min) > 0:
            df_5min['timestamp'] = pd.to_datetime(df_5min['timestamp'], unit='s')
            df_5min = df_5min.sort_values('timestamp')
            df_5min['ema20'] = df_5min['close'].ewm(span=20, adjust=False).mean()
            df_5min['sma50'] = df_5min['close'].rolling(window=50).mean()
            
            latest_5min = df_5min.iloc[-1]
            print(f'Latest timestamp: {latest_5min["timestamp"]}')
            print(f'Latest close: {latest_5min["close"]:.2f}')
            print(f'Latest EMA20: {latest_5min["ema20"]:.2f}')
            print(f'Latest SMA50: {latest_5min["sma50"]:.2f}')
            
            # Calculate gap
            if not pd.isna(latest_5min['ema20']) and not pd.isna(latest_5min['sma50']):
                gap_pct = abs(latest_5min['ema20'] - latest_5min['sma50']) / latest_5min['close'] * 100
                print(f'Gap percentage: {gap_pct:.3f}%')
            
            # Show last 5 records
            print('\nLast 5 records:')
            for i in range(max(0, len(df_5min)-5), len(df_5min)):
                row = df_5min.iloc[i]
                ema20 = row['ema20'] if not pd.isna(row['ema20']) else 'N/A'
                sma50 = row['sma50'] if not pd.isna(row['sma50']) else 'N/A'
                print(f'{row["timestamp"]} - Close: {row["close"]:.2f}, EMA20: {ema20}, SMA50: {sma50}')
        else:
            print('No 5-minute data found')
        
        print('\n=== 30-MINUTE TIMEFRAME DATA ===')
        print(f'Records available: {len(df_30min)}')
        if len(df_30min) > 0:
            df_30min['timestamp'] = pd.to_datetime(df_30min['timestamp'], unit='s')
            df_30min = df_30min.sort_values('timestamp')
            df_30min['ema20'] = df_30min['close'].ewm(span=20, adjust=False).mean()
            df_30min['sma50'] = df_30min['close'].rolling(window=50).mean()
            
            latest_30min = df_30min.iloc[-1]
            print(f'Latest timestamp: {latest_30min["timestamp"]}')
            print(f'Latest close: {latest_30min["close"]:.2f}')
            print(f'Latest EMA20: {latest_30min["ema20"]:.2f}')
            print(f'Latest SMA50: {latest_30min["sma50"]:.2f}')
            
            # Calculate gap
            if not pd.isna(latest_30min['ema20']) and not pd.isna(latest_30min['sma50']):
                gap_pct = abs(latest_30min['ema20'] - latest_30min['sma50']) / latest_30min['close'] * 100
                print(f'Gap percentage: {gap_pct:.3f}%')
            
            # Show last 5 records
            print('\nLast 5 records:')
            for i in range(max(0, len(df_30min)-5), len(df_30min)):
                row = df_30min.iloc[i]
                ema20 = row['ema20'] if not pd.isna(row['ema20']) else 'N/A'
                sma50 = row['sma50'] if not pd.isna(row['sma50']) else 'N/A'
                print(f'{row["timestamp"]} - Close: {row["close"]:.2f}, EMA20: {ema20}, SMA50: {sma50}')
        else:
            print('No 30-minute data found')
        
        # Check data freshness
        print('\n=== DATA FRESHNESS CHECK ===')
        current_time = datetime.now()
        
        if len(df_5min) > 0:
            latest_5min_time = pd.to_datetime(df_5min.iloc[-1]['timestamp'], unit='s')
            time_diff_5min = current_time - latest_5min_time
            print(f'5-minute data age: {time_diff_5min}')
        
        if len(df_30min) > 0:
            latest_30min_time = pd.to_datetime(df_30min.iloc[-1]['timestamp'], unit='s')
            time_diff_30min = current_time - latest_30min_time
            print(f'30-minute data age: {time_diff_30min}')
        
        # Check all available timeframes
        print('\n=== ALL AVAILABLE TIMEFRAMES ===')
        query_timeframes = "SELECT DISTINCT timeframe, COUNT(*) as count, MAX(timestamp) as latest FROM ohlcv_data GROUP BY timeframe ORDER BY timeframe"
        df_timeframes = pd.read_sql_query(query_timeframes, conn)
        
        for _, row in df_timeframes.iterrows():
            latest_time = pd.to_datetime(row['latest'], unit='s')
            print(f'Timeframe {row["timeframe"]}min: {row["count"]} records, latest: {latest_time}')
    
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()