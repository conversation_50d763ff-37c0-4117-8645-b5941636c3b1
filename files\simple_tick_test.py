#!/usr/bin/env python3
"""
Simple test to check if basic tick collection works.
"""

import MetaTrader5 as mt5
import time
import sqlite3
import os
from datetime import datetime
import threading

def test_basic_tick_collection():
    """Test basic tick collection without OHLCV aggregation."""
    
    print("🧪 TESTING BASIC TICK COLLECTION")
    print("=" * 40)
    
    # Initialize MT5
    if not mt5.initialize():
        print("❌ MT5 initialization failed")
        return False
    
    print("✅ MT5 initialized")
    
    # Test symbol
    symbol = "DEX 900 DOWN Index"
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        print(f"❌ No tick data for {symbol}")
        mt5.shutdown()
        return False
    
    print(f"✅ Current tick: {tick.bid:.2f}/{tick.ask:.2f}")
    
    # Setup database
    db_path = "data/synthetic_cache/synthetic_data.db"
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    conn = sqlite3.connect(db_path, check_same_thread=False)
    cursor = conn.cursor()
    
    # Create table if not exists
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS tick_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp REAL,
            bid REAL,
            ask REAL,
            last REAL,
            volume INTEGER,
            time_msc INTEGER,
            flags INTEGER,
            volume_real REAL
        )
    """)
    conn.commit()
    
    # Count initial ticks
    cursor.execute("SELECT COUNT(*) FROM tick_data WHERE timestamp > ?", (time.time() - 60,))
    initial_count = cursor.fetchone()[0]
    print(f"Initial recent ticks: {initial_count}")
    
    # Collect ticks for 10 seconds
    print("Collecting ticks for 10 seconds...")
    start_time = time.time()
    tick_count = 0
    
    while time.time() - start_time < 10:
        tick = mt5.symbol_info_tick(symbol)
        if tick:
            try:
                cursor.execute("""
                    INSERT INTO tick_data 
                    (timestamp, bid, ask, last, volume, time_msc, flags, volume_real)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    tick.time,
                    tick.bid,
                    tick.ask,
                    tick.last,
                    tick.volume,
                    tick.time_msc,
                    tick.flags,
                    tick.volume_real
                ))
                conn.commit()
                tick_count += 1
                
                if tick_count % 10 == 0:
                    print(f"  Collected {tick_count} ticks...")
                    
            except Exception as e:
                print(f"❌ Error storing tick: {e}")
        
        time.sleep(0.1)  # 100ms interval
    
    # Check results
    cursor.execute("SELECT COUNT(*) FROM tick_data WHERE timestamp > ?", (time.time() - 60,))
    final_count = cursor.fetchone()[0]
    new_ticks = final_count - initial_count
    
    print(f"✅ Collection completed")
    print(f"Ticks collected: {new_ticks}")
    
    if new_ticks > 0:
        cursor.execute("SELECT timestamp, bid, ask FROM tick_data ORDER BY timestamp DESC LIMIT 3")
        latest = cursor.fetchall()
        print("Latest ticks:")
        for tick in latest:
            dt = datetime.fromtimestamp(tick[0])
            print(f"  {dt}: {tick[1]:.2f}/{tick[2]:.2f}")
        
        success = True
    else:
        print("❌ No ticks collected")
        success = False
    
    # Cleanup
    conn.close()
    mt5.shutdown()
    
    return success

if __name__ == "__main__":
    try:
        success = test_basic_tick_collection()
        
        if success:
            print("\n🎉 BASIC TICK COLLECTION: WORKING")
        else:
            print("\n💥 BASIC TICK COLLECTION: FAILED")
            
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
