#!/usr/bin/env python3
"""
Debug script to investigate why real-time data aggregation is not working.
"""

import time
import pandas as pd
from datetime import datetime, timedelta
from synthetic_data_collector import SyntheticDataCollector
import logging
import sqlite3

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_real_time_collection():
    """Debug the real-time data collection process."""
    
    print("🔍 DEBUGGING REAL-TIME DATA COLLECTION")
    print("=" * 60)
    
    # Initialize data collector
    print("\n1️⃣ Initializing data collector...")
    collector = SyntheticDataCollector()
    
    # Check database structure
    print("\n2️⃣ Checking database structure...")
    cursor = collector.conn.cursor()
    
    # Check tick_data table
    cursor.execute("SELECT COUNT(*) FROM tick_data")
    tick_count_before = cursor.fetchone()[0]
    print(f"   Tick data records before: {tick_count_before}")
    
    # Check ohlcv_data table
    cursor.execute("SELECT COUNT(*) FROM ohlcv_data")
    ohlcv_count_before = cursor.fetchone()[0]
    print(f"   OHLCV data records before: {ohlcv_count_before}")
    
    # Start real-time collection
    print("\n3️⃣ Starting real-time data collection...")
    collector.start_real_time_collection()
    
    if not collector.is_collecting:
        print("❌ Failed to start real-time collection!")
        return False
    
    print("✅ Real-time collection started")
    
    # Monitor tick collection for 30 seconds
    print("\n4️⃣ Monitoring tick collection (30 seconds)...")
    
    for i in range(6):  # Check every 5 seconds for 30 seconds
        time.sleep(5)
        
        # Check tick data
        cursor.execute("SELECT COUNT(*) FROM tick_data")
        current_tick_count = cursor.fetchone()[0]
        new_ticks = current_tick_count - tick_count_before
        
        # Check OHLCV data
        cursor.execute("SELECT COUNT(*) FROM ohlcv_data")
        current_ohlcv_count = cursor.fetchone()[0]
        new_ohlcv = current_ohlcv_count - ohlcv_count_before
        
        print(f"   Check {i+1}/6: +{new_ticks} ticks, +{new_ohlcv} OHLCV records")
        
        # Show latest tick if available
        if new_ticks > 0:
            cursor.execute("SELECT * FROM tick_data ORDER BY timestamp DESC LIMIT 1")
            latest_tick = cursor.fetchone()
            if latest_tick:
                tick_time = datetime.fromtimestamp(latest_tick[1])
                print(f"      Latest tick: {tick_time} - Bid: {latest_tick[2]:.2f}, Ask: {latest_tick[3]:.2f}")
        
        # Show latest OHLCV if available
        if new_ohlcv > 0:
            cursor.execute("SELECT * FROM ohlcv_data ORDER BY timestamp DESC LIMIT 1")
            latest_ohlcv = cursor.fetchone()
            if latest_ohlcv:
                ohlcv_time = datetime.fromtimestamp(latest_ohlcv[1])
                print(f"      Latest OHLCV: {ohlcv_time} - TF: {latest_ohlcv[2]}min, Close: {latest_ohlcv[6]:.2f}")
    
    # Test manual aggregation
    print("\n5️⃣ Testing manual OHLCV aggregation...")
    try:
        # Force update for 1-minute timeframe
        collector._update_ohlcv_from_ticks(1)
        
        # Check if it worked
        cursor.execute("SELECT COUNT(*) FROM ohlcv_data")
        final_ohlcv_count = cursor.fetchone()[0]
        manual_ohlcv = final_ohlcv_count - ohlcv_count_before
        
        print(f"   Manual aggregation result: +{manual_ohlcv} OHLCV records")
        
        if manual_ohlcv > 0:
            cursor.execute("SELECT * FROM ohlcv_data ORDER BY timestamp DESC LIMIT 1")
            latest_manual = cursor.fetchone()
            if latest_manual:
                manual_time = datetime.fromtimestamp(latest_manual[1])
                print(f"   Manual OHLCV: {manual_time} - TF: {latest_manual[2]}min, Close: {latest_manual[6]:.2f}")
        
    except Exception as e:
        print(f"   ❌ Manual aggregation failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Stop collection
    print("\n6️⃣ Stopping real-time collection...")
    collector.stop_real_time_collection()
    
    # Final assessment
    print("\n📊 DEBUGGING RESULTS:")
    print("=" * 30)
    
    cursor.execute("SELECT COUNT(*) FROM tick_data")
    final_tick_count = cursor.fetchone()[0]
    total_new_ticks = final_tick_count - tick_count_before
    
    cursor.execute("SELECT COUNT(*) FROM ohlcv_data")
    final_ohlcv_count = cursor.fetchone()[0]
    total_new_ohlcv = final_ohlcv_count - ohlcv_count_before
    
    print(f"Total new ticks collected: {total_new_ticks}")
    print(f"Total new OHLCV records: {total_new_ohlcv}")
    
    if total_new_ticks > 0:
        print("✅ Tick collection is working")
    else:
        print("❌ Tick collection is NOT working")
    
    if total_new_ohlcv > 0:
        print("✅ OHLCV aggregation is working")
    else:
        print("❌ OHLCV aggregation is NOT working")
    
    # Check for errors in logs
    print("\n7️⃣ Checking for recent errors...")
    try:
        # Look for recent log files
        import os
        log_files = [f for f in os.listdir('logs') if f.endswith('.log')]
        if log_files:
            latest_log = max(log_files, key=lambda x: os.path.getmtime(os.path.join('logs', x)))
            print(f"   Latest log file: {latest_log}")
            
            # Check for errors
            with open(os.path.join('logs', latest_log), 'r') as f:
                lines = f.readlines()
                error_lines = [line for line in lines[-50:] if 'ERROR' in line]
                if error_lines:
                    print("   Recent errors found:")
                    for error in error_lines[-5:]:  # Show last 5 errors
                        print(f"      {error.strip()}")
                else:
                    print("   No recent errors found in logs")
        else:
            print("   No log files found")
    except Exception as e:
        print(f"   Error checking logs: {e}")
    
    return total_new_ticks > 0 and total_new_ohlcv > 0

if __name__ == "__main__":
    try:
        success = debug_real_time_collection()
        
        if success:
            print("\n🎉 DEBUGGING RESULT: Real-time data collection is working!")
        else:
            print("\n💥 DEBUGGING RESULT: Issues found with real-time data collection")
            
    except Exception as e:
        print(f"\n💥 Debug failed with error: {e}")
        import traceback
        traceback.print_exc()
