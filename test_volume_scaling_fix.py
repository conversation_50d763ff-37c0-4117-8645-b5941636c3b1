#!/usr/bin/env python3
"""
Test the volume scaling fix to ensure it never goes below minimum volume.
"""

import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_volume_scaling_logic():
    """Test the volume scaling logic with different volume scenarios."""
    try:
        # Mock the necessary components
        class MockDataCollector:
            pass
        
        from order_execution_system import OrderExecutionSystem
        from trading_signal_generator import TradingSignal, SignalType
        
        # Create order execution system
        mock_data_collector = MockDataCollector()
        execution_system = OrderExecutionSystem(mock_data_collector)
        
        logger.info("=== Volume Scaling Logic Test ===")
        
        # Test scenarios with different position sizes
        test_scenarios = [
            {"position_size": 0.001, "expected_scaling": False, "description": "Very small position (0.1%)"},
            {"position_size": 0.005, "expected_scaling": False, "description": "Small position (0.5%)"},
            {"position_size": 0.01, "expected_scaling": False, "description": "Minimum position (1%)"},
            {"position_size": 0.02, "expected_scaling": True, "description": "Scalable position (2%)"},
            {"position_size": 0.05, "expected_scaling": True, "description": "Large position (5%)"},
        ]
        
        for scenario in test_scenarios:
            logger.info(f"\nTesting: {scenario['description']}")
            
            # Create test signal
            test_signal = TradingSignal(
                signal_type=SignalType.STRONG_SELL,
                confidence=0.8,  # High confidence to trigger scaling check
                entry_price=65000.0,
                stop_loss=65350.0,
                take_profit=64650.0,
                position_size=scenario["position_size"],
                risk_reward_ratio=2.0,
                timeframe=5,
                timestamp=datetime.now(),
                ai_predictions={},
                market_regime='NORMAL',
                reasoning='Test signal for volume scaling'
            )
            
            # Test if scaling should be used
            should_scale = execution_system._should_use_position_scaling(test_signal)
            logger.info(f"  Position size: {scenario['position_size']} ({scenario['position_size']*100:.1f}%)")
            logger.info(f"  Should use scaling: {should_scale}")
            logger.info(f"  Expected scaling: {scenario['expected_scaling']}")
            
            # Verify expectation
            if should_scale == scenario['expected_scaling']:
                logger.info(f"  ✅ CORRECT: Scaling decision matches expectation")
            else:
                logger.error(f"  ❌ INCORRECT: Expected {scenario['expected_scaling']}, got {should_scale}")
                return False
            
            # If scaling should be used, test the parameters
            if should_scale:
                full_volume = execution_system._calculate_volume(test_signal)
                scaling_params = execution_system._calculate_scaling_parameters(test_signal, full_volume)
                
                if scaling_params:
                    initial_volume = scaling_params['initial_volume']
                    logger.info(f"  Full volume: {full_volume:.3f}")
                    logger.info(f"  Initial volume: {initial_volume:.3f}")
                    
                    # Check if initial volume meets minimum requirements
                    if initial_volume >= 0.01:  # Minimum volume for DEX 900 DOWN Index
                        logger.info(f"  ✅ Initial volume meets minimum requirement (≥0.01)")
                    else:
                        logger.error(f"  ❌ Initial volume {initial_volume:.3f} below minimum 0.01")
                        return False
                else:
                    logger.info(f"  ✅ Scaling parameters correctly returned None (volume too small)")
        
        logger.info("\n✅ All volume scaling tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"Error testing volume scaling logic: {e}")
        return False

def test_minimum_volume_enforcement():
    """Test that the system never tries to trade below minimum volume."""
    try:
        import MetaTrader5 as mt5
        
        # Initialize MT5 to get actual symbol info
        if not mt5.initialize():
            logger.error("MT5 initialization failed")
            return False
        
        symbol = "DEX 900 DOWN Index"
        symbol_info = mt5.symbol_info(symbol)
        
        if not symbol_info:
            logger.error("Could not get symbol info")
            return False
        
        logger.info("=== Minimum Volume Enforcement Test ===")
        logger.info(f"Symbol: {symbol}")
        logger.info(f"Minimum volume: {symbol_info.volume_min}")
        logger.info(f"Volume step: {symbol_info.volume_step}")
        
        # Test edge cases
        test_volumes = [0.005, 0.01, 0.015, 0.02, 0.025]
        
        for test_vol in test_volumes:
            logger.info(f"\nTesting volume: {test_vol}")
            
            # Simulate scaling calculation
            initial_volume = max(test_vol * 0.5, symbol_info.volume_min)
            remaining_volume = test_vol - initial_volume
            
            logger.info(f"  50% of {test_vol}: {test_vol * 0.5:.3f}")
            logger.info(f"  Adjusted initial: {initial_volume:.3f}")
            logger.info(f"  Remaining: {remaining_volume:.3f}")
            
            # Check compliance
            if initial_volume >= symbol_info.volume_min:
                logger.info(f"  ✅ Compliant with minimum volume")
            else:
                logger.error(f"  ❌ Below minimum volume!")
                return False
            
            # Check if scaling makes sense
            if remaining_volume <= 0:
                logger.info(f"  ⚠️ No remaining volume for scaling (would disable scaling)")
            else:
                logger.info(f"  ✅ Has remaining volume for scaling")
        
        mt5.shutdown()
        logger.info("\n✅ Minimum volume enforcement test passed!")
        return True
        
    except Exception as e:
        logger.error(f"Error testing minimum volume enforcement: {e}")
        return False

def main():
    """Run all volume scaling fix tests."""
    logger.info("🔧 VOLUME SCALING FIX TESTING")
    logger.info("=" * 50)
    
    tests = [
        ("Volume Scaling Logic", test_volume_scaling_logic),
        ("Minimum Volume Enforcement", test_minimum_volume_enforcement),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"Result: {status}")
        except Exception as e:
            logger.error(f"Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("🎉 Volume scaling fix verified!")
        logger.info("The system will no longer try to trade volumes below 0.01")
    else:
        logger.warning("⚠️ Volume scaling fix needs more work")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
