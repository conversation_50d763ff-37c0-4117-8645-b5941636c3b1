#!/usr/bin/env python3
"""
Test script to diagnose dashboard connection issues.
"""

import requests
import time
import subprocess
import sys
import os
import socket
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_port_availability(port=5000):
    """Check if port is available or in use."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', port))
            if result == 0:
                logger.info(f"✅ Port {port} is in use (something is listening)")
                return True
            else:
                logger.info(f"❌ Port {port} is not in use")
                return False
    except Exception as e:
        logger.error(f"Error checking port {port}: {e}")
        return False

def test_dashboard_connection(max_attempts=5):
    """Test dashboard connection with multiple attempts."""
    logger.info("🔄 Testing dashboard connection...")
    
    for attempt in range(1, max_attempts + 1):
        try:
            logger.info(f"Attempt {attempt}/{max_attempts}")
            response = requests.get('http://localhost:5000', timeout=10)
            
            if response.status_code == 200:
                logger.info(f"✅ Dashboard connection successful! Status: {response.status_code}")
                logger.info(f"Response length: {len(response.text)} characters")
                return True
            else:
                logger.warning(f"⚠️ Dashboard responded with status: {response.status_code}")
                
        except requests.exceptions.ConnectionError as e:
            logger.error(f"❌ Connection error (attempt {attempt}): {e}")
        except requests.exceptions.Timeout as e:
            logger.error(f"❌ Timeout error (attempt {attempt}): {e}")
        except Exception as e:
            logger.error(f"❌ Unexpected error (attempt {attempt}): {e}")
        
        if attempt < max_attempts:
            logger.info("Waiting 2 seconds before next attempt...")
            time.sleep(2)
    
    logger.error("❌ All dashboard connection attempts failed")
    return False

def check_dashboard_process():
    """Check if dashboard server process is running."""
    try:
        # Check for Python processes running dashboard_server.py
        result = subprocess.run([
            'tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'
        ], capture_output=True, text=True, shell=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            python_processes = len(lines) - 1  # Subtract header
            logger.info(f"Found {python_processes} Python processes running")
            
            # Try to find dashboard-specific process
            dashboard_processes = 0
            for line in lines[1:]:  # Skip header
                if 'dashboard' in line.lower():
                    dashboard_processes += 1
            
            if dashboard_processes > 0:
                logger.info(f"✅ Found {dashboard_processes} dashboard-related processes")
                return True
            else:
                logger.warning("⚠️ No dashboard-specific processes found")
                return False
        else:
            logger.error("Failed to check running processes")
            return False
            
    except Exception as e:
        logger.error(f"Error checking dashboard process: {e}")
        return False

def start_dashboard_manually():
    """Try to start dashboard server manually."""
    try:
        logger.info("🚀 Attempting to start dashboard server manually...")
        
        # Check if dashboard_server.py exists
        if not os.path.exists('dashboard_server.py'):
            logger.error("❌ dashboard_server.py not found in current directory")
            return False
        
        # Start dashboard server
        process = subprocess.Popen([
            sys.executable, 'dashboard_server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        logger.info(f"Started dashboard process with PID: {process.pid}")
        
        # Give it time to start
        logger.info("Waiting 15 seconds for dashboard to start...")
        time.sleep(15)
        
        # Check if process is still running
        if process.poll() is None:
            logger.info("✅ Dashboard process is still running")
            return True
        else:
            logger.error("❌ Dashboard process terminated")
            stdout, stderr = process.communicate()
            if stdout:
                logger.error(f"STDOUT: {stdout.decode()}")
            if stderr:
                logger.error(f"STDERR: {stderr.decode()}")
            return False
            
    except Exception as e:
        logger.error(f"Error starting dashboard manually: {e}")
        return False

def diagnose_dashboard_issues():
    """Comprehensive dashboard diagnosis."""
    logger.info("=" * 60)
    logger.info("DASHBOARD CONNECTION DIAGNOSIS")
    logger.info("=" * 60)
    
    # Step 1: Check port availability
    logger.info("\n1. Checking port availability...")
    port_in_use = check_port_availability(5000)
    
    # Step 2: Test connection
    logger.info("\n2. Testing dashboard connection...")
    connection_works = test_dashboard_connection()
    
    # Step 3: Check processes
    logger.info("\n3. Checking dashboard processes...")
    process_running = check_dashboard_process()
    
    # Step 4: Analysis
    logger.info("\n" + "=" * 40)
    logger.info("DIAGNOSIS RESULTS")
    logger.info("=" * 40)
    
    if port_in_use and connection_works:
        logger.info("✅ Dashboard is running and accessible")
        return True
    elif port_in_use and not connection_works:
        logger.warning("⚠️ Port is in use but connection fails - possible issue with dashboard server")
        logger.info("Recommendation: Restart the dashboard server")
        return False
    elif not port_in_use and not process_running:
        logger.error("❌ Dashboard server is not running")
        logger.info("Recommendation: Start the dashboard server")
        
        # Try to start it manually
        logger.info("\n4. Attempting manual dashboard startup...")
        if start_dashboard_manually():
            logger.info("\n5. Re-testing connection after manual start...")
            return test_dashboard_connection()
        else:
            return False
    else:
        logger.error("❌ Unclear dashboard state")
        logger.info("Recommendation: Check logs and restart system")
        return False

def main():
    """Main diagnosis function."""
    success = diagnose_dashboard_issues()
    
    if success:
        logger.info("\n🎉 Dashboard diagnosis completed successfully!")
        logger.info("Dashboard should be accessible at: http://localhost:5000")
    else:
        logger.error("\n💥 Dashboard diagnosis found issues!")
        logger.error("Please check the recommendations above")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
