#!/usr/bin/env python3
"""
Test script to verify that the AI Trading System can close positions on MT5.
This will test the complete trade lifecycle: open → monitor → close.
"""

import sys
import logging
import time
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_position_closing():
    """Test that the system can open and close positions on MT5."""
    try:
        print("🧪 TESTING POSITION CLOSING CAPABILITY")
        print("=" * 60)
        
        # Import required modules
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        from trading_signal_generator import TradingSignalGenerator, TradingSignal, SignalType
        from order_execution_system import OrderExecutionSystem
        
        print("📊 Initializing trading components...")
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Components initialized")
        
        # Check MT5 connection
        print("🔌 Testing MT5 connection...")
        if not order_executor.mt5_connected:
            print("❌ MT5 not connected! Please ensure MT5 is running and logged in.")
            return False
            
        print("✅ MT5 connected successfully")
        
        # Get current price
        print("💰 Getting current market price...")
        current_price = order_executor._get_current_price()
        if current_price is None:
            print("❌ Could not get current price!")
            return False
            
        print(f"✅ Current price: {current_price:.2f}")
        
        # Check account info
        print("👤 Checking account information...")
        import MetaTrader5 as mt5
        account_info = mt5.account_info()
        if not account_info:
            print("❌ Could not get account information!")
            return False
            
        print(f"✅ Account Balance: ${account_info.balance:.2f}")
        print(f"✅ Account Equity: ${account_info.equity:.2f}")
        print(f"✅ Free Margin: ${account_info.margin_free:.2f}")
        
        # Create a test trading signal
        print("🎯 Creating test trading signal...")
        
        # Create a minimal test signal (BUY signal) without SL/TP for testing
        test_signal = TradingSignal(
            signal_type=SignalType.WEAK_BUY,
            confidence=0.6,
            entry_price=current_price,
            stop_loss=0.0,  # No stop loss for test (0.0 means disabled)
            take_profit=0.0,  # No take profit for test (0.0 means disabled)
            position_size=0.01,  # Minimum position size for testing
            risk_reward_ratio=2.0,
            timeframe=15,  # Integer timeframe
            timestamp=datetime.now(),
            ai_predictions={"test": 0.6},
            market_regime="TEST",
            reasoning="Test signal for position closing verification"
        )
        
        print(f"✅ Test signal created:")
        print(f"   Type: {test_signal.signal_type.name}")
        print(f"   Entry: {test_signal.entry_price:.2f}")
        print(f"   Stop Loss: {test_signal.stop_loss:.2f}")
        print(f"   Take Profit: {test_signal.take_profit:.2f}")
        
        # Test trade execution capability
        print("\n🚀 STEP 1: OPENING POSITION...")
        print("🔄 Attempting to execute test signal...")
        
        # Execute the signal
        order = order_executor.execute_signal(test_signal)
        
        if order is None:
            print("❌ Trade execution failed - no order returned!")
            return False
            
        if order.status.name != "FILLED":
            print(f"❌ Trade execution failed - order status: {order.status.name}")
            return False
            
        print("🎉 POSITION OPENED SUCCESSFULLY!")
        print(f"✅ Order ID: {order.order_id}")
        print(f"✅ Fill Price: {order.fill_price:.2f}")
        print(f"✅ Volume: {order.volume}")
        print(f"✅ Status: {order.status.name}")
        
        # Wait a moment for position to be registered
        print("\n⏳ Waiting 3 seconds for position registration...")
        time.sleep(3)
        
        # Check if position is in active positions
        print("🔍 Checking active positions...")
        if order.order_id not in order_executor.active_positions:
            print("⚠️  Position not found in active positions, checking MT5 directly...")
            # Try to find the position in MT5
            positions = mt5.positions_get(symbol="DEX 900 DOWN Index")
            if positions:
                print(f"✅ Found {len(positions)} position(s) in MT5")
                # Use the first position for testing
                position_id = positions[0].ticket
                print(f"✅ Using position ID: {position_id}")
            else:
                print("❌ No positions found in MT5!")
                return False
        else:
            position_id = order.order_id
            print(f"✅ Position found in active positions: {position_id}")
        
        # Test position closing capability
        print(f"\n🚀 STEP 2: CLOSING POSITION {position_id}...")
        print("🔄 Attempting to close position...")
        
        # Close the position
        close_success = order_executor.close_position(position_id, "Test_Complete")
        
        if close_success:
            print("🎉 POSITION CLOSED SUCCESSFULLY!")
            print(f"✅ Position {position_id} closed successfully")
            
            # Wait a moment and verify closure
            print("⏳ Waiting 2 seconds to verify closure...")
            time.sleep(2)
            
            # Check if position is still active
            positions_after = mt5.positions_get(symbol="DEX 900 DOWN Index")
            if positions_after:
                remaining_positions = [p.ticket for p in positions_after]
                if position_id in remaining_positions:
                    print("⚠️  Warning: Position still appears in MT5 positions")
                else:
                    print("✅ Position successfully removed from MT5")
            else:
                print("✅ No positions remaining in MT5 - closure confirmed")
                
            return True
            
        else:
            print("❌ POSITION CLOSING FAILED!")
            print(f"   Could not close position {position_id}")
            print("   Please manually close the position in MT5")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 AI TRADING SYSTEM - POSITION CLOSING TEST")
    print("=" * 60)
    print("⚠️  IMPORTANT: Make sure you're using a DEMO account!")
    print("⚠️  This test will open and close a real position.")
    print("=" * 60)
    
    success = test_position_closing()
    
    if success:
        print("\n🎉 POSITION CLOSING TEST PASSED!")
        print("✅ The AI Trading System CAN open AND close positions on MT5!")
        print("✅ Complete trade lifecycle verified: OPEN → MONITOR → CLOSE")
    else:
        print("\n❌ POSITION CLOSING TEST FAILED!")
        print("🔧 Please check the error messages above.")
        
    sys.exit(0 if success else 1)
