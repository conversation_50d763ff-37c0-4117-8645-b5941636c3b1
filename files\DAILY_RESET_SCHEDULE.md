# ⏰ AI TRADING SYSTEM - DAILY RESET SCHEDULE & LIMITS

## **🕛 AUTOMATIC RESET TIME:**

### **⏰ MIDNIGHT RESET (00:00:00 Local Time)**
The AI system automatically resets ALL daily counters and limits at **midnight** (00:00:00) local time.

**📅 Reset Logic:**
```python
current_date = datetime.now().date()
if current_date != self.last_reset_date:
    # Reset all daily counters
    self.daily_trade_count = 0
    self.daily_pnl = 0.0
    self.last_reset_date = current_date
```

## **📊 DAILY LIMITS & THRESHOLDS:**

### **🔢 TRADE LIMITS:**
- **Maximum Daily Trades**: **10 trades per day**
- **Reset Time**: Midnight (00:00:00)
- **Blocking**: System stops generating signals after 10 trades

### **💰 FINANCIAL LIMITS:**
- **Maximum Daily Drawdown**: **$50.00**
- **Reset Time**: Midnight (00:00:00)
- **Emergency Stop**: System stops trading if daily loss exceeds $50

### **📈 POSITION LIMITS:**
- **Maximum Concurrent Positions**: **3 positions**
- **Current User Setting**: **1 position at a time** (more restrictive)
- **Reset**: Not daily - resets when positions close

## **🔄 WHAT RESETS AT MIDNIGHT:**

### **✅ AUTOMATICALLY RESET:**
1. **Daily Trade Count**: 0 → Ready for 10 new trades
2. **Daily P&L**: $0.00 → Fresh daily loss tracking
3. **Emergency Stop Flags**: Cleared → System can trade again
4. **Timeframe Position Tracking**: Cleared → All timeframes available

### **❌ NOT RESET (Persistent):**
- **Active Positions**: Remain open across days
- **Account Balance**: Actual MT5 account balance
- **Model Performance**: Historical model statistics
- **System Configuration**: All settings remain

## **⚠️ EMERGENCY STOP CONDITIONS:**

### **🚨 DAILY DRAWDOWN LIMIT:**
- **Trigger**: Daily P&L < -$50.00
- **Action**: Immediate trading halt
- **Reset**: Next day at midnight
- **Override**: Manual reset using `reset_daily_limits.py`

### **🔢 TRADE LIMIT REACHED:**
- **Trigger**: 10 trades executed in one day
- **Action**: No new signals generated
- **Reset**: Next day at midnight
- **Override**: Manual reset using `reset_daily_limits.py`

## **🛠️ MANUAL RESET OPTIONS:**

### **📝 Manual Reset Script:**
**File**: `reset_daily_limits.py`
**Purpose**: Reset limits before midnight if needed

**When to Use:**
- Emergency stop triggered incorrectly
- Want to start fresh mid-day
- System limits reached but want to continue

**What it Resets:**
- Daily trade count → 0
- Daily P&L → $0.00
- Emergency stop flags → Cleared
- Timeframe positions → Cleared

## **🕐 RESET TIMING EXAMPLES:**

### **📅 NORMAL DAY:**
- **11:59:59 PM**: System has 8 trades, -$25.50 P&L
- **12:00:00 AM**: **AUTOMATIC RESET**
- **12:00:01 AM**: System has 0 trades, $0.00 P&L, ready to trade

### **🚨 EMERGENCY STOP:**
- **2:30 PM**: Daily loss reaches -$50.01
- **2:30 PM**: **EMERGENCY STOP TRIGGERED**
- **2:30 PM - 11:59 PM**: No trading allowed
- **12:00:00 AM**: **AUTOMATIC RESET** - Trading resumes

### **🔢 TRADE LIMIT:**
- **4:45 PM**: 10th trade executed
- **4:45 PM**: **TRADE LIMIT REACHED**
- **4:45 PM - 11:59 PM**: No new signals generated
- **12:00:00 AM**: **AUTOMATIC RESET** - Can trade again

## **📊 CURRENT CONFIGURATION:**

### **💼 RISK MANAGEMENT:**
```python
MAX_DAILY_TRADES = 10                    # 10 trades per day
MAX_DAILY_DRAWDOWN = 50.0               # $50 daily loss limit
MAX_CONCURRENT_POSITIONS = 3            # 3 positions max
BASE_RISK_PER_TRADE = 0.005            # 0.5% risk per trade
```

### **⏰ RESET SCHEDULE:**
- **Daily Reset**: 00:00:00 local time
- **Automatic**: Yes, no manual intervention needed
- **Timezone**: System local timezone
- **Frequency**: Every 24 hours

## **🎯 MONITORING RESET STATUS:**

### **📊 Dashboard Display:**
- **Daily Trade Count**: Shows current/max (e.g., "3/10")
- **Daily P&L**: Shows current daily profit/loss
- **Time to Reset**: Shows hours until midnight reset

### **📝 Log Messages:**
```
Daily trading counters reset
Daily trade count: 0
Daily P&L: 0.0
Reset date: 2025-01-15
```

## **🚀 SYSTEM STATUS AFTER RESET:**

### **✅ READY TO TRADE:**
- ✅ Daily trade count: 0/10
- ✅ Daily P&L: $0.00
- ✅ Emergency stops: Cleared
- ✅ All timeframes: Available
- ✅ Signal generation: Active

**The AI system automatically resets at midnight every day, giving you a fresh start with full trading capacity!** 🎯
