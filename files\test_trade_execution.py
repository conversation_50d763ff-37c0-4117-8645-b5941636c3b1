#!/usr/bin/env python3
"""
Test script to verify that the AI Trading System can actually place trades on MT5.
This will test the complete trade execution pipeline.
"""

import sys
import os
import logging
import time
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_trade_execution():
    """Test that the system can actually place trades on MT5."""
    try:
        print("🧪 TESTING TRADE EXECUTION CAPABILITY")
        print("=" * 60)
        
        # Import required modules
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        from trading_signal_generator import TradingSignalGenerator
        from order_execution_system import OrderExecutionSystem
        from trading_signal_generator import TradingSignal, SignalType
        
        print("📊 Initializing trading components...")
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Components initialized")
        
        # Check MT5 connection
        print("🔌 Testing MT5 connection...")
        if not order_executor.mt5_connected:
            print("❌ MT5 not connected! Please ensure MT5 is running and logged in.")
            return False
            
        print("✅ MT5 connected successfully")
        
        # Get current price
        print("💰 Getting current market price...")
        current_price = order_executor._get_current_price()
        if current_price is None:
            print("❌ Could not get current price!")
            return False
            
        print(f"✅ Current price: {current_price:.2f}")
        
        # Check account info
        print("👤 Checking account information...")
        import MetaTrader5 as mt5
        account_info = mt5.account_info()
        if not account_info:
            print("❌ Could not get account information!")
            return False

        print(f"✅ Account Balance: ${account_info.balance:.2f}")
        print(f"✅ Account Equity: ${account_info.equity:.2f}")
        print(f"✅ Free Margin: ${account_info.margin_free:.2f}")
        
        # Create a test trading signal
        print("🎯 Creating test trading signal...")
        
        # Create a minimal test signal (BUY signal) without SL/TP for testing
        test_signal = TradingSignal(
            signal_type=SignalType.WEAK_BUY,
            confidence=0.6,
            entry_price=current_price,
            stop_loss=0.0,  # No stop loss for test (0.0 means disabled)
            take_profit=0.0,  # No take profit for test (0.0 means disabled)
            position_size=0.01,  # Minimum position size for testing
            risk_reward_ratio=2.0,
            timeframe=15,  # Integer timeframe
            timestamp=datetime.now(),
            ai_predictions={"test": 0.6},
            market_regime="TEST",
            reasoning="Test signal for trade execution verification - no SL/TP"
        )
        
        print(f"✅ Test signal created:")
        print(f"   Type: {test_signal.signal_type.name}")
        print(f"   Entry: {test_signal.entry_price:.2f}")
        print(f"   Stop Loss: {test_signal.stop_loss:.2f}")
        print(f"   Take Profit: {test_signal.take_profit:.2f}")
        
        # Test trade execution capability
        print("\n🚀 TESTING TRADE EXECUTION...")
        print("⚠️  This will place a REAL trade to test execution capability!")
        print("⚠️  Using minimum position size for safety...")
        print("🔄 Attempting to execute test signal...")
        
        # Execute the signal
        order = order_executor.execute_signal(test_signal)
        
        if order is None:
            print("❌ Trade execution failed - no order returned!")
            return False
            
        if order.status.name == "FILLED":
            print("🎉 TRADE EXECUTION SUCCESSFUL!")
            print(f"✅ Order ID: {order.order_id}")
            print(f"✅ Fill Price: {order.fill_price:.2f}")
            print(f"✅ Volume: {order.volume}")
            print(f"✅ Status: {order.status.name}")
            
            # Wait a moment then close the position for testing
            print("\n⏳ Waiting 10 seconds before closing test position...")
            time.sleep(10)
            
            print("🔄 Closing test position...")
            if order.order_id is not None:
                close_success = order_executor.close_position(order.order_id, "Test_Complete")
            else:
                close_success = False
            
            if close_success:
                print("✅ Test position closed successfully!")
            else:
                print("⚠️  Warning: Could not close test position automatically.")
                print(f"   Please manually close position {order.order_id} in MT5")
                
            return True
            
        elif order.status.name == "REJECTED":
            print("❌ Trade execution failed - order rejected!")
            print("   This could be due to:")
            print("   - Insufficient margin")
            print("   - Market closed")
            print("   - Invalid parameters")
            print("   - MT5 connection issues")
            return False
            
        else:
            print(f"⚠️  Unexpected order status: {order.status.name}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 AI TRADING SYSTEM - TRADE EXECUTION TEST")
    print("=" * 60)
    print("⚠️  IMPORTANT: Make sure you're using a DEMO account!")
    print("⚠️  This test will place a real trade to verify execution capability.")
    print("=" * 60)
    
    success = test_trade_execution()
    
    if success:
        print("\n🎉 TRADE EXECUTION TEST PASSED!")
        print("✅ The AI Trading System CAN place trades on MT5!")
    else:
        print("\n❌ TRADE EXECUTION TEST FAILED!")
        print("🔧 Please check MT5 connection and account settings.")
        
    sys.exit(0 if success else 1)
