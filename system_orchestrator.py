#!/usr/bin/env python3
"""
AI Trading System Orchestrator
Complete startup sequence management with fail-fast and retry logic.
"""

import os
import sys
import time
import logging
import subprocess
import threading
from datetime import datetime, timedelta
from pathlib import Path
import json

# Set up logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/system_startup.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('SystemOrchestrator')

class AITradingSystemOrchestrator:
    """Orchestrates the complete AI Trading System startup sequence."""
    
    def __init__(self):
        """Initialize the system orchestrator."""
        self.start_time = datetime.now()
        self.phase_results = {}
        self.retry_attempts = 3
        self.retry_delay = 10  # seconds
        
        # System components
        self.components = {
            'data_collector': None,
            'pattern_detector': None,
            'ai_manager': None,
            'trading_engine': None,
            'dashboard_server': None
        }
        
        # Phase status tracking
        self.phases = {
            'environment_validation': False,
            'data_collection': False,
            'model_preparation': False,
            'cache_management': False,
            'system_activation': False,
            'dashboard_launch': False
        }
        
    def start_complete_system(self):
        """Execute the complete startup sequence."""
        try:
            logger.info("STARTING AI TRADING SYSTEM COMPLETE STARTUP SEQUENCE")
            logger.info("=" * 80)
            logger.info("SEQUENCE: Environment -> Data -> Models -> Dashboard -> System -> Cache")
            logger.info("FAIL-FAST MODE: Any failure stops entire startup")
            logger.info("RETRY LOGIC: 3 attempts per component")
            logger.info("=" * 80)

            # Phase 1: Environment & MT5 Validation
            self.execute_phase_with_retry("environment_validation", self.phase1_environment_validation)

            # Phase 2: Smart Data Collection
            self.execute_phase_with_retry("data_collection", self.phase2_data_collection)

            # Phase 3: Model Preparation
            self.execute_phase_with_retry("model_preparation", self.phase3_model_preparation)

            # Phase 4: Dashboard Launch (MOVED EARLIER - before trading starts)
            self.execute_phase_with_retry("dashboard_launch", self.phase4_dashboard_launch)

            # Phase 5: Trading System Activation (after dashboard is ready)
            self.execute_phase_with_retry("system_activation", self.phase5_system_activation)

            # Phase 6: Cache Management System (moved to last)
            self.execute_phase_with_retry("cache_management", self.phase6_cache_management)
            
            # System Ready
            self.system_ready()
            
        except Exception as e:
            logger.error(f"CRITICAL STARTUP FAILURE: {e}")
            self.cleanup_failed_startup()
            return False
            
    def execute_phase_with_retry(self, phase_name, phase_function):
        """Execute a phase with retry logic."""
        for attempt in range(1, self.retry_attempts + 1):
            try:
                logger.info(f"PHASE: {phase_name.upper()} - Attempt {attempt}/{self.retry_attempts}")

                success = phase_function()

                if success:
                    self.phases[phase_name] = True
                    logger.info(f"SUCCESS: PHASE {phase_name.upper()} COMPLETED SUCCESSFULLY")
                    return True
                else:
                    raise Exception(f"Phase {phase_name} returned False")

            except Exception as e:
                logger.error(f"ERROR: PHASE {phase_name.upper()} FAILED (Attempt {attempt}): {e}")

                if attempt < self.retry_attempts:
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"CRITICAL: PHASE {phase_name.upper()} FAILED AFTER {self.retry_attempts} ATTEMPTS")
                    raise Exception(f"Phase {phase_name} failed after {self.retry_attempts} attempts: {e}")
    
    def phase1_environment_validation(self):
        """Phase 1: Environment & MT5 Validation."""
        logger.info("Phase 1: Environment & MT5 Validation")
        
        # Check virtual environment
        if not self.validate_virtual_environment():
            raise Exception("Virtual environment validation failed")
            
        # Check Python dependencies
        if not self.validate_dependencies():
            raise Exception("Dependencies validation failed")
            
        # Check MT5 availability
        if not self.validate_mt5_connection():
            raise Exception("MT5 connection validation failed")
            
        # Check required directories
        if not self.validate_directories():
            raise Exception("Directory structure validation failed")
            
        # Check database connectivity
        if not self.validate_database():
            raise Exception("Database validation failed")
            
        logger.info("SUCCESS: Environment validation completed successfully")
        return True
        
    def phase2_data_collection(self):
        """Phase 2: Smart Data Collection with fallback."""
        logger.info("Phase 2: Smart Data Collection")
        
        try:
            # Try smart incremental collection first
            logger.info("Attempting smart incremental data collection...")
            if self.smart_data_collection():
                logger.info("SUCCESS: Smart data collection successful")
                return True
        except Exception as e:
            logger.warning(f"WARNING: Smart collection failed: {e}")

        # Fallback to full historical collection
        logger.info("Falling back to full historical data collection...")
        if self.full_data_collection():
            logger.info("SUCCESS: Full data collection successful")
            return True
        else:
            raise Exception("Both smart and full data collection failed")
            
    def phase3_model_preparation(self):
        """Phase 3: Model Loading/Training."""
        logger.info("Phase 3: Model Preparation")

        # Check model age and decide on training
        if self.check_model_training_needed():
            logger.info("Model training required (>7 days old)")
            if not self.train_all_models():
                raise Exception("Model training failed")
        else:
            logger.info("SUCCESS: Models are current (<7 days old)")

        # Load and validate all models
        if not self.load_and_validate_models():
            raise Exception("Model loading/validation failed")

        logger.info("SUCCESS: Model preparation completed successfully")
        return True
        
    def phase6_cache_management(self):
        """Phase 6: Cache Management System Startup."""
        logger.info("Phase 6: Cache Management System Startup")

        # Start cache management system
        if not self.start_cache_management_system():
            raise Exception("Cache management system startup failed")

        logger.info("SUCCESS: Cache management system startup completed successfully")
        return True

    def phase5_system_activation(self):
        """Phase 5: Trading System Activation."""
        logger.info("Phase 5: Trading System Activation")
        
        # Initialize core trading components
        if not self.initialize_trading_components():
            raise Exception("Trading components initialization failed")
            
        # Start real-time monitoring
        if not self.start_real_time_monitoring():
            raise Exception("Real-time monitoring startup failed")
            
        # Validate system integration
        if not self.validate_system_integration():
            raise Exception("System integration validation failed")
            
        logger.info("SUCCESS: Trading system activation completed successfully")
        return True
        
    def phase4_dashboard_launch(self):
        """Phase 4: Dashboard Launch (Early - for monitoring)."""
        logger.info("Phase 4: Dashboard Launch (Early - for monitoring)")

        # Quick check if dashboard is already running
        try:
            import requests
            response = requests.get('http://localhost:5000', timeout=2)
            if response.status_code == 200:
                logger.info("✅ Dashboard is already running and accessible!")
                try:
                    self.open_dashboard_browser()
                    logger.info("Browser opened for existing dashboard")
                except Exception as e:
                    logger.warning(f"Could not open browser: {e}")
                return True
        except:
            pass  # Dashboard not running, continue with startup

        # Start dashboard server (non-blocking)
        logger.info("Starting dashboard server in background...")
        try:
            import subprocess
            import sys

            # Start dashboard in background
            dashboard_process = subprocess.Popen(
                [sys.executable, 'dashboard_server.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            logger.info(f"✅ Dashboard server started with PID: {dashboard_process.pid}")
            logger.info("Dashboard will initialize in background (2-3 minutes)")
            logger.info("Dashboard will be available at: http://localhost:5000")

            # Store process reference
            self.components['dashboard'] = dashboard_process

        except Exception as e:
            logger.warning(f"Could not start dashboard server: {e}")
            logger.info("System will continue without dashboard")

        logger.info("SUCCESS: Dashboard launch initiated (non-blocking)")
        return True
        
    def validate_virtual_environment(self):
        """Validate virtual environment is active."""
        try:
            venv_path = os.environ.get('VIRTUAL_ENV')
            if not venv_path:
                logger.error("Virtual environment not activated")
                return False
                
            logger.info(f"SUCCESS: Virtual environment active: {venv_path}")
            return True
        except Exception as e:
            logger.error(f"Virtual environment check failed: {e}")
            return False

    def validate_dependencies(self):
        """Validate all required Python packages."""
        try:
            required_packages = [
                'pandas', 'numpy', 'sklearn', 'tensorflow',
                'MetaTrader5', 'flask', 'xgboost'
            ]

            for package in required_packages:
                try:
                    __import__(package)
                    logger.debug(f"SUCCESS: Package {package} available")
                except ImportError:
                    logger.error(f"ERROR: Package {package} missing")
                    return False

            logger.info("SUCCESS: All dependencies validated")
            return True
        except Exception as e:
            logger.error(f"Dependency validation failed: {e}")
            return False
            
    def validate_mt5_connection(self):
        """Validate MT5 connection and DEX 900 DOWN Index availability."""
        try:
            import MetaTrader5 as mt5
            
            if not mt5.initialize():
                logger.error("MT5 initialization failed")
                return False
                
            # Check DEX 900 DOWN Index
            symbol_info = mt5.symbol_info("DEX 900 DOWN Index")
            if symbol_info is None:
                logger.error("DEX 900 DOWN Index not available")
                return False
                
            logger.info("SUCCESS: MT5 connection and symbol validated")
            return True
        except Exception as e:
            logger.error(f"MT5 validation failed: {e}")
            return False

    def validate_directories(self):
        """Validate and create required directories."""
        try:
            required_dirs = [
                'data', 'data/historical', 'data/models', 'data/realtime',
                'logs', 'models/saved', 'models/performance', 'reports'
            ]

            for dir_path in required_dirs:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
                logger.debug(f"SUCCESS: Directory {dir_path} ready")

            logger.info("SUCCESS: Directory structure validated")
            return True
        except Exception as e:
            logger.error(f"Directory validation failed: {e}")
            return False
            
    def validate_database(self):
        """Validate database connectivity and table structure."""
        try:
            import sqlite3
            import os
            import config

            # Test database connection directly without creating SyntheticDataCollector
            db_path = os.path.join(config.DATA_CACHE_DIR, "synthetic_data.db")

            # Ensure directory exists
            os.makedirs(config.DATA_CACHE_DIR, exist_ok=True)

            # Test connection with proper settings
            conn = sqlite3.connect(db_path, timeout=30.0, check_same_thread=False)
            conn.execute("PRAGMA journal_mode=WAL;")
            conn.execute("PRAGMA busy_timeout=30000;")

            # Test basic query
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            conn.close()

            logger.info(f"SUCCESS: Database connectivity validated with {len(tables)} tables")
            return True
        except Exception as e:
            logger.error(f"Database validation failed: {e}")
            return False

    def smart_data_collection(self):
        """Smart incremental data collection."""
        try:
            from synthetic_data_collector import SyntheticDataCollector

            collector = SyntheticDataCollector()

            # Check if we have recent data
            latest_data = collector.get_latest_data(timeframe=15, count=1)
            if not latest_data.empty:
                latest_timestamp = latest_data.iloc[-1]['timestamp']
                hours_since_update = (datetime.now() - latest_timestamp).total_seconds() / 3600

                if hours_since_update < 24:  # Data is recent
                    logger.info(f"SUCCESS: Recent data found ({hours_since_update:.1f}h old), performing incremental update")
                    return collector.collect_historical_data()

            # No recent data, need full collection
            logger.info("WARNING: No recent data found, triggering full collection")
            return False

        except Exception as e:
            logger.error(f"Smart data collection failed: {e}")
            return False

    def full_data_collection(self):
        """Full historical data collection."""
        try:
            from synthetic_data_collector import SyntheticDataCollector

            collector = SyntheticDataCollector()
            logger.info("Starting full historical data collection...")

            success = collector.collect_historical_data()
            if success:
                collector.wait_for_queue_completion()
                logger.info("SUCCESS: Full data collection completed")
                return True
            else:
                logger.error("ERROR: Full data collection failed")
                return False

        except Exception as e:
            logger.error(f"Full data collection failed: {e}")
            return False

    def check_model_training_needed(self):
        """Check if model training is needed (>7 days old)."""
        try:
            models_dir = Path('models/saved')
            if not models_dir.exists():
                logger.info("No models directory found, training needed")
                return True

            # Check for model directories (each model is stored in its own directory)
            model_dirs = [d for d in models_dir.iterdir() if d.is_dir()]
            if not model_dirs:
                logger.info("No model directories found, training needed")
                return True

            # Check for actual model files within the directories
            model_files = []
            for model_dir in model_dirs:
                # Look for model files (either .keras or .pkl)
                keras_files = list(model_dir.glob('*.keras'))
                pkl_files = list(model_dir.glob('model.pkl'))
                if keras_files:
                    model_files.extend(keras_files)
                elif pkl_files:
                    model_files.extend(pkl_files)

            if not model_files:
                logger.info("No model files found in model directories, training needed")
                return True

            # Check age of newest model file
            newest_model = max(model_files, key=lambda f: f.stat().st_mtime)
            model_age = datetime.now() - datetime.fromtimestamp(newest_model.stat().st_mtime)

            if model_age.days >= 7:
                logger.info(f"Models are {model_age.days} days old, training needed")
                return True
            else:
                logger.info(f"Models are {model_age.days} days old, training not needed")
                return False

        except Exception as e:
            logger.error(f"Model age check failed: {e}")
            return True  # Default to training if check fails

    def train_all_models(self):
        """Train all AI models with real-time progress display."""
        try:
            logger.info("Starting model training process...")
            logger.info("Training 9 AI models for DEX 900 DOWN Index trading system")

            # Start model training with real-time output
            process = subprocess.Popen([
                sys.executable, 'train_all_models.py', '--auto'
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
               text=True, bufsize=1, universal_newlines=True)

            # Monitor training progress in real-time
            training_progress = {
                'models_completed': 0,
                'current_model': None,
                'total_models': 9
            }

            logger.info("Model training in progress...")

            # Read output line by line for real-time progress
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    line = output.strip()

                    # Parse training progress from output
                    self._parse_training_progress(line, training_progress)

            # Wait for process to complete
            return_code = process.wait()

            if return_code == 0:
                logger.info(f"SUCCESS: Model training completed successfully! All {training_progress['total_models']} models trained")
                return True
            else:
                logger.error(f"ERROR: Model training failed with return code: {return_code}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("ERROR: Model training timed out (>1 hour)")
            return False
        except Exception as e:
            logger.error(f"Model training failed: {e}")
            return False

    def _parse_training_progress(self, line, progress):
        """Parse training progress from model training output."""
        try:
            # Look for model training indicators
            if "Training model:" in line:
                model_name = line.split("Training model:")[-1].strip()
                progress['current_model'] = model_name
                logger.info(f"Training model: {model_name}")

            elif "Model saved:" in line:
                model_name = line.split("Model saved:")[-1].strip()
                progress['models_completed'] += 1
                logger.info(f"SUCCESS: Model completed: {model_name} ({progress['models_completed']}/{progress['total_models']})")

            elif "Training completed for" in line:
                model_name = line.split("Training completed for")[-1].strip()
                logger.info(f"Training finished: {model_name}")

            elif "SUCCESS:" in line and "completed)" in line:
                logger.info(f"SUCCESS: {line}")

            elif "ERROR:" in line or "Failed to train" in line:
                logger.warning(f"WARNING: Training issue: {line}")

            elif "FAILED:" in line:
                logger.warning(f"ERROR: {line}")

            # Show other important training messages
            elif any(keyword in line.lower() for keyword in ['epoch', 'loss', 'accuracy', 'validation']):
                if progress['current_model']:
                    logger.info(f"PROGRESS {progress['current_model']}: {line}")

        except Exception as e:
            # Don't let parsing errors stop the training
            pass

    def load_and_validate_models(self):
        """Load and validate all AI models."""
        try:
            from ai_model_manager import AIModelManager

            logger.info("Loading and validating AI models...")

            # Initialize required components for AIModelManager
            from synthetic_data_collector import SyntheticDataCollector
            from synthetic_pattern_detector import SyntheticPatternDetector

            data_collector = SyntheticDataCollector()
            pattern_detector = SyntheticPatternDetector(data_collector)
            ai_manager = AIModelManager(data_collector, pattern_detector)

            # Check if models exist and can be loaded
            model_configs = ai_manager.model_configs
            loaded_count = 0

            for model_name in model_configs.keys():
                try:
                    if ai_manager.load_model(model_name):
                        loaded_count += 1
                        logger.info(f"SUCCESS: Model loaded: {model_name}")
                    else:
                        logger.warning(f"WARNING: Model not found: {model_name}")
                except Exception as e:
                    logger.warning(f"WARNING: Failed to load {model_name}: {e}")

            if loaded_count == len(model_configs):  # All models loaded
                logger.info(f"SUCCESS: All {loaded_count}/{len(model_configs)} models loaded successfully")
                self.components['ai_manager'] = ai_manager
                return True
            else:
                logger.error(f"ERROR: Only {loaded_count}/{len(model_configs)} models loaded")
                return False

        except Exception as e:
            logger.error(f"Model loading failed: {e}")
            return False

    def initialize_trading_components(self):
        """Initialize core trading system components."""
        try:
            logger.info("Initializing trading components...")

            # Create a script to start the actual trading engine
            trading_script_content = '''
import sys
import logging
from trading_engine import TradingEngine

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trading_engine.log'),
        logging.StreamHandler()
    ]
)

def main():
    """Start the trading engine."""
    print("Starting AI Trading Engine...")

    engine = TradingEngine()

    try:
        # Initialize components
        if not engine.initialize_components():
            print("Failed to initialize trading components")
            return False

        # Start trading
        if not engine.start_trading():
            print("Failed to start trading system")
            return False

        print("Trading system started successfully!")
        print("Trading loop active - generating signals every 3 minutes")
        print("Press Ctrl+C to stop")

        # Keep running
        while engine.running:
            import time
            time.sleep(60)

    except KeyboardInterrupt:
        print("\\nShutdown requested")
        engine.stop_trading("User requested shutdown")
    except Exception as e:
        print(f"Trading engine error: {e}")
        engine.stop_trading("Error occurred")
        return False

    return True

if __name__ == "__main__":
    main()
'''

            # Write the trading script with UTF-8 encoding
            with open('start_trading_engine.py', 'w', encoding='utf-8') as f:
                f.write(trading_script_content)

            # Start the actual trading engine
            result = subprocess.Popen([
                sys.executable, 'start_trading_engine.py'
            ])

            # Give it time to initialize
            time.sleep(30)

            if result.poll() is None:  # Process is still running
                logger.info("SUCCESS: Trading engine started successfully")
                self.components['trading_engine'] = result
                return True
            else:
                logger.error("ERROR: Trading engine failed to start")
                return False

        except Exception as e:
            logger.error(f"Trading components initialization failed: {e}")
            return False

    def start_real_time_monitoring(self):
        """Start real-time monitoring systems."""
        try:
            logger.info("Real-time monitoring validation...")

            # The trading system should already be monitoring
            # Just validate it's working
            time.sleep(5)

            logger.info("SUCCESS: Real-time monitoring validated")
            return True

        except Exception as e:
            logger.error(f"Real-time monitoring startup failed: {e}")
            return False

    def validate_system_integration(self):
        """Validate all system components are working together."""
        try:
            logger.info("Validating system integration...")

            # Check if trading engine is still running
            if self.components.get('trading_engine'):
                if self.components['trading_engine'].poll() is None:
                    logger.info("SUCCESS: Trading engine running")
                else:
                    logger.error("ERROR: Trading engine stopped")
                    return False

            logger.info("SUCCESS: System integration validated")
            return True

        except Exception as e:
            logger.error(f"System integration validation failed: {e}")
            return False

    def start_dashboard_server(self):
        """Start dashboard server (fast, non-blocking)."""
        try:
            logger.info("Starting dashboard server...")

            # Quick check if dashboard is already running
            try:
                import requests
                response = requests.get('http://localhost:5000', timeout=3)
                if response.status_code == 200:
                    logger.info("✅ Dashboard server is already running and accessible!")
                    return True
            except:
                logger.info("Dashboard not running, starting new instance...")

            # Start dashboard in background (non-blocking)
            result = subprocess.Popen([
                sys.executable, 'dashboard_server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            logger.info(f"✅ Dashboard process started with PID: {result.pid}")
            logger.info("Dashboard will initialize in background (2-3 minutes)")
            logger.info("Dashboard will be available at: http://localhost:5000")

            # Store process reference
            self.components['dashboard'] = result

            # Return immediately - don't wait for initialization
            return True

        except Exception as e:
            logger.error(f"Dashboard server startup failed: {e}")
            return False



    def validate_dashboard_connection(self, auto_start=True):
        """Validate dashboard connectivity and optionally start it if not running."""
        try:
            import requests

            logger.info("Validating dashboard connection...")

            # First, try a quick connection test
            try:
                response = requests.get('http://localhost:5000', timeout=5)
                if response.status_code == 200:
                    logger.info(f"SUCCESS: Dashboard is already running (status: {response.status_code})")
                    return True
            except requests.exceptions.ConnectionError:
                logger.info("Dashboard not accessible - will attempt to start it")
            except Exception as e:
                logger.warning(f"Dashboard connection test failed: {e}")

            # If auto_start is enabled and dashboard is not running, start it
            if auto_start:
                logger.info("🚀 Starting dashboard server automatically...")
                if not self.start_dashboard_server():
                    logger.error("Failed to start dashboard server")
                    return False

                # Wait for dashboard to fully initialize
                logger.info("Waiting for dashboard to fully initialize...")
                time.sleep(15)

            # Try to connect to dashboard
            for attempt in range(1, 6):  # 1-5 attempts
                try:
                    logger.info(f"Dashboard connection attempt {attempt}/5...")
                    response = requests.get('http://localhost:5000', timeout=10)
                    if response.status_code == 200:
                        logger.info(f"SUCCESS: Dashboard connection validated (status: {response.status_code})")
                        logger.info(f"Dashboard response length: {len(response.text)} characters")
                        return True
                    else:
                        logger.warning(f"Dashboard responded with status: {response.status_code}")
                except requests.exceptions.ConnectionError as e:
                    logger.warning(f"Connection error on attempt {attempt}: {e}")
                except requests.exceptions.Timeout as e:
                    logger.warning(f"Timeout error on attempt {attempt}: {e}")
                except Exception as e:
                    logger.warning(f"Unexpected error on attempt {attempt}: {e}")

                if attempt < 5:
                    logger.info("Waiting 3 seconds before next attempt...")
                    time.sleep(3)

            # If we still can't connect and auto_start was used, try one more time with a longer wait
            if auto_start:
                logger.info("Dashboard still not accessible, waiting longer for initialization...")
                time.sleep(60)  # Wait 60 seconds for models to load
                try:
                    response = requests.get('http://localhost:5000', timeout=15)
                    if response.status_code == 200:
                        logger.info(f"SUCCESS: Dashboard connection validated after extended wait (status: {response.status_code})")
                        return True
                except Exception as e:
                    logger.error(f"Final dashboard connection attempt failed: {e}")

            logger.error("ERROR: Dashboard connection failed after all attempts")
            return False

        except ImportError:
            logger.error("ERROR: requests module not available for dashboard validation")
            return False
        except Exception as e:
            logger.error(f"Dashboard connection validation failed: {e}")
            return False

    def open_dashboard_browser(self):
        """Open dashboard in browser."""
        try:
            import webbrowser

            logger.info("Opening dashboard in browser...")
            webbrowser.open('http://localhost:5000')

            logger.info("SUCCESS: Dashboard opened in browser")
            return True

        except Exception as e:
            logger.error(f"Browser launch failed: {e}")
            return False

    def start_cache_management_system(self):
        """Start the cache management system for daily cleanup and memory monitoring."""
        try:
            import threading
            from start_cache_management_system import IntegratedCacheManagementSystem

            logger.info("Starting integrated cache management system...")

            # Create cache management system
            cache_system = IntegratedCacheManagementSystem()

            # Start cache management in separate thread
            def start_cache_thread():
                try:
                    if cache_system.start_integrated_system():
                        logger.info("SUCCESS: Cache management system started")
                        # Keep it running
                        while cache_system.running:
                            time.sleep(60)
                    else:
                        logger.error("ERROR: Cache management system failed to start")
                except Exception as e:
                    logger.error(f"ERROR: Cache management thread error: {e}")

            # Start in daemon thread so it doesn't block shutdown
            cache_thread = threading.Thread(target=start_cache_thread, daemon=True)
            cache_thread.start()

            # Give it a moment to start
            time.sleep(3)

            # Store reference for cleanup
            self.components['cache_management'] = cache_system

            logger.info("SUCCESS: Cache management system startup completed")
            logger.info("   Comprehensive cleanup: 00:00 daily")
            logger.info("   Light cleanup: Every 4 hours (04:00, 08:00, 12:00, 16:00, 20:00)")
            logger.info("   Memory monitoring: Active (80% threshold)")
            logger.info("   AI model protection: Enabled")
            logger.info("   Hybrid approach: Optimal performance consistency")

            return True

        except Exception as e:
            logger.error(f"Cache management system startup failed: {e}")
            return False

    def system_ready(self):
        """System startup completed successfully."""
        total_time = datetime.now() - self.start_time

        logger.info("=" * 80)
        logger.info("SUCCESS: AI TRADING SYSTEM STARTUP COMPLETED SUCCESSFULLY!")
        logger.info("=" * 80)
        logger.info(f"Total startup time: {total_time}")
        logger.info("Dashboard: http://localhost:5000")
        logger.info("All 9 AI models loaded and operational")
        logger.info("Real-time monitoring active")
        logger.info("System ready for trading operations")
        logger.info("Press Ctrl+C to stop the system")
        logger.info("=" * 80)

        # Keep system running
        try:
            while True:
                time.sleep(60)  # Check every minute
                self.health_check()
        except KeyboardInterrupt:
            logger.info("Shutdown requested by user")
            self.cleanup_system()

    def health_check(self):
        """Perform periodic health checks and restart stopped components."""
        try:
            # Check if components are still running
            for name, component in self.components.items():
                if hasattr(component, 'poll') and component.poll() is not None:
                    logger.warning(f"WARNING: Component {name} has stopped")
                    
                    # Attempt to restart critical components
                    if name == 'trading_engine':
                        logger.info(f"Attempting to restart {name}...")
                        if self._restart_trading_engine():
                            logger.info(f"SUCCESS: Successfully restarted {name}")
                        else:
                            logger.error(f"ERROR: Failed to restart {name}")
                    elif name == 'dashboard_server':
                        logger.info(f"Attempting to restart {name}...")
                        if self._restart_dashboard_server():
                            logger.info(f"SUCCESS: Successfully restarted {name}")
                        else:
                            logger.error(f"ERROR: Failed to restart {name}")

        except Exception as e:
            logger.error(f"Health check failed: {e}")

    def cleanup_failed_startup(self):
        """Cleanup after failed startup."""
        logger.error("Cleaning up after failed startup...")
        self.cleanup_system()

    def _restart_trading_engine(self):
        """Restart the trading engine component."""
        try:
            # Clean up old process if it exists
            if self.components.get('trading_engine'):
                try:
                    self.components['trading_engine'].terminate()
                except:
                    pass
            
            # Start new trading engine process
            result = subprocess.Popen([
                sys.executable, '-c', '''
import sys
import logging
from trading_engine import TradingEngine

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trading_engine.log'),
        logging.StreamHandler()
    ]
)

def main():
    engine = TradingEngine()
    if engine.initialize_components():
        if engine.start_trading():
            # Keep running
            while engine.running:
                import time
                time.sleep(60)
        else:
            print("Failed to start trading")
    else:
        print("Failed to initialize components")

if __name__ == "__main__":
    main()
'''
            ])
            
            # Give it time to start
            time.sleep(10)
            
            if result.poll() is None:  # Process is still running
                self.components['trading_engine'] = result
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error restarting trading engine: {e}")
            return False
    
    def _restart_dashboard_server(self):
        """Restart the dashboard server component."""
        try:
            # Clean up old process if it exists
            if self.components.get('dashboard_server'):
                try:
                    self.components['dashboard_server'].terminate()
                except:
                    pass
            
            # Start new dashboard server process
            result = subprocess.Popen([
                sys.executable, 'dashboard_server.py'
            ])
            
            # Give it time to start
            time.sleep(15)
            
            if result.poll() is None:  # Process is still running
                self.components['dashboard_server'] = result
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error restarting dashboard server: {e}")
            return False

    def cleanup_system(self):
        """Cleanup system components."""
        logger.info("Cleaning up system components...")

        for name, component in self.components.items():
            try:
                if hasattr(component, 'terminate'):
                    component.terminate()
                    logger.info(f"SUCCESS: {name} terminated")
            except:
                pass

        logger.info("SUCCESS: System cleanup completed")

def main():
    """Main entry point."""
    orchestrator = AITradingSystemOrchestrator()
    orchestrator.start_complete_system()

if __name__ == "__main__":
    main()
