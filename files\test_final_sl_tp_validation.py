#!/usr/bin/env python3
"""
Final test to verify the updated AI Trading System uses correct 50-point minimum SL/TP distances.
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_final_sl_tp_validation():
    """Test that the updated system uses correct 50-point minimum distances."""
    try:
        print("🧪 FINAL SL/TP VALIDATION TEST")
        print("=" * 60)
        print("⚠️  Testing updated system with 50-point minimum distances")
        print("=" * 60)
        
        # Import required modules
        from trading_signal_generator import TradingSignalGenerator, SignalType
        from order_execution_system import OrderExecutionSystem, OrderType
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        
        print("📊 Initializing trading components...")
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Components initialized")
        
        # Get current price
        print("💰 Getting current market price...")
        import MetaTrader5 as mt5
        tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
        if tick is None:
            print("❌ Could not get current price!")
            return False
        current_price = (tick.bid + tick.ask) / 2
        print(f"✅ Current price: {current_price:.2f}")
        
        # Test signal generator SL/TP calculation with updated minimum
        print("\n🎯 TESTING UPDATED SIGNAL GENERATOR:")
        
        test_regime = {
            'regime_name': 'QUIET',
            'volatility': 0.01,
            'jump_probability': 0.3,
            'volatility_expansion': False,
            'jumpiness': 0.4
        }
        
        import pandas as pd
        dummy_data = pd.DataFrame({'close': [current_price]})
        
        # Test BUY signal
        buy_sl, buy_tp = signal_generator._calculate_stop_loss_take_profit(
            SignalType.WEAK_BUY, current_price, test_regime, dummy_data
        )
        
        buy_sl_distance = abs(current_price - buy_sl)
        buy_tp_distance = abs(buy_tp - current_price)
        
        print(f"   BUY Signal SL/TP:")
        print(f"   - Stop Loss: {buy_sl:.2f} (distance: {buy_sl_distance:.2f})")
        print(f"   - Take Profit: {buy_tp:.2f} (distance: {buy_tp_distance:.2f})")
        
        if buy_sl_distance >= 50.0 and buy_tp_distance >= 50.0:
            print("   ✅ BUY SL/TP distances meet 50-point minimum")
        else:
            print("   ❌ BUY SL/TP distances below 50-point minimum")
            return False
            
        # Test SELL signal
        sell_sl, sell_tp = signal_generator._calculate_stop_loss_take_profit(
            SignalType.WEAK_SELL, current_price, test_regime, dummy_data
        )
        
        sell_sl_distance = abs(sell_sl - current_price)
        sell_tp_distance = abs(current_price - sell_tp)
        
        print(f"   SELL Signal SL/TP:")
        print(f"   - Stop Loss: {sell_sl:.2f} (distance: {sell_sl_distance:.2f})")
        print(f"   - Take Profit: {sell_tp:.2f} (distance: {sell_tp_distance:.2f})")
        
        if sell_sl_distance >= 50.0 and sell_tp_distance >= 50.0:
            print("   ✅ SELL SL/TP distances meet 50-point minimum")
        else:
            print("   ❌ SELL SL/TP distances below 50-point minimum")
            return False
            
        # Test order execution validation with updated minimum
        print("\n🔧 TESTING UPDATED ORDER EXECUTION VALIDATION:")
        
        # Test with distance below 50 (should be corrected)
        test_sl_too_close = current_price - 30.0  # Only 30 points (should be corrected to 50)
        corrected_sl = order_executor._validate_sl_tp_distance(
            test_sl_too_close, current_price, OrderType.BUY, "SL"
        )
        
        if corrected_sl:
            corrected_distance = abs(current_price - corrected_sl)
            print(f"   Auto-correction test:")
            print(f"   - Original: {test_sl_too_close:.2f} (30 points)")
            print(f"   - Corrected: {corrected_sl:.2f} ({corrected_distance:.2f} points)")
            
            if corrected_distance >= 50.0:
                print("   ✅ Auto-correction working with 50-point minimum")
            else:
                print("   ❌ Auto-correction not using 50-point minimum")
                return False
        else:
            print("   ❌ Auto-correction failed")
            return False
            
        # Test with distance above 50 (should be unchanged)
        test_sl_good = current_price - 60.0  # 60 points (above minimum)
        unchanged_sl = order_executor._validate_sl_tp_distance(
            test_sl_good, current_price, OrderType.BUY, "SL"
        )
        
        if unchanged_sl == test_sl_good:
            print(f"   ✅ Valid distance unchanged: {test_sl_good:.2f} (60 points)")
        else:
            print(f"   ❌ Valid distance incorrectly changed: {test_sl_good:.2f} → {unchanged_sl:.2f}")
            return False
            
        # Summary
        print(f"\n📋 FINAL VALIDATION SUMMARY:")
        print(f"   ✅ Signal Generator: Uses 50-point minimum")
        print(f"   ✅ Order Execution: Validates 50-point minimum")
        print(f"   ✅ Auto-correction: Works with 50-point minimum")
        print(f"   ✅ System Updated: Ready for reliable trading")
        
        print(f"\n🎊 SYSTEM READY FOR OVERNIGHT TRADING:")
        print(f"   ✅ All SL/TP distances will be ≥50 points")
        print(f"   ✅ No order rejections due to insufficient distances")
        print(f"   ✅ Automatic correction of any invalid levels")
        print(f"   ✅ Full compliance with DEX 900 DOWN Index requirements")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 FINAL SL/TP VALIDATION TEST")
    print("=" * 60)
    print("⚠️  This verifies the system uses correct 50-point minimum")
    print("=" * 60)
    
    success = test_final_sl_tp_validation()
    
    if success:
        print("\n🎉 FINAL SL/TP VALIDATION PASSED!")
        print("✅ AI Trading System updated with correct minimums!")
        print("✅ System ready for reliable overnight trading!")
        print("✅ No more order rejections due to SL/TP distances!")
    else:
        print("\n❌ FINAL SL/TP VALIDATION FAILED!")
        print("🔧 System may still have SL/TP distance issues.")
        
    sys.exit(0 if success else 1)
