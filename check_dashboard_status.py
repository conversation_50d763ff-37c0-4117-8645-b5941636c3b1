#!/usr/bin/env python3
"""
Quick Dashboard Status Checker
Provides instant status of the dashboard server.
"""

import requests
import logging
import subprocess
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dashboard_status():
    """Check dashboard status and provide detailed information."""
    logger.info("🔍 Checking Dashboard Status...")
    
    # Test connection
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            logger.info("✅ Dashboard is RUNNING and accessible")
            logger.info(f"   URL: http://localhost:5000")
            logger.info(f"   Response size: {len(response.text)} characters")
            logger.info(f"   Status code: {response.status_code}")
            return True
        else:
            logger.warning(f"⚠️ Dashboard responded with status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        logger.error("❌ Dashboard is NOT accessible (connection refused)")
        return False
    except requests.exceptions.Timeout:
        logger.error("❌ Dashboard connection timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Error checking dashboard: {e}")
        return False

def check_dashboard_processes():
    """Check if dashboard processes are running."""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            if 'python.exe' in result.stdout:
                lines = [line for line in result.stdout.split('\n') if 'python.exe' in line]
                logger.info(f"🔍 Found {len(lines)} Python processes running")
                return len(lines) > 0
            else:
                logger.info("🔍 No Python processes found")
                return False
        else:  # Unix/Linux
            result = subprocess.run(['pgrep', '-f', 'python'], capture_output=True, text=True)
            processes = result.stdout.strip().split('\n') if result.stdout.strip() else []
            logger.info(f"🔍 Found {len(processes)} Python processes running")
            return len(processes) > 0
    except Exception as e:
        logger.error(f"Error checking processes: {e}")
        return False

def main():
    """Main status check function."""
    logger.info("=" * 50)
    logger.info("DASHBOARD STATUS CHECK")
    logger.info("=" * 50)
    
    # Check dashboard accessibility
    dashboard_accessible = check_dashboard_status()
    
    # Check processes
    processes_running = check_dashboard_processes()
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("STATUS SUMMARY")
    logger.info("=" * 50)
    
    if dashboard_accessible:
        logger.info("✅ Dashboard Status: RUNNING")
        logger.info("✅ Recommendation: Dashboard is working correctly")
    elif processes_running:
        logger.info("⚠️ Dashboard Status: STARTING/LOADING")
        logger.info("⚠️ Recommendation: Wait a few minutes for dashboard to finish loading")
    else:
        logger.info("❌ Dashboard Status: NOT RUNNING")
        logger.info("❌ Recommendation: Run 'python start_dashboard_robust.py' to start dashboard")
    
    return dashboard_accessible

if __name__ == "__main__":
    main()
