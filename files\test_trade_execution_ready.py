#!/usr/bin/env python3
"""
Test script to verify that the AI Trading System can execute trades with proper SL/TP distances
when signals are generated. This ensures no execution errors due to minimum distance violations.
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_trade_execution_ready():
    """Test that the system can execute trades with proper SL/TP when signals are present."""
    try:
        print("🎯 TESTING TRADE EXECUTION READINESS")
        print("=" * 60)
        print("⚠️  This test verifies the system can execute trades without SL/TP errors")
        print("=" * 60)
        
        # Import required modules
        from trading_signal_generator import TradingSignalGenerator, TradingSignal, SignalType
        from order_execution_system import OrderExecutionSystem, OrderType
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        
        print("📊 Initializing trading components...")
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Components initialized")
        
        # Get current price
        print("💰 Getting current market price...")
        import MetaTrader5 as mt5
        tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
        if tick is None:
            print("❌ Could not get current price!")
            return False
        current_price = (tick.bid + tick.ask) / 2
        print(f"✅ Current price: {current_price:.2f}")
        
        # Test 1: Create a manual test signal with proper SL/TP
        print("\n🧪 TEST 1: MANUAL SIGNAL WITH PROPER SL/TP")
        
        # Create test signal with validated SL/TP distances
        MIN_DISTANCE = 10.05
        
        test_signal = TradingSignal(
            signal_type=SignalType.WEAK_BUY,
            confidence=0.7,
            entry_price=current_price,
            stop_loss=current_price - MIN_DISTANCE - 5.0,  # Extra buffer beyond minimum
            take_profit=current_price + MIN_DISTANCE + 5.0,  # Extra buffer beyond minimum
            position_size=0.01,  # Minimum position size
            risk_reward_ratio=2.0,
            timeframe=15,
            timestamp=datetime.now(),
            ai_predictions={"test": 0.7},
            market_regime="TEST",
            reasoning="Manual test signal with proper SL/TP distances"
        )
        
        print(f"✅ Test signal created:")
        print(f"   Type: {test_signal.signal_type.name}")
        print(f"   Entry: {test_signal.entry_price:.2f}")
        print(f"   Stop Loss: {test_signal.stop_loss:.2f} (distance: {abs(current_price - test_signal.stop_loss):.2f})")
        print(f"   Take Profit: {test_signal.take_profit:.2f} (distance: {abs(test_signal.take_profit - current_price):.2f})")
        print(f"   Min Required: {MIN_DISTANCE:.2f}")
        
        # Verify distances meet requirements
        sl_distance = abs(current_price - test_signal.stop_loss)
        tp_distance = abs(test_signal.take_profit - current_price)
        
        if sl_distance >= MIN_DISTANCE and tp_distance >= MIN_DISTANCE:
            print("✅ SL/TP distances meet minimum requirements")
        else:
            print("❌ SL/TP distances below minimum - this would cause execution errors!")
            return False
            
        # Test 2: Validate order execution system can handle the signal
        print("\n🧪 TEST 2: ORDER EXECUTION VALIDATION")
        
        # Check if order can be executed
        can_execute = order_executor._can_execute_order(test_signal)
        if can_execute:
            print("✅ Order execution system accepts the signal")
        else:
            print("❌ Order execution system rejects the signal")
            return False
            
        # Test 3: Validate SL/TP distance checking
        print("\n🧪 TEST 3: SL/TP DISTANCE VALIDATION")
        
        # Test the validation method directly
        validated_sl = order_executor._validate_sl_tp_distance(
            test_signal.stop_loss, current_price, OrderType.BUY, "SL"
        )
        validated_tp = order_executor._validate_sl_tp_distance(
            test_signal.take_profit, current_price, OrderType.BUY, "TP"
        )
        
        if validated_sl == test_signal.stop_loss:
            print("✅ Stop Loss validation PASSED (no adjustment needed)")
        else:
            print(f"⚠️  Stop Loss adjusted: {test_signal.stop_loss:.2f} → {validated_sl:.2f}")
            
        if validated_tp == test_signal.take_profit:
            print("✅ Take Profit validation PASSED (no adjustment needed)")
        else:
            print(f"⚠️  Take Profit adjusted: {test_signal.take_profit:.2f} → {validated_tp:.2f}")
            
        # Test 4: Test with insufficient distances (should be auto-corrected)
        print("\n🧪 TEST 4: AUTO-CORRECTION OF INSUFFICIENT DISTANCES")
        
        # Create signal with distances too small
        bad_sl = current_price - 5.0  # Only 5 points (should be 10.05+)
        bad_tp = current_price + 5.0  # Only 5 points (should be 10.05+)
        
        corrected_sl = order_executor._validate_sl_tp_distance(
            bad_sl, current_price, OrderType.BUY, "SL"
        )
        corrected_tp = order_executor._validate_sl_tp_distance(
            bad_tp, current_price, OrderType.BUY, "TP"
        )
        
        if corrected_sl and abs(current_price - corrected_sl) >= MIN_DISTANCE:
            print(f"✅ SL auto-correction WORKING: {bad_sl:.2f} → {corrected_sl:.2f}")
        else:
            print("❌ SL auto-correction FAILED")
            return False
            
        if corrected_tp and abs(corrected_tp - current_price) >= MIN_DISTANCE:
            print(f"✅ TP auto-correction WORKING: {bad_tp:.2f} → {corrected_tp:.2f}")
        else:
            print("❌ TP auto-correction FAILED")
            return False
            
        # Test 5: Signal generator SL/TP calculation
        print("\n🧪 TEST 5: SIGNAL GENERATOR SL/TP CALCULATION")
        
        # Test regime analysis
        test_regime = {
            'regime_name': 'QUIET',
            'volatility': 0.01,
            'jump_probability': 0.3,
            'volatility_expansion': False,
            'jumpiness': 0.4
        }
        
        # Test signal generator SL/TP calculation
        import pandas as pd
        dummy_data = pd.DataFrame({'close': [current_price]})
        
        calc_sl, calc_tp = signal_generator._calculate_stop_loss_take_profit(
            SignalType.WEAK_BUY, current_price, test_regime, dummy_data
        )
        
        calc_sl_distance = abs(current_price - calc_sl)
        calc_tp_distance = abs(calc_tp - current_price)
        
        print(f"   Signal Generator SL: {calc_sl:.2f} (distance: {calc_sl_distance:.2f})")
        print(f"   Signal Generator TP: {calc_tp:.2f} (distance: {calc_tp_distance:.2f})")
        
        if calc_sl_distance >= MIN_DISTANCE and calc_tp_distance >= MIN_DISTANCE:
            print("✅ Signal generator produces compliant SL/TP distances")
        else:
            print("❌ Signal generator produces non-compliant SL/TP distances")
            return False
            
        # Test 6: Account and symbol readiness
        print("\n🧪 TEST 6: ACCOUNT AND SYMBOL READINESS")
        
        # Check account
        account_info = mt5.account_info()
        if account_info and account_info.trade_allowed:
            print(f"✅ Account ready: ${account_info.balance:.2f} balance, trading allowed")
        else:
            print("❌ Account not ready for trading")
            return False
            
        # Check symbol
        symbol_info = mt5.symbol_info("DEX 900 DOWN Index")
        if symbol_info and symbol_info.trade_mode == 4:
            print(f"✅ Symbol ready: Full trading mode, spread {symbol_info.spread} points")
        else:
            print("❌ Symbol not ready for trading")
            return False
            
        # Summary
        print("\n📋 TRADE EXECUTION READINESS SUMMARY:")
        print("✅ SL/TP minimum distances properly enforced")
        print("✅ Auto-correction working for insufficient distances")
        print("✅ Signal generator produces compliant SL/TP levels")
        print("✅ Order execution system validates distances")
        print("✅ Account and symbol ready for trading")
        print("✅ System will NOT encounter SL/TP distance errors")
        
        print("\n🎉 CONCLUSION:")
        print("   When the AI generates trading signals, the system will:")
        print("   1. ✅ Calculate proper SL/TP distances (≥10.05 points)")
        print("   2. ✅ Auto-correct any insufficient distances")
        print("   3. ✅ Execute trades without distance-related errors")
        print("   4. ✅ Comply with DEX 900 DOWN Index requirements")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 AI TRADING SYSTEM - TRADE EXECUTION READINESS TEST")
    print("=" * 60)
    print("⚠️  This verifies the system can execute trades without SL/TP errors")
    print("=" * 60)
    
    success = test_trade_execution_ready()
    
    if success:
        print("\n🎉 TRADE EXECUTION READINESS TEST PASSED!")
        print("✅ The system is ready to execute trades without SL/TP errors!")
        print("✅ When signals are generated, trades will execute successfully!")
    else:
        print("\n❌ TRADE EXECUTION READINESS TEST FAILED!")
        print("🔧 The system may encounter errors when trying to execute trades.")
        
    sys.exit(0 if success else 1)
