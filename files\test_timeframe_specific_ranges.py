#!/usr/bin/env python3
"""
Test the timeframe-specific historical data collection ranges.
Verify that 1-minute data uses appropriate date ranges.
"""

import sys
from datetime import datetime, timed<PERSON>ta

def test_optimal_start_date_calculation():
    """Test the optimal start date calculation for different timeframes."""
    print("🧪 TESTING OPTIMAL START DATE CALCULATION")
    print("=" * 60)
    
    try:
        from synthetic_data_collector import SyntheticDataCollector
        import config
        
        # Initialize data collector
        data_collector = SyntheticDataCollector()
        
        # Test parameters
        end_date = datetime.now()
        default_start_date = config.START_DATE  # 2023-04-01
        
        print(f"📅 Test Parameters:")
        print(f"   End Date: {end_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   Default Start Date: {default_start_date.strftime('%Y-%m-%d')}")
        print(f"   Default Range: {(end_date - default_start_date).days} days")
        
        # Test different timeframes
        timeframes_to_test = [1, 5, 15, 30, 60, 240, 1440]
        
        print(f"\n🕐 TIMEFRAME-SPECIFIC DATE RANGES:")
        print("-" * 60)
        
        for interval in timeframes_to_test:
            optimal_start = data_collector._get_optimal_start_date(interval, end_date, default_start_date)
            days_back = (end_date - optimal_start).days
            
            # Determine expected behavior
            if interval == 1:
                expected_days = 30
                reason = "Synthetic 1-min limitation"
            elif interval == 5:
                expected_days = 90
                reason = "5-min data availability"
            elif interval <= 30:
                expected_days = 180
                reason = "Medium frequency data"
            else:
                expected_days = (end_date - default_start_date).days
                reason = "Full historical range"
            
            status = "✅" if abs(days_back - expected_days) <= 1 else "❌"  # Allow 1 day tolerance
            
            print(f"{status} {interval:4}min: {days_back:4} days | {optimal_start.strftime('%Y-%m-%d')} | {reason}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing optimal start dates: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_historical_data_collection():
    """Test actual historical data collection with new ranges."""
    print(f"\n📊 TESTING HISTORICAL DATA COLLECTION")
    print("=" * 60)
    
    try:
        from synthetic_data_collector import SyntheticDataCollector
        
        # Initialize data collector
        data_collector = SyntheticDataCollector()
        
        if not data_collector.mt5_connected:
            print("❌ MT5 not connected - cannot test data collection")
            return False
        
        print("✅ MT5 connected successfully")
        
        # Test specific timeframes with small data collection
        test_timeframes = [1, 5, 15]  # Test key timeframes
        
        print(f"\n🔍 TESTING DATA COLLECTION FOR KEY TIMEFRAMES:")
        print("-" * 50)
        
        for interval in test_timeframes:
            print(f"\n📈 Testing {interval}-minute data collection:")
            
            # Calculate optimal date range
            end_date = datetime.now()
            optimal_start = data_collector._get_optimal_start_date(interval, end_date, datetime(2023, 4, 1))
            
            # Test with a smaller range for quick testing (last 24 hours)
            test_end = datetime.now()
            test_start = test_end - timedelta(hours=24)
            
            print(f"   Optimal range: {optimal_start.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            print(f"   Test range: {test_start.strftime('%Y-%m-%d %H:%M')} to {test_end.strftime('%Y-%m-%d %H:%M')}")
            
            try:
                # Test data collection
                data_collector.collect_timeframe_data(interval, test_start, test_end)
                
                # Check if data was collected
                df = data_collector.get_latest_data(interval, count=10)
                
                if not df.empty:
                    print(f"   ✅ Success: {len(df)} records collected")
                    latest_time = df['timestamp'].max()
                    print(f"   📅 Latest data: {latest_time}")
                else:
                    print(f"   ⚠️  No data collected (may be normal for some timeframes)")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing data collection: {e}")
        return False

def test_1min_data_specifically():
    """Test 1-minute data collection specifically."""
    print(f"\n🎯 SPECIFIC 1-MINUTE DATA TEST")
    print("=" * 50)
    
    try:
        from synthetic_data_collector import SyntheticDataCollector
        
        data_collector = SyntheticDataCollector()
        
        if not data_collector.mt5_connected:
            print("❌ MT5 not connected")
            return False
        
        # Test 1-minute data with the new optimal range
        end_date = datetime.now()
        optimal_start = data_collector._get_optimal_start_date(1, end_date, datetime(2023, 4, 1))
        
        print(f"📊 1-Minute Data Collection Test:")
        print(f"   Optimal Start: {optimal_start.strftime('%Y-%m-%d')}")
        print(f"   End Date: {end_date.strftime('%Y-%m-%d')}")
        print(f"   Range: {(end_date - optimal_start).days} days")
        
        # Test with last 6 hours for quick verification
        test_start = end_date - timedelta(hours=6)
        
        print(f"\n🔍 Quick test (last 6 hours):")
        print(f"   From: {test_start.strftime('%Y-%m-%d %H:%M')}")
        print(f"   To: {end_date.strftime('%Y-%m-%d %H:%M')}")
        
        # Collect data
        data_collector.collect_timeframe_data(1, test_start, end_date)
        
        # Verify collection
        df = data_collector.get_latest_data(1, count=50)
        
        if not df.empty:
            print(f"   ✅ SUCCESS: {len(df)} 1-minute records collected")
            print(f"   📈 Price range: {df['close'].min():.2f} - {df['close'].max():.2f}")
            print(f"   🕐 Time range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        else:
            print(f"   ❌ No 1-minute data collected")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in 1-minute test: {e}")
        return False

def main():
    """Main test function."""
    print("🔧 TIMEFRAME-SPECIFIC HISTORICAL RANGES TEST")
    print("=" * 70)
    
    # Test optimal start date calculation
    calc_test = test_optimal_start_date_calculation()
    
    # Test historical data collection
    collection_test = test_historical_data_collection()
    
    # Test 1-minute data specifically
    minute_test = test_1min_data_specifically()
    
    print(f"\n🎯 TEST RESULTS:")
    print("=" * 40)
    
    if calc_test and collection_test and minute_test:
        print("🎉 ALL TESTS PASSED!")
        print("\n💡 WHAT THIS MEANS:")
        print("✅ 1-minute data will use 30-day range (optimal for synthetics)")
        print("✅ 5-minute data will use 90-day range")
        print("✅ 15+ minute data will use full historical range")
        print("✅ No more 'No data received for 1min timeframe' warnings")
        
        print(f"\n📋 NEXT STEPS:")
        print("1. Run your data collection system")
        print("2. 1-minute data should collect successfully")
        print("3. All timeframes will use appropriate historical ranges")
        return True
    else:
        print("❌ SOME TESTS FAILED")
        print("Check the error messages above for details")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
