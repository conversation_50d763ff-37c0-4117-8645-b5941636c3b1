#!/usr/bin/env python3
"""
Test current EMA/SMA values to check if convergence filter should be blocking trades.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from synthetic_data_collector import SyntheticDataCollector
    from ema_sma_distance_filter import EMASMADistanceFilter
    import config
    from synthetic_pattern_detector import SyntheticPatternDetector
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

def test_current_ema_sma_values():
    """Test current EMA/SMA values and filter behavior."""
    print("🔍 TESTING CURRENT EMA/SMA VALUES")
    print("=" * 50)
    
    try:
        # Initialize components
        print("📊 Initializing data collector...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)

        # Initialize EMA/SMA filter with current config
        ema_sma_filter = EMASMADistanceFilter(
            min_distance_pct=config.EMA_SMA_FILTER.get('min_distance_pct', 0.15),
            enabled=config.EMA_SMA_FILTER.get('enabled', True)
        )

        print(f"🎯 Filter configuration:")
        print(f"   Enabled: {ema_sma_filter.enabled}")
        print(f"   Threshold: {ema_sma_filter.min_distance_pct}%")

        # Get recent market data
        print("\n📈 Fetching recent market data...")

        # Get 5-minute data (most commonly used timeframe)
        market_data = data_collector.get_latest_data(timeframe=5, count=100)
        
        if market_data is None or market_data.empty:
            print("❌ No market data available")
            return False
        
        print(f"✅ Retrieved {len(market_data)} data points")
        print(f"   Latest time: {market_data.index[-1]}")
        print(f"   Latest price: {market_data['close'].iloc[-1]:.2f}")
        
        # Calculate indicators
        print("\n🧮 Calculating indicators...")
        df_indicators = pattern_detector.calculate_synthetic_indicators(market_data)

        if df_indicators.empty:
            print("❌ No indicators calculated")
            return False

        # Add EMA20 and SMA50 calculations (these are not included in synthetic indicators)
        print("📊 Adding EMA20 and SMA50 calculations...")
        df_indicators['ema20'] = df_indicators['close'].ewm(span=20, adjust=False).mean()
        df_indicators['sma50'] = df_indicators['close'].rolling(window=50).mean()

        # Get latest values
        latest = df_indicators.iloc[-1]
        ema20 = latest.get('ema20', np.nan)
        sma50 = latest.get('sma50', np.nan)
        
        print(f"📊 Latest indicator values:")
        print(f"   EMA20: {ema20:.2f}")
        print(f"   SMA50: {sma50:.2f}")
        
        if pd.isna(ema20) or pd.isna(sma50):
            print("❌ EMA20 or SMA50 values are missing")
            return False
        
        # Calculate distance manually
        distance_pct = abs((ema20 - sma50) / sma50) * 100
        print(f"   Distance: {distance_pct:.3f}%")
        print(f"   EMA above SMA: {ema20 > sma50}")
        
        # Test the filter
        print(f"\n🔍 Testing EMA/SMA filter...")
        filter_result, filter_reason, filter_details = ema_sma_filter.check_dataframe(df_indicators)
        
        print(f"📋 Filter result:")
        print(f"   Allowed: {filter_result}")
        print(f"   Reason: {filter_reason}")
        print(f"   Threshold: {filter_details.get('threshold', 'N/A')}%")
        
        # Determine if system should be trading
        print(f"\n🎯 ANALYSIS:")
        if filter_result:
            print("✅ TRADING SHOULD BE ALLOWED")
            print(f"   Distance ({distance_pct:.3f}%) is above threshold ({ema_sma_filter.min_distance_pct}%)")
            print("   EMA and SMA have sufficient separation for clear signals")
        else:
            print("🚫 TRADING SHOULD BE BLOCKED")
            print(f"   Distance ({distance_pct:.3f}%) is below threshold ({ema_sma_filter.min_distance_pct}%)")
            print("   EMA and SMA are too close - unclear trend signals")
        
        # Show recent trend
        print(f"\n📈 Recent EMA/SMA trend (last 10 periods):")
        recent_data = df_indicators.tail(10)
        for i, (idx, row) in enumerate(recent_data.iterrows()):
            ema = row.get('ema20', np.nan)
            sma = row.get('sma50', np.nan)
            if not pd.isna(ema) and not pd.isna(sma):
                dist = abs((ema - sma) / sma) * 100
                status = "✅" if dist >= ema_sma_filter.min_distance_pct else "🚫"
                print(f"   {i+1:2d}. {status} EMA: {ema:.2f}, SMA: {sma:.2f}, Dist: {dist:.3f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing EMA/SMA values: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_chart_values():
    """Test with approximate values from the user's chart."""
    print("\n🖼️  TESTING WITH CHART VALUES")
    print("=" * 50)
    
    # From the chart, it looks like:
    # - Current price around 5742
    # - EMA (green line) and SMA (red line) are very close
    # - Both appear to be around 5740-5742 range
    
    chart_scenarios = [
        {"name": "Chart scenario 1", "ema20": 5741.5, "sma50": 5741.0},  # Very close
        {"name": "Chart scenario 2", "ema20": 5742.0, "sma50": 5740.5},  # Close but maybe OK
        {"name": "Chart scenario 3", "ema20": 5741.0, "sma50": 5742.0},  # SMA above EMA
    ]
    
    # Initialize filter
    ema_sma_filter = EMASMADistanceFilter(
        min_distance_pct=config.EMA_SMA_FILTER.get('min_distance_pct', 0.15),
        enabled=config.EMA_SMA_FILTER.get('enabled', True)
    )
    
    print(f"🎯 Using threshold: {ema_sma_filter.min_distance_pct}%")
    
    for scenario in chart_scenarios:
        print(f"\n🧪 {scenario['name']}:")
        ema20 = scenario['ema20']
        sma50 = scenario['sma50']
        
        is_sufficient, reason, details = ema_sma_filter.is_distance_sufficient(ema20, sma50)
        distance_pct = details.get('distance_pct', 0)
        
        status = "✅ ALLOWED" if is_sufficient else "🚫 BLOCKED"
        print(f"   {status}")
        print(f"   EMA20: {ema20:.2f}, SMA50: {sma50:.2f}")
        print(f"   Distance: {distance_pct:.3f}%")
        print(f"   Reason: {reason}")

if __name__ == "__main__":
    print("🔍 EMA/SMA CONVERGENCE FILTER TEST")
    print("=" * 60)
    
    # Test current values
    success = test_current_ema_sma_values()
    
    # Test with chart approximations
    test_with_chart_values()
    
    if success:
        print("\n✅ Test completed successfully")
    else:
        print("\n❌ Test failed - check system configuration")
