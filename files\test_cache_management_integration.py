#!/usr/bin/env python3
"""
Test script to verify cache management system integration with main AI trading system.
"""

import sys
import os
import time
from datetime import datetime

def test_cache_management_integration():
    """Test that cache management system is properly integrated."""
    print("🔍 TESTING CACHE MANAGEMENT INTEGRATION")
    print("=" * 60)
    
    try:
        # Test 1: Check if cache management system can be imported
        print("\n🧪 TEST 1: Cache Management System Import")
        print("-" * 50)
        
        try:
            from start_cache_management_system import IntegratedCacheManagementSystem
            print("✅ IntegratedCacheManagementSystem imported successfully")
        except ImportError as e:
            print(f"❌ Failed to import cache management system: {e}")
            return False
        
        # Test 2: Check if system orchestrator has cache management phase
        print("\n🧪 TEST 2: System Orchestrator Integration")
        print("-" * 50)
        
        try:
            from system_orchestrator import AITradingSystemOrchestrator
            orchestrator = AITradingSystemOrchestrator()
            
            # Check if cache_management phase exists
            if 'cache_management' in orchestrator.phases:
                print("✅ Cache management phase found in orchestrator")
            else:
                print("❌ Cache management phase missing from orchestrator")
                return False
                
            # Check if cache management method exists
            if hasattr(orchestrator, 'start_cache_management_system'):
                print("✅ start_cache_management_system method found")
            else:
                print("❌ start_cache_management_system method missing")
                return False
                
            # Check if phase4_cache_management method exists
            if hasattr(orchestrator, 'phase4_cache_management'):
                print("✅ phase4_cache_management method found")
            else:
                print("❌ phase4_cache_management method missing")
                return False
                
        except Exception as e:
            print(f"❌ System orchestrator integration test failed: {e}")
            return False
        
        # Test 3: Test cache management system creation
        print("\n🧪 TEST 3: Cache Management System Creation")
        print("-" * 50)
        
        try:
            cache_system = IntegratedCacheManagementSystem()
            print("✅ Cache management system created successfully")
            
            # Check configuration
            config = cache_system.config
            print(f"   Memory threshold: {config['memory_threshold']}%")
            print(f"   Daily cleanup time: {config['daily_cleanup_time']}")
            print(f"   Emergency cleanup: {config['emergency_cleanup_enabled']}")
            
        except Exception as e:
            print(f"❌ Cache management system creation failed: {e}")
            return False
        
        # Test 4: Test daily cleanup system
        print("\n🧪 TEST 4: Daily Cleanup System")
        print("-" * 50)
        
        try:
            from daily_cache_cleanup_system import DailyCacheCleanupSystem
            cleanup_system = DailyCacheCleanupSystem()
            print("✅ Daily cleanup system created successfully")
            
            # Check protected files
            protected_count = len(cleanup_system.protected_files)
            print(f"   Protected files/directories: {protected_count}")
            
            # Check if AI models are protected
            ai_protected = any('models/' in path for path in cleanup_system.protected_files)
            if ai_protected:
                print("✅ AI models are protected from cleanup")
            else:
                print("❌ AI models not protected from cleanup")
                return False
                
        except Exception as e:
            print(f"❌ Daily cleanup system test failed: {e}")
            return False
        
        # Test 5: Test memory monitoring system
        print("\n🧪 TEST 5: Memory Monitoring System")
        print("-" * 50)
        
        try:
            from memory_monitor_system import MemoryMonitorSystem
            memory_monitor = MemoryMonitorSystem()
            print("✅ Memory monitoring system created successfully")
            
            # Test memory info retrieval
            memory_info = memory_monitor.get_memory_info()
            if memory_info:
                print(f"   Current memory usage: {memory_info['system_percent']:.1f}%")
                print(f"   Available memory: {memory_info['system_available_gb']:.2f} GB")
                print("✅ Memory monitoring working correctly")
            else:
                print("❌ Memory monitoring not working")
                return False
                
        except Exception as e:
            print(f"❌ Memory monitoring system test failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def test_startup_sequence():
    """Test the expected startup sequence with cache management."""
    print("\n🎯 EXPECTED STARTUP SEQUENCE WITH CACHE MANAGEMENT")
    print("=" * 60)
    
    phases = [
        "1. Environment Validation",
        "2. Data Collection", 
        "3. Model Preparation",
        "4. Cache Management System ← NEW",
        "5. Trading System Activation",
        "6. Dashboard Launch"
    ]
    
    for phase in phases:
        print(f"   {phase}")
    
    print("\n📊 CACHE MANAGEMENT BENEFITS:")
    print("   ✅ Daily cleanup at 00:00 (midnight)")
    print("   ✅ Memory monitoring (80% threshold)")
    print("   ✅ AI model protection (never deleted)")
    print("   ✅ Automatic cache refresh for consistent performance")
    print("   ✅ Emergency cleanup when memory high")
    
    return True

def test_cache_cleanup_schedule():
    """Test that cache cleanup is properly scheduled."""
    print("\n⏰ CACHE CLEANUP SCHEDULE TEST")
    print("=" * 60)
    
    try:
        import schedule
        from daily_cache_cleanup_system import schedule_daily_cleanup
        
        print("✅ Schedule library available")
        print("✅ Daily cleanup scheduler function available")
        
        print("\n📅 CLEANUP SCHEDULE:")
        print("   Time: 00:00 (midnight)")
        print("   Frequency: Daily")
        print("   What gets cleaned:")
        print("     - Tick data >30 days old")
        print("     - Log files >7 days old (archived)")
        print("     - Temporary cache files")
        print("     - Python __pycache__ directories")
        print("     - Memory garbage collection")
        
        print("\n🛡️ WHAT'S PROTECTED:")
        print("     - All AI model files (.pkl, .keras)")
        print("     - Historical OHLCV data")
        print("     - Pattern events")
        print("     - Synthetic indicators")
        print("     - Configuration files")
        
        return True
        
    except Exception as e:
        print(f"❌ Schedule test failed: {e}")
        return False

def main():
    """Run all cache management integration tests."""
    print("🎯 CACHE MANAGEMENT INTEGRATION TESTS")
    print("=" * 80)
    print("Testing integration of cache management system with AI trading system")
    print("This addresses the issue of AI performance degrading throughout the day")
    print("=" * 80)
    
    tests = [
        ("Cache Management Integration", test_cache_management_integration),
        ("Startup Sequence", test_startup_sequence),
        ("Cache Cleanup Schedule", test_cache_cleanup_schedule),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 TEST RESULTS SUMMARY:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Cache management is properly integrated.")
        print("💡 The AI trading system will now:")
        print("   - Start cache management automatically")
        print("   - Clean cache daily at midnight")
        print("   - Monitor memory usage continuously")
        print("   - Maintain consistent AI performance throughout the day")
        print("\n🔄 Next steps:")
        print("   1. Restart AI trading system using start_complete_ai_trading_system.bat")
        print("   2. Verify cache management logs appear in logs/cache_management.log")
        print("   3. Monitor AI performance consistency throughout trading days")
    else:
        print("⚠️  Some tests failed. Please review the integration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
