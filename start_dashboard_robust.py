#!/usr/bin/env python3
"""
Robust Dashboard Startup Script
Ensures the dashboard server starts and stays running with automatic restart on failure.
"""

import subprocess
import time
import sys
import os
import logging
import requests
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dashboard_running():
    """Check if dashboard is accessible."""
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        return response.status_code == 200
    except:
        return False

def kill_existing_dashboard():
    """Kill any existing dashboard processes."""
    try:
        # Kill any existing Python processes that might be running the dashboard
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                         capture_output=True, text=True)
        else:  # Unix/Linux
            subprocess.run(['pkill', '-f', 'dashboard_server.py'], 
                         capture_output=True, text=True)
        time.sleep(2)
    except Exception as e:
        logger.warning(f"Error killing existing processes: {e}")

def start_dashboard():
    """Start the dashboard server."""
    try:
        logger.info("Starting dashboard server...")
        
        # Start dashboard in a new process
        if os.name == 'nt':  # Windows
            process = subprocess.Popen(
                [sys.executable, 'dashboard_server.py'],
                creationflags=subprocess.CREATE_NEW_CONSOLE,
                cwd=os.getcwd()
            )
        else:  # Unix/Linux
            process = subprocess.Popen(
                [sys.executable, 'dashboard_server.py'],
                cwd=os.getcwd()
            )
        
        logger.info(f"Dashboard process started with PID: {process.pid}")
        return process
        
    except Exception as e:
        logger.error(f"Error starting dashboard: {e}")
        return None

def wait_for_dashboard_ready(max_wait_seconds=120):
    """Wait for dashboard to be ready."""
    logger.info("Waiting for dashboard to be ready...")
    start_time = time.time()
    
    while time.time() - start_time < max_wait_seconds:
        if check_dashboard_running():
            logger.info("✅ Dashboard is ready and accessible!")
            return True
        
        elapsed = int(time.time() - start_time)
        logger.info(f"Dashboard not ready yet... waiting ({elapsed}s/{max_wait_seconds}s)")
        time.sleep(5)
    
    logger.error("❌ Dashboard failed to start within timeout period")
    return False

def main():
    """Main function to start dashboard robustly."""
    logger.info("=" * 60)
    logger.info("ROBUST DASHBOARD STARTUP")
    logger.info("=" * 60)
    
    # Step 1: Check if dashboard is already running
    if check_dashboard_running():
        logger.info("✅ Dashboard is already running and accessible!")
        logger.info("Dashboard URL: http://localhost:5000")
        return True
    
    # Step 2: Kill any existing dashboard processes
    logger.info("🔄 Cleaning up any existing dashboard processes...")
    kill_existing_dashboard()
    
    # Step 3: Start dashboard
    logger.info("🚀 Starting dashboard server...")
    process = start_dashboard()
    
    if process is None:
        logger.error("❌ Failed to start dashboard process")
        return False
    
    # Step 4: Wait for dashboard to be ready
    if wait_for_dashboard_ready():
        logger.info("🎉 Dashboard startup completed successfully!")
        logger.info("Dashboard URL: http://localhost:5000")
        logger.info("The dashboard will auto-refresh every 3 minutes")
        logger.info("Press Ctrl+C in the dashboard console to stop")
        return True
    else:
        logger.error("❌ Dashboard startup failed")
        try:
            process.terminate()
        except:
            pass
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        logger.error("Dashboard startup failed. Please check the logs above.")
        sys.exit(1)
    else:
        logger.info("Dashboard startup completed successfully!")
        sys.exit(0)
