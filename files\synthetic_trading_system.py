"""
Main Synthetic Trading System for DEX 900 DOWN Index.
Orchestrates data collection, pattern detection, and trading decisions.
"""

import os
import sys
import time
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd
import numpy as np

import config
from synthetic_data_collector import SyntheticDataCollector
from synthetic_pattern_detector import SyntheticPatternDetector

# Set up logging
logging.basicConfig(
    level=getattr(logging, config.LOGGING_CONFIG["level"]),
    format=config.LOGGING_CONFIG["format"],
    handlers=[
        logging.FileHandler("logs/synthetic_trading_system.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SyntheticTradingSystem")

class SyntheticTradingSystem:
    """
    Main AI Trading System for Deriv's Synthetic DEX 900 DOWN Index.
    Coordinates all components for algorithmic pattern-based trading.
    """
    
    def __init__(self):
        """Initialize the trading system."""
        self.system_name = config.SYSTEM_NAME
        self.version = config.VERSION
        self.start_time = datetime.now()
        
        # System state
        self.is_running = False
        self.is_trading_enabled = False
        self.system_health = "INITIALIZING"
        
        # Initialize components
        self.data_collector = None
        self.pattern_detector = None
        
        # Performance tracking
        self.performance_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'current_drawdown': 0.0,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'profit_factor': 0.0
        }
        
        # Risk monitoring
        self.risk_metrics = {
            'current_exposure': 0.0,
            'daily_pnl': 0.0,
            'positions_count': 0,
            'last_trade_time': None,
            'consecutive_losses': 0,
            'risk_limit_breached': False
        }
        
        logger.info(f"Initializing {self.system_name} v{self.version}")
        
    def initialize_system(self) -> bool:
        """Initialize all system components."""
        try:
            logger.info("Starting system initialization...")
            
            # Create necessary directories
            self.ensure_directories()
            
            # Initialize data collector
            logger.info("Initializing data collector...")
            self.data_collector = SyntheticDataCollector()
            
            if not self.data_collector.mt5_connected:
                logger.error("Failed to connect to MT5. Cannot proceed.")
                return False
                
            # Initialize pattern detector
            logger.info("Initializing pattern detector...")
            self.pattern_detector = SyntheticPatternDetector(self.data_collector)
            
            # Test data collection
            logger.info("Testing data collection...")
            test_data = self.data_collector.get_latest_data(timeframe=1, count=10)
            if test_data.empty:
                logger.warning("No recent data available. Will collect historical data.")
                
            self.system_health = "INITIALIZED"
            logger.info("System initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            self.system_health = "INITIALIZATION_FAILED"
            return False
            
    def ensure_directories(self):
        """Ensure all necessary directories exist."""
        for directory in config.DIRECTORIES:
            os.makedirs(directory, exist_ok=True)
            
    def collect_historical_data(self) -> bool:
        """Collect and analyze historical data for pattern learning."""
        try:
            logger.info("Starting historical data collection...")
            
            # Collect complete historical data
            success = self.data_collector.collect_historical_data()
            
            if not success:
                logger.error("Historical data collection failed")
                return False
                
            # Analyze patterns in historical data
            logger.info("Analyzing historical patterns...")
            self.analyze_historical_patterns()
            
            logger.info("Historical data collection and analysis completed")
            return True
            
        except Exception as e:
            logger.error(f"Historical data collection failed: {e}")
            return False
            
    def analyze_historical_patterns(self):
        """Analyze patterns in historical data to understand algorithmic behavior."""
        try:
            # Analyze each timeframe
            for timeframe_name, timeframe_config in config.SYNTHETIC_TIMEFRAMES.items():
                if timeframe_name == "tick":
                    continue  # Skip tick analysis for now
                    
                intervals = timeframe_config.get("intervals", [])
                for interval in intervals:
                    logger.info(f"Analyzing patterns for {interval}min timeframe")
                    
                    # Get historical data
                    df = self.data_collector.get_latest_data(interval, count=1000)
                    if df.empty:
                        logger.warning(f"No data available for {interval}min analysis")
                        continue
                        
                    # Calculate synthetic indicators
                    df_with_indicators = self.pattern_detector.calculate_synthetic_indicators(df)
                    
                    # Detect jump events
                    jump_events = self.pattern_detector.detect_jump_events(df_with_indicators)
                    
                    logger.info(f"Found {len(jump_events)} jump events in {interval}min data")
                    
                    # Store pattern analysis results
                    self.store_pattern_analysis(interval, df_with_indicators, jump_events)
                    
        except Exception as e:
            logger.error(f"Pattern analysis failed: {e}")
            
    def store_pattern_analysis(self, timeframe: int, df: pd.DataFrame, events: List[Dict]):
        """Store pattern analysis results for future reference."""
        try:
            # Check if DataFrame is empty
            if df.empty:
                logger.debug(f"No data to store for {timeframe}min timeframe")
                return

            # Store indicators in database
            cursor = self.data_collector.conn.cursor()

            for _, row in df.iterrows():
                # Convert and validate all values to prevent binding errors
                try:
                    timestamp_val = row.get('timestamp', 0)
                    if hasattr(timestamp_val, 'timestamp'):
                        timestamp_val = timestamp_val.timestamp()
                    elif pd.isna(timestamp_val):
                        timestamp_val = datetime.now().timestamp()

                    cursor.execute("""
                        INSERT OR REPLACE INTO synthetic_indicators
                        (timestamp, timeframe, jumpiness_score, volatility_compression,
                         price_acceleration, tick_velocity, regime_state, pattern_similarity)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        float(timestamp_val),
                        int(timeframe),
                        float(row.get('jumpiness_score', 0) or 0),
                        float(row.get('volatility_compression', 0) or 0),
                        float(row.get('price_acceleration', 0) or 0),
                        float(row.get('tick_velocity', 0) or 0),
                        int(row.get('regime_state', 0) or 0),
                        0.0  # pattern_similarity placeholder
                    ))
                except Exception as row_error:
                    logger.warning(f"Skipping row due to data conversion error: {row_error}")
                    continue
                
            # Store events
            for event in events:
                # Fix timestamp conversion
                try:
                    if hasattr(event['timestamp'], 'timestamp'):
                        timestamp_value = event['timestamp'].timestamp()
                    elif isinstance(event['timestamp'], (int, float)):
                        timestamp_value = float(event['timestamp'])
                    else:
                        # Convert string or other types to timestamp
                        timestamp_value = pd.to_datetime(event['timestamp']).timestamp()
                except:
                    # Fallback to current time if conversion fails
                    timestamp_value = datetime.now().timestamp()

                cursor.execute("""
                    INSERT OR REPLACE INTO pattern_events
                    (timestamp, event_type, magnitude, duration, pre_pattern, post_pattern, confidence)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    timestamp_value,
                    str(event['event_type']),
                    float(event['magnitude']),
                    int(event['duration']),
                    str(event.get('pre_pattern', [])),
                    str(event.get('post_pattern', [])),
                    1.0  # confidence placeholder
                ))
                
            self.data_collector.conn.commit()
            logger.debug(f"Stored pattern analysis for {timeframe}min timeframe")
            
        except Exception as e:
            logger.error(f"Error storing pattern analysis: {e}")
            
    def start_real_time_monitoring(self):
        """Start real-time data collection and monitoring."""
        try:
            logger.info("Starting real-time monitoring...")
            
            # Start real-time data collection
            self.data_collector.start_real_time_collection()
            
            # Start monitoring thread
            self.is_running = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
            self.monitoring_thread.daemon = True
            self.monitoring_thread.start()
            
            self.system_health = "RUNNING"
            logger.info("Real-time monitoring started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start real-time monitoring: {e}")
            self.system_health = "MONITORING_FAILED"
            
    def _monitoring_loop(self):
        """Main monitoring loop for real-time analysis."""
        logger.info("Monitoring loop started")
        
        while self.is_running:
            try:
                # Get latest data for analysis
                current_data = self.get_current_market_state()
                
                if current_data is not None:
                    # Analyze current patterns
                    analysis_result = self.analyze_current_patterns(current_data)
                    
                    # Log current state
                    self.log_current_state(analysis_result)
                    
                    # Update performance metrics
                    self.update_performance_metrics()
                    
                # Sleep for monitoring interval
                time.sleep(5)  # Monitor every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)  # Wait longer on error
                
        logger.info("Monitoring loop stopped")
        
    def get_current_market_state(self) -> Optional[Dict]:
        """Get current market state for analysis."""
        try:
            # Get latest 1-minute data
            df_1m = self.data_collector.get_latest_data(timeframe=1, count=100)
            if df_1m.empty:
                return None
                
            # Calculate current indicators
            df_with_indicators = self.pattern_detector.calculate_synthetic_indicators(df_1m)
            
            if df_with_indicators.empty:
                return None
                
            # Get latest values
            latest = df_with_indicators.iloc[-1]
            
            current_state = {
                'timestamp': latest.get('timestamp', datetime.now()),
                'price': latest.get('close', 0),
                'regime_state': latest.get('regime_state', 0),
                'jumpiness_score': latest.get('jumpiness_score', 0),
                'volatility_compression': latest.get('volatility_compression', 0),
                'price_acceleration': latest.get('price_acceleration', 0),
                'tick_velocity': latest.get('tick_velocity', 0),
                'mean_reversion_signal': latest.get('mean_reversion_signal', 0),
                'volatility': latest.get('volatility', 0),
                'volume': latest.get('volume', 0)
            }
            
            return current_state
            
        except Exception as e:
            logger.error(f"Error getting current market state: {e}")
            return None
            
    def analyze_current_patterns(self, current_state: Dict) -> Dict:
        """Analyze current patterns and generate trading signals."""
        try:
            analysis = {
                'timestamp': current_state['timestamp'],
                'regime': self.pattern_detector.regimes.get(current_state['regime_state'], 'UNKNOWN'),
                'signals': {},
                'risk_assessment': {},
                'recommendations': []
            }
            
            # Regime-based analysis
            regime_state = current_state['regime_state']
            
            if regime_state == 0:  # QUIET
                analysis['signals']['scalping_opportunity'] = current_state['tick_velocity'] < 0.5
                analysis['recommendations'].append("Consider range trading in quiet period")
                
            elif regime_state == 1:  # PRE_JUMP
                analysis['signals']['jump_imminent'] = current_state['jumpiness_score'] > 0.7
                analysis['recommendations'].append("Prepare for potential volatility event")
                
            elif regime_state == 2:  # JUMPING
                analysis['signals']['momentum_trade'] = current_state['price_acceleration'] > 1.0
                analysis['recommendations'].append("Consider momentum trading with tight stops")
                
            elif regime_state == 3:  # POST_JUMP
                analysis['signals']['reversion_setup'] = current_state['mean_reversion_signal'] > 0.5
                analysis['recommendations'].append("Look for mean reversion opportunities")
                
            elif regime_state == 4:  # REVERTING
                analysis['signals']['reversion_active'] = True
                analysis['recommendations'].append("Monitor reversion completion")
                
            # Risk assessment
            analysis['risk_assessment'] = {
                'volatility_level': 'HIGH' if current_state['volatility'] > 0.01 else 'NORMAL',
                'position_sizing_multiplier': self.calculate_position_sizing_multiplier(current_state),
                'stop_loss_distance': self.calculate_stop_loss_distance(current_state),
                'max_hold_time': self.calculate_max_hold_time(current_state)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing current patterns: {e}")
            return {'error': str(e)}
            
    def calculate_position_sizing_multiplier(self, state: Dict) -> float:
        """Calculate position sizing multiplier based on current conditions."""
        base_multiplier = 1.0
        
        # Reduce size in high volatility
        if state['volatility'] > 0.01:
            base_multiplier *= 0.5
            
        # Reduce size during jumps
        if state['regime_state'] == 2:  # JUMPING
            base_multiplier *= 0.3
            
        # Increase size in quiet periods
        if state['regime_state'] == 0:  # QUIET
            base_multiplier *= 1.0
            
        return max(0.1, min(1.0, base_multiplier))
        
    def calculate_stop_loss_distance(self, state: Dict) -> float:
        """Calculate appropriate stop loss distance."""
        base_distance = state['volatility'] * 2.0  # 2x volatility
        
        # Tighter stops during jumps
        if state['regime_state'] == 2:  # JUMPING
            base_distance *= 0.5
            
        return max(0.0001, base_distance)  # Minimum 1 pip
        
    def calculate_max_hold_time(self, state: Dict) -> int:
        """Calculate maximum hold time in seconds."""
        base_time = 300  # 5 minutes default
        
        # Shorter holds during volatile periods
        if state['volatility'] > 0.01:
            base_time = 120  # 2 minutes
            
        # Very short holds during jumps
        if state['regime_state'] == 2:  # JUMPING
            base_time = 30  # 30 seconds
            
        return base_time
        
    def log_current_state(self, analysis: Dict):
        """Log current system state."""
        if 'error' in analysis:
            logger.error(f"Analysis error: {analysis['error']}")
            return
            
        regime = analysis.get('regime', 'UNKNOWN')
        signals = analysis.get('signals', {})
        
        # Log every minute to avoid spam
        current_minute = datetime.now().minute
        if not hasattr(self, '_last_log_minute') or self._last_log_minute != current_minute:
            logger.info(f"Current regime: {regime}, Active signals: {len(signals)}")
            self._last_log_minute = current_minute
            
    def update_performance_metrics(self):
        """Update system performance metrics."""
        # This will be implemented when trading is active
        # For now, just update system uptime
        uptime = datetime.now() - self.start_time
        self.performance_stats['uptime_hours'] = uptime.total_seconds() / 3600
        
    def stop_system(self):
        """Stop the trading system gracefully."""
        logger.info("Stopping trading system...")
        
        self.is_running = False
        self.is_trading_enabled = False
        
        # Stop real-time collection
        if self.data_collector:
            self.data_collector.stop_real_time_collection()
            
        # Wait for monitoring thread to stop
        if hasattr(self, 'monitoring_thread'):
            self.monitoring_thread.join(timeout=10)
            
        self.system_health = "STOPPED"
        logger.info("Trading system stopped")
        
    def get_system_status(self) -> Dict:
        """Get current system status."""
        return {
            'system_name': self.system_name,
            'version': self.version,
            'health': self.system_health,
            'is_running': self.is_running,
            'is_trading_enabled': self.is_trading_enabled,
            'uptime': (datetime.now() - self.start_time).total_seconds(),
            'performance_stats': self.performance_stats,
            'risk_metrics': self.risk_metrics
        }

def main():
    """Main entry point for the trading system."""
    print(f"Starting {config.SYSTEM_NAME} v{config.VERSION}")
    
    # Create trading system
    trading_system = SyntheticTradingSystem()
    
    try:
        # Initialize system
        if not trading_system.initialize_system():
            print("System initialization failed. Exiting.")
            return
            
        # Collect historical data
        print("Collecting historical data...")
        if not trading_system.collect_historical_data():
            print("Historical data collection failed. Continuing with limited data.")
            
        # Start real-time monitoring
        print("Starting real-time monitoring...")
        trading_system.start_real_time_monitoring()
        
        print("System is running. Press Ctrl+C to stop.")
        
        # Keep system running
        while trading_system.is_running:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        trading_system.stop_system()
        print("System stopped")

if __name__ == "__main__":
    main()
