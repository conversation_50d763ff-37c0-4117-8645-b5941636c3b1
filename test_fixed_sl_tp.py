#!/usr/bin/env python3
"""
Test that dynamic SL/TP and trailing stops have been removed and replaced with fixed values.
"""

import sys
import logging
from datetime import datetime
import pandas as pd

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_fixed_sl_tp_calculation():
    """Test that SL/TP calculation now uses fixed values instead of ATR."""
    try:
        from trading_signal_generator import TradingSignalGenerator, SignalType
        from data_collector import DataCollector
        
        # Create mock data collector
        class MockDataCollector:
            def get_market_data(self, timeframe):
                # Return mock market data
                return pd.DataFrame({
                    'open': [65000, 65100, 65200],
                    'high': [65050, 65150, 65250],
                    'low': [64950, 65050, 65150],
                    'close': [65100, 65200, 65300],
                    'volume': [1000, 1100, 1200]
                })
        
        mock_data_collector = MockDataCollector()
        signal_generator = TradingSignalGenerator(mock_data_collector)
        
        logger.info("=== Fixed SL/TP Calculation Test ===")
        
        # Test different timeframes
        test_scenarios = [
            {'timeframe_group': 'short_term', 'expected_sl': 50.0, 'expected_tp': 100.0},
            {'timeframe_group': 'medium_term', 'expected_sl': 150.0, 'expected_tp': 300.0},
            {'timeframe_group': 'long_term', 'expected_sl': 250.0, 'expected_tp': 500.0},
        ]
        
        current_price = 65000.0
        mock_market_data = pd.DataFrame({
            'high': [65100, 65200, 65300],
            'low': [64900, 65000, 65100],
            'close': [65000, 65100, 65200]
        })
        
        for scenario in test_scenarios:
            logger.info(f"\nTesting {scenario['timeframe_group']} timeframe:")
            
            # Test BUY signal
            sl_buy, tp_buy = signal_generator._calculate_stop_loss_take_profit(
                SignalType.STRONG_BUY, current_price, {}, mock_market_data, scenario['timeframe_group']
            )
            
            expected_sl_buy = current_price - scenario['expected_sl']
            expected_tp_buy = current_price + scenario['expected_tp']
            
            logger.info(f"  BUY - SL: {sl_buy:.2f} (expected: {expected_sl_buy:.2f})")
            logger.info(f"  BUY - TP: {tp_buy:.2f} (expected: {expected_tp_buy:.2f})")
            
            # Check if values match expected fixed distances
            if abs(sl_buy - expected_sl_buy) < 0.01 and abs(tp_buy - expected_tp_buy) < 0.01:
                logger.info(f"  ✅ BUY SL/TP uses fixed distances correctly")
            else:
                logger.error(f"  ❌ BUY SL/TP does not match expected fixed distances")
                return False
            
            # Test SELL signal
            sl_sell, tp_sell = signal_generator._calculate_stop_loss_take_profit(
                SignalType.STRONG_SELL, current_price, {}, mock_market_data, scenario['timeframe_group']
            )
            
            expected_sl_sell = current_price + scenario['expected_sl']
            expected_tp_sell = current_price - scenario['expected_tp']
            
            logger.info(f"  SELL - SL: {sl_sell:.2f} (expected: {expected_sl_sell:.2f})")
            logger.info(f"  SELL - TP: {tp_sell:.2f} (expected: {expected_tp_sell:.2f})")
            
            # Check if values match expected fixed distances
            if abs(sl_sell - expected_sl_sell) < 0.01 and abs(tp_sell - expected_tp_sell) < 0.01:
                logger.info(f"  ✅ SELL SL/TP uses fixed distances correctly")
            else:
                logger.error(f"  ❌ SELL SL/TP does not match expected fixed distances")
                return False
        
        logger.info("\n✅ All fixed SL/TP tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"Error testing fixed SL/TP calculation: {e}")
        return False

def test_no_atr_methods():
    """Test that ATR calculation methods have been removed."""
    try:
        from trading_signal_generator import TradingSignalGenerator
        from data_collector import DataCollector
        
        logger.info("=== ATR Methods Removal Test ===")
        
        # Create mock data collector
        class MockDataCollector:
            pass
        
        mock_data_collector = MockDataCollector()
        signal_generator = TradingSignalGenerator(mock_data_collector)
        
        # Check if ATR method has been removed
        if hasattr(signal_generator, '_calculate_atr'):
            logger.error("❌ _calculate_atr method still exists")
            return False
        else:
            logger.info("✅ _calculate_atr method has been removed")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing ATR methods removal: {e}")
        return False

def test_no_trailing_stop():
    """Test that trailing stop functionality has been removed."""
    try:
        from order_execution_system import OrderExecutionSystem
        
        logger.info("=== Trailing Stop Removal Test ===")
        
        # Create mock data collector
        class MockDataCollector:
            pass
        
        mock_data_collector = MockDataCollector()
        execution_system = OrderExecutionSystem(mock_data_collector)
        
        # Check if trailing stop method has been removed
        if hasattr(execution_system, '_handle_trailing_stop_loss'):
            logger.error("❌ _handle_trailing_stop_loss method still exists")
            return False
        else:
            logger.info("✅ _handle_trailing_stop_loss method has been removed")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing trailing stop removal: {e}")
        return False

def test_fixed_scaling_distances():
    """Test that position scaling now uses fixed distances instead of ATR."""
    try:
        from order_execution_system import OrderExecutionSystem
        from trading_signal_generator import TradingSignal, SignalType
        
        logger.info("=== Fixed Scaling Distances Test ===")
        
        # Create mock data collector
        class MockDataCollector:
            pass
        
        mock_data_collector = MockDataCollector()
        execution_system = OrderExecutionSystem(mock_data_collector)
        
        # Create test signal
        test_signal = TradingSignal(
            signal_type=SignalType.STRONG_BUY,
            confidence=0.8,
            entry_price=65000.0,
            stop_loss=64850.0,
            take_profit=65300.0,
            position_size=0.02,
            risk_reward_ratio=2.0,
            timeframe=5,
            timestamp=datetime.now(),
            ai_predictions={},
            market_regime='NORMAL',
            reasoning='Test signal for fixed scaling'
        )
        
        # Test fixed distance calculation
        distance = execution_system._estimate_fixed_distance(test_signal)
        logger.info(f"Fixed distance for medium_term: {distance}")
        
        # Should return 100.0 for medium_term
        if distance == 100.0:
            logger.info("✅ Fixed distance calculation works correctly")
            return True
        else:
            logger.error(f"❌ Expected 100.0, got {distance}")
            return False
        
    except Exception as e:
        logger.error(f"Error testing fixed scaling distances: {e}")
        return False

def main():
    """Run all tests to verify dynamic features have been removed."""
    logger.info("🔧 DYNAMIC SL/TP AND TRAILING STOP REMOVAL VERIFICATION")
    logger.info("=" * 60)
    
    tests = [
        ("Fixed SL/TP Calculation", test_fixed_sl_tp_calculation),
        ("ATR Methods Removal", test_no_atr_methods),
        ("Trailing Stop Removal", test_no_trailing_stop),
        ("Fixed Scaling Distances", test_fixed_scaling_distances),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"Result: {status}")
        except Exception as e:
            logger.error(f"Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("🎉 Dynamic SL/TP and trailing stop removal verified!")
        logger.info("✅ System now uses fixed SL/TP distances")
        logger.info("✅ ATR-based calculations removed")
        logger.info("✅ Trailing stop functionality removed")
        logger.info("✅ Position scaling uses fixed distances")
    else:
        logger.warning("⚠️ Some dynamic features may still be present")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
