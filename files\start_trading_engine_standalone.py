"""
Standalone Trading Engine Starter for AI Trading System.
This script starts the actual trading engine with signal generation and trade execution.
"""

import sys
import logging
import time
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trading_engine.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("TradingEngineStarter")

def main():
    """Start the trading engine with full functionality."""
    print("🚀 AI TRADING ENGINE STANDALONE STARTER")
    print("=" * 60)
    print("🎯 DEX 900 DOWN Index - 9 Model Ensemble")
    print("🔄 3-minute trading cycles with signal generation")
    print("📈 Real-time trade execution")
    print("=" * 60)
    
    try:
        # Import the trading engine
        from trading_engine import TradingEngine
        
        print("🔧 Initializing Trading Engine...")
        engine = TradingEngine()
        
        # Initialize all components
        print("📚 Loading AI models and components...")
        if not engine.initialize_components():
            print("❌ Failed to initialize trading components")
            print("💡 Make sure:")
            print("   - MT5 is running and logged in")
            print("   - AI models are trained (run train_all_models.py)")
            print("   - All dependencies are installed")
            return False
            
        print("✅ All components initialized successfully!")
        
        # Start the trading system
        print("🚀 Starting automated trading system...")
        if not engine.start_trading():
            print("❌ Failed to start trading system")
            return False
            
        print("🎉 TRADING SYSTEM STARTED SUCCESSFULLY!")
        print("=" * 60)
        print("📊 System Status:")
        print("   🤖 AI Models: Loaded and active")
        print("   🔄 Trading Loop: Running (3-minute cycles)")
        print("   📈 Signal Generation: Active")
        print("   💰 Trade Execution: Ready")
        print("   📊 Real-time Monitoring: Active")
        print("=" * 60)
        print("⚠️  IMPORTANT:")
        print("   - Keep this window open to maintain trading")
        print("   - Press Ctrl+C to stop the system")
        print("   - Monitor the dashboard at http://localhost:5000")
        print("=" * 60)
        
        # Keep the system running
        try:
            while engine.running:
                time.sleep(60)  # Check every minute
                
                # Log status every 10 minutes
                if hasattr(engine, 'cycle_count') and engine.cycle_count % 3 == 0:
                    status = engine.get_system_status()
                    print(f"📊 Status: {engine.cycle_count} cycles, "
                          f"{status['trading_stats']['signals_generated']} signals, "
                          f"{status['trading_stats']['trades_executed']} trades")
                    
        except KeyboardInterrupt:
            print("\n🛑 Shutdown requested by user")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all required modules are installed")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        logger.error(f"Trading engine error: {e}")
        return False
        
    finally:
        # Clean shutdown
        try:
            if 'engine' in locals():
                print("🧹 Stopping trading engine...")
                engine.stop_trading("User requested shutdown")
                print("✅ Trading engine stopped cleanly")
        except:
            pass
            
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Trading engine failed to start")
        print("📋 Check the logs for detailed error information:")
        print("   - logs/trading_engine.log")
        print("   - logs/synthetic_trading_system.log")
        input("Press Enter to exit...")
    else:
        print("✅ Trading engine shutdown complete")
