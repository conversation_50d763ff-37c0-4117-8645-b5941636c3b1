#!/usr/bin/env python3
"""
Test script to open and close a real trade with minimum SL/TP distances.
This verifies the complete trade lifecycle with DEX 900 DOWN Index requirements.
"""

import sys
import logging
import time
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_minimum_sl_tp_trade():
    """Test opening and closing a trade with minimum SL/TP distances."""
    try:
        print("🧪 TESTING MINIMUM SL/TP TRADE EXECUTION")
        print("=" * 60)
        print("⚠️  This will open and close a REAL trade with minimum SL/TP!")
        print("⚠️  Make sure you're using a DEMO account!")
        print("=" * 60)
        
        # Import required modules
        from trading_signal_generator import TradingSignal, SignalType
        from order_execution_system import OrderExecutionSystem
        from synthetic_data_collector import SyntheticDataCollector
        
        print("📊 Initializing trading components...")
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Components initialized")
        
        # Check MT5 connection
        print("🔌 Checking MT5 connection...")
        if not order_executor.mt5_connected:
            print("❌ MT5 not connected! Please ensure MT5 is running and logged in.")
            return False
            
        print("✅ MT5 connected successfully")
        
        # Get current price and symbol info
        print("💰 Getting market information...")
        import MetaTrader5 as mt5
        
        # Get current price
        tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
        if tick is None:
            print("❌ Could not get current price!")
            return False
        current_price = (tick.bid + tick.ask) / 2
        
        # Get symbol info for minimum distance
        symbol_info = mt5.symbol_info("DEX 900 DOWN Index")
        if symbol_info is None:
            print("❌ Could not get symbol information!")
            return False
            
        print(f"✅ Current Price: {current_price:.2f}")
        print(f"✅ Minimum Distance Required: {symbol_info.trade_stops_level} points")
        print(f"✅ Point Value: {symbol_info.point}")
        
        # Calculate minimum SL/TP distances
        MIN_DISTANCE_POINTS = symbol_info.trade_stops_level
        MIN_DISTANCE_PRICE = MIN_DISTANCE_POINTS * symbol_info.point
        
        print(f"✅ Minimum SL/TP Distance: {MIN_DISTANCE_PRICE:.2f} price units")
        
        # Check account info
        print("👤 Checking account information...")
        account_info = mt5.account_info()
        if not account_info:
            print("❌ Could not get account information!")
            return False
            
        print(f"✅ Account Balance: ${account_info.balance:.2f}")
        print(f"✅ Account Equity: ${account_info.equity:.2f}")
        print(f"✅ Free Margin: ${account_info.margin_free:.2f}")
        
        if not account_info.trade_allowed:
            print("❌ Trading not allowed on this account!")
            return False
            
        print("✅ Trading is allowed")
        
        # Create test signal with EXACT minimum SL/TP distances
        print(f"\n🎯 Creating test signal with MINIMUM SL/TP distances...")
        
        # Use exactly the minimum required distance (no extra buffer)
        min_sl = current_price - MIN_DISTANCE_PRICE
        min_tp = current_price + MIN_DISTANCE_PRICE
        
        from datetime import datetime as dt

        test_signal = TradingSignal(
            signal_type=SignalType.WEAK_BUY,
            confidence=0.6,
            entry_price=current_price,
            stop_loss=min_sl,
            take_profit=min_tp,
            position_size=0.01,  # Minimum position size
            risk_reward_ratio=1.0,  # 1:1 since we're using minimum distances
            timeframe=15,
            timestamp=dt.now(),
            ai_predictions={"test": 0.6},
            market_regime="TEST",
            reasoning="Test signal with exact minimum SL/TP distances"
        )
        
        print(f"✅ Test Signal Created:")
        print(f"   Type: {test_signal.signal_type.name}")
        print(f"   Entry Price: {test_signal.entry_price:.2f}")
        print(f"   Stop Loss: {test_signal.stop_loss:.2f} (distance: {abs(current_price - test_signal.stop_loss):.2f})")
        print(f"   Take Profit: {test_signal.take_profit:.2f} (distance: {abs(test_signal.take_profit - current_price):.2f})")
        print(f"   Position Size: {test_signal.position_size} lots")
        print(f"   Required Min: {MIN_DISTANCE_PRICE:.2f}")
        
        # Verify distances meet requirements
        sl_distance = abs(current_price - test_signal.stop_loss)
        tp_distance = abs(test_signal.take_profit - current_price)
        
        if sl_distance >= MIN_DISTANCE_PRICE and tp_distance >= MIN_DISTANCE_PRICE:
            print("✅ SL/TP distances meet minimum requirements")
        else:
            print(f"❌ SL/TP distances below minimum!")
            print(f"   SL Distance: {sl_distance:.2f} (required: {MIN_DISTANCE_PRICE:.2f})")
            print(f"   TP Distance: {tp_distance:.2f} (required: {MIN_DISTANCE_PRICE:.2f})")
            return False
        
        # Step 1: Open the position
        print(f"\n🚀 STEP 1: OPENING POSITION WITH MINIMUM SL/TP...")
        print("🔄 Executing trade...")
        
        order = order_executor.execute_signal(test_signal)
        
        if order is None:
            print("❌ Trade execution failed - no order returned!")
            return False
            
        if order.status.name != "FILLED":
            print(f"❌ Trade execution failed - order status: {order.status.name}")
            print(f"   Order details: {order}")
            return False
            
        print("🎉 POSITION OPENED SUCCESSFULLY!")
        print(f"✅ Order ID: {order.order_id}")
        print(f"✅ Fill Price: {order.fill_price:.2f}")
        print(f"✅ Volume: {order.volume} lots")
        print(f"✅ Status: {order.status.name}")
        
        # Wait for position to be registered
        print("\n⏳ Waiting 3 seconds for position registration...")
        time.sleep(3)
        
        # Verify position exists
        print("🔍 Verifying position exists...")
        positions = mt5.positions_get(symbol="DEX 900 DOWN Index")
        
        if not positions:
            print("❌ No positions found in MT5!")
            return False
            
        # Find our position
        our_position = None
        for pos in positions:
            if pos.ticket == order.order_id:
                our_position = pos
                break
                
        if our_position is None:
            print(f"⚠️  Position {order.order_id} not found, using first available position")
            our_position = positions[0]
            position_id = our_position.ticket
        else:
            position_id = order.order_id
            
        print(f"✅ Position found: {position_id}")
        print(f"✅ Position Type: {'BUY' if our_position.type == 0 else 'SELL'}")
        print(f"✅ Position Volume: {our_position.volume}")
        print(f"✅ Position Open Price: {our_position.price_open:.2f}")
        print(f"✅ Position Current Price: {our_position.price_current:.2f}")
        print(f"✅ Position Profit: ${our_position.profit:.2f}")
        
        # Check if SL/TP were set correctly
        if our_position.sl > 0:
            actual_sl_distance = abs(our_position.price_open - our_position.sl)
            print(f"✅ Stop Loss Set: {our_position.sl:.2f} (distance: {actual_sl_distance:.2f})")
        else:
            print("⚠️  Stop Loss not set")
            
        if our_position.tp > 0:
            actual_tp_distance = abs(our_position.tp - our_position.price_open)
            print(f"✅ Take Profit Set: {our_position.tp:.2f} (distance: {actual_tp_distance:.2f})")
        else:
            print("⚠️  Take Profit not set")
        
        # Step 2: Close the position
        print(f"\n🚀 STEP 2: CLOSING POSITION {position_id}...")
        print("🔄 Attempting to close position...")
        
        close_success = order_executor.close_position(position_id, "Minimum_SL_TP_Test_Complete")
        
        if close_success:
            print("🎉 POSITION CLOSED SUCCESSFULLY!")
            print(f"✅ Position {position_id} closed")
            
            # Wait and verify closure
            print("⏳ Waiting 2 seconds to verify closure...")
            time.sleep(2)
            
            # Check if position still exists
            positions_after = mt5.positions_get(symbol="DEX 900 DOWN Index")
            if positions_after:
                remaining_ids = [p.ticket for p in positions_after]
                if position_id in remaining_ids:
                    print("⚠️  Warning: Position still appears in MT5")
                else:
                    print("✅ Position successfully removed from MT5")
            else:
                print("✅ No positions remaining - closure confirmed")
                
            # Get final P&L from history
            print("📊 Getting final trade results...")
            from datetime import datetime, timedelta
            
            # Get deals from the last few minutes
            deals = mt5.history_deals_get(
                datetime.now() - timedelta(minutes=5),
                datetime.now()
            )
            
            if deals:
                # Find our deals
                our_deals = [deal for deal in deals if deal.position_id == position_id]
                if our_deals:
                    total_profit = sum(deal.profit for deal in our_deals)
                    print(f"✅ Final P&L: ${total_profit:.2f}")
                    
                    for deal in our_deals:
                        deal_type = "ENTRY" if deal.entry == 1 else "EXIT"
                        print(f"   {deal_type}: {deal.price:.2f} at {deal.time}")
                else:
                    print("⚠️  Could not find deal history")
            else:
                print("⚠️  No recent deals found")
                
            return True
            
        else:
            print("❌ POSITION CLOSING FAILED!")
            print(f"   Could not close position {position_id}")
            print("   Please manually close the position in MT5")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 AI TRADING SYSTEM - MINIMUM SL/TP TRADE TEST")
    print("=" * 60)
    print("⚠️  IMPORTANT: This will place a REAL trade!")
    print("⚠️  Make sure you're using a DEMO account!")
    print("⚠️  This test uses MINIMUM SL/TP distances!")
    print("=" * 60)
    
    # Ask for confirmation
    print("\n🔔 CONFIRMATION REQUIRED:")
    print("   This test will:")
    print("   1. Open a BUY position with minimum SL/TP distances")
    print("   2. Verify the position is created correctly")
    print("   3. Close the position immediately")
    print("   4. Verify complete trade lifecycle")
    print("\n   Are you ready to proceed? (This will place a real trade)")
    
    # For automated testing, we'll proceed directly
    print("🚀 Proceeding with minimum SL/TP trade test...")
    
    success = test_minimum_sl_tp_trade()
    
    if success:
        print("\n🎉 MINIMUM SL/TP TRADE TEST PASSED!")
        print("✅ System can open trades with minimum SL/TP distances!")
        print("✅ System can close positions successfully!")
        print("✅ Complete trade lifecycle verified with minimum requirements!")
    else:
        print("\n❌ MINIMUM SL/TP TRADE TEST FAILED!")
        print("🔧 Please check the error messages above.")
        
    sys.exit(0 if success else 1)
