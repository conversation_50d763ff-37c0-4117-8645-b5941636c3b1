#!/usr/bin/env python3
"""
Test that the imblearn package is properly installed and working.
"""

import sys

def test_imblearn_import():
    """Test that imblearn can be imported and used."""
    print("🧪 TESTING IMBLEARN PACKAGE")
    print("=" * 40)
    
    try:
        # Test basic import
        print("   Testing basic import...")
        from imblearn.over_sampling import SMOTE
        print("   ✅ SMOTE imported successfully")
        
        # Test sklearn utils import
        print("   Testing sklearn utils...")
        from sklearn.utils.class_weight import compute_class_weight
        print("   ✅ compute_class_weight imported successfully")
        
        # Test basic functionality
        print("   Testing basic SMOTE functionality...")
        import numpy as np
        
        # Create simple test data
        X = np.array([[1, 2], [2, 3], [3, 4], [4, 5], [5, 6]])
        y = np.array([0, 0, 0, 1, 1])
        
        smote = SMOTE(random_state=42, k_neighbors=1)
        X_resampled, y_resampled = smote.fit_resample(X, y)
        
        print(f"   ✅ SMOTE test successful:")
        print(f"      Original: {len(X)} samples")
        print(f"      Resampled: {len(X_resampled)} samples")
        print(f"      Original classes: {np.bincount(y)}")
        print(f"      Resampled classes: {np.bincount(y_resampled)}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Functionality error: {e}")
        return False

def test_ai_model_manager_import():
    """Test that AI Model Manager can import imblearn properly."""
    print(f"\n🧠 TESTING AI MODEL MANAGER IMPORTS")
    print("=" * 50)
    
    try:
        print("   Testing AI Model Manager import...")
        from ai_model_manager import AIModelManager
        print("   ✅ AI Model Manager imported successfully")
        
        # Test the specific import that was failing
        print("   Testing class imbalance handling imports...")
        from sklearn.utils.class_weight import compute_class_weight
        from imblearn.over_sampling import SMOTE
        print("   ✅ All class imbalance imports successful")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Main test function."""
    print("🔧 IMBLEARN PACKAGE VERIFICATION")
    print("=" * 60)
    
    # Test basic imblearn functionality
    imblearn_test = test_imblearn_import()
    
    # Test AI Model Manager imports
    ai_manager_test = test_ai_model_manager_import()
    
    print(f"\n🎯 TEST RESULTS:")
    print("=" * 30)
    
    if imblearn_test and ai_manager_test:
        print("🎉 ALL TESTS PASSED!")
        print("\n💡 WHAT THIS MEANS:")
        print("✅ imblearn package is properly installed")
        print("✅ SMOTE class balancing will work")
        print("✅ AI Model Manager can handle class imbalance")
        print("✅ Short-term model retraining should work without errors")
        
        print(f"\n📋 NEXT STEPS:")
        print("1. Run the short-term model retraining script again")
        print("2. The 'No module named imblearn' error should be resolved")
        print("3. Models should train successfully with proper class balancing")
        
        return True
    else:
        print("❌ SOME TESTS FAILED")
        print("The imblearn package may need to be reinstalled or there are other issues")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
