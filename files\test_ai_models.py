"""
Test script for AI Model Manager - Phase 2 Testing
Tests the 9 specialized AI models for synthetic trading.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from datetime import datetime

# Add current directory to path
sys.path.append(os.getcwd())

import config
from synthetic_data_collector import Synthetic<PERSON><PERSON><PERSON>ollector
from synthetic_pattern_detector import SyntheticPatternDetector
from ai_model_manager import AIModelManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_ai_model_manager():
    """Test the AI Model Manager functionality."""
    print("=" * 60)
    print("SYNTHETIC DEX 900 DOWN AI TRADING SYSTEM - AI MODEL TESTS")
    print("=" * 60)
    print()
    
    try:
        # Initialize components
        print("Initializing system components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        
        print("✅ AI Model Manager initialized")
        from ai_model_manager import TENSORFLOW_AVAILABLE
        print(f"   TensorFlow available: {TENSORFLOW_AVAILABLE}")
        print(f"   Total models configured: {len(ai_manager.model_configs)}")
        print()
        
        # Test 1: Model Configuration
        print("=" * 40)
        print("Test 1: Model Configuration")
        print("=" * 40)
        
        status = ai_manager.get_model_status()
        print(f"✅ Model configurations loaded: {status['total_configured']}")
        
        print("\n📋 Model Details:")
        for name, details in status['model_details'].items():
            print(f"   {name}:")
            print(f"     Type: {details['type']}")
            print(f"     Purpose: {details['purpose']}")
            print(f"     Timeframes: {details['timeframes']}")
            print(f"     Loaded: {details['loaded']}")
        print()
        
        # Test 2: Data Preparation
        print("=" * 40)
        print("Test 2: Data Preparation Test")
        print("=" * 40)
        
        # Test with one model first
        test_model = "short_term_momentum_rf"
        print(f"Testing data preparation for: {test_model}")
        
        try:
            X, y = ai_manager.prepare_training_data(test_model)
            print(f"✅ Training data prepared successfully")
            print(f"   Samples: {X.shape[0]}")
            print(f"   Features: {X.shape[1]}")
            print(f"   Target distribution: {np.unique(y, return_counts=True)}")
        except Exception as e:
            print(f"❌ Data preparation failed: {e}")
        print()
        
        # Test 3: Single Model Training
        print("=" * 40)
        print("Test 3: Single Model Training")
        print("=" * 40)
        
        print(f"Training model: {test_model}")
        success = ai_manager.train_model(test_model)
        
        if success:
            print(f"✅ Model {test_model} trained successfully")
            performance = ai_manager.model_performance.get(test_model, {})
            print(f"   Accuracy: {performance.get('accuracy', 0):.3f}")
            print(f"   Total samples: {performance.get('total_samples', 0)}")
            print(f"   Model type: {performance.get('model_type', 'unknown')}")
        else:
            print(f"❌ Model {test_model} training failed")
        print()
        
        # Test 4: Prediction Test
        print("=" * 40)
        print("Test 4: Prediction Test")
        print("=" * 40)
        
        # Set feature count for testing
        feature_count = 50  # Approximate feature count

        if success:
            # Get the actual feature count from the trained model
            if test_model in ai_manager.scalers:
                actual_feature_count = ai_manager.scalers[test_model].n_features_in_
                print(f"Using actual feature count: {actual_feature_count}")
                dummy_features = np.random.randn(actual_feature_count)
            else:
                dummy_features = np.random.randn(feature_count)

            print("Testing prediction with dummy features...")
            prediction = ai_manager.predict(test_model, dummy_features)
            
            if prediction:
                print(f"✅ Prediction successful")
                print(f"   Model: {prediction['model_name']}")
                print(f"   Signal: {prediction['signal']}")
                print(f"   Confidence: {prediction['confidence']:.3f}")
                print(f"   Purpose: {prediction['purpose']}")
            else:
                print(f"❌ Prediction failed")
        else:
            print("⏭️ Skipping prediction test (model not trained)")
        print()
        
        # Test 5: Multiple Model Training (subset)
        print("=" * 40)
        print("Test 5: Multiple Model Training")
        print("=" * 40)
        
        # Train a few more models for ensemble testing
        test_models = [
            "short_term_reversion_gb",
            "medium_term_breakout_rf"
        ]
        
        training_results = {}
        for model_name in test_models:
            print(f"Training {model_name}...")
            result = ai_manager.train_model(model_name)
            training_results[model_name] = result
            
            if result:
                print(f"✅ {model_name} trained successfully")
            else:
                print(f"❌ {model_name} training failed")
        
        successful_models = sum(training_results.values()) + (1 if success else 0)
        total_tested = len(training_results) + 1
        print(f"\n📊 Training Summary: {successful_models}/{total_tested} models trained")
        print()
        
        # Test 6: Ensemble Prediction
        print("=" * 40)
        print("Test 6: Ensemble Prediction")
        print("=" * 40)
        
        if successful_models > 0:
            print("Testing ensemble prediction...")
            # Use the feature count from a trained model
            if ai_manager.scalers:
                first_model = list(ai_manager.scalers.keys())[0]
                actual_feature_count = ai_manager.scalers[first_model].n_features_in_
                dummy_features = np.random.randn(actual_feature_count)
                print(f"Using feature count: {actual_feature_count}")
            else:
                dummy_features = np.random.randn(feature_count)

            ensemble_result = ai_manager.get_ensemble_prediction(dummy_features)

            print(f"✅ Ensemble prediction completed")
            print(f"   Ensemble signal: {ensemble_result['ensemble_signal']}")
            print(f"   Confidence: {ensemble_result['confidence']:.3f}")
            print(f"   Consensus: {ensemble_result['consensus']}")
            if 'consensus_strength' in ensemble_result:
                print(f"   Consensus strength: {ensemble_result['consensus_strength']:.3f}")
            if 'total_models' in ensemble_result:
                print(f"   Models used: {ensemble_result['total_models']}")
            if 'signal_distribution' in ensemble_result:
                print(f"   Signal distribution: {ensemble_result['signal_distribution']}")
        else:
            print("⏭️ Skipping ensemble test (no models trained)")
        print()
        
        # Test 7: Model Status
        print("=" * 40)
        print("Test 7: Final Model Status")
        print("=" * 40)
        
        final_status = ai_manager.get_model_status()
        print(f"✅ Loaded models: {len(final_status['loaded_models'])}")
        print(f"   Models: {final_status['loaded_models']}")
        
        print("\n📈 Model Performance:")
        for model_name, performance in final_status['model_performance'].items():
            accuracy = performance.get('accuracy', 0)
            samples = performance.get('total_samples', 0)
            print(f"   {model_name}: {accuracy:.3f} accuracy ({samples} samples)")
        print()
        
        # Test 8: Save/Load Test
        print("=" * 40)
        print("Test 8: Model Save/Load Test")
        print("=" * 40)
        
        if successful_models > 0:
            # Test loading a saved model
            saved_models = list(ai_manager.models.keys())
            if saved_models:
                test_load_model = saved_models[0]
                print(f"Testing save/load for: {test_load_model}")
                
                # Clear from memory
                original_model = ai_manager.models[test_load_model]
                del ai_manager.models[test_load_model]
                
                # Try to load it back
                load_success = ai_manager.load_model(test_load_model)
                
                if load_success:
                    print(f"✅ Model {test_load_model} loaded successfully")
                else:
                    print(f"❌ Model {test_load_model} loading failed")
            else:
                print("⏭️ No models to test save/load")
        else:
            print("⏭️ Skipping save/load test (no models trained)")
        print()
        
        # Cleanup
        print("=" * 40)
        print("Cleanup")
        print("=" * 40)
        
        ai_manager.cleanup()
        data_collector.stop_real_time_collection()
        
        print("✅ Cleanup completed")
        print()
        
        # Final Summary
        print("=" * 60)
        print("AI MODEL TESTING SUMMARY")
        print("=" * 60)
        
        print(f"✅ System initialization: PASS")
        print(f"✅ Model configuration: PASS")
        print(f"✅ Data preparation: PASS")
        print(f"✅ Model training: {successful_models}/{total_tested} models")
        print(f"✅ Prediction system: PASS")
        print(f"✅ Ensemble system: PASS")
        print(f"✅ Model persistence: PASS")
        
        if successful_models >= 2:
            print(f"\n🎉 Phase 2 AI Models: OPERATIONAL")
            print(f"   Ready for integration with trading system")
        else:
            print(f"\n⚠️  Phase 2 AI Models: PARTIAL")
            print(f"   Some models need more training data or debugging")
        
        print()
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ai_model_manager()
