#!/usr/bin/env python3
"""Check when models were last trained."""

import os
from datetime import datetime

def check_model_age():
    """Check the age of trained models."""
    model_dir = 'models/saved'
    
    if not os.path.exists(model_dir):
        print("❌ No models directory found!")
        return
    
    print('🤖 MODEL TRAINING STATUS')
    print('=' * 50)
    
    models = [d for d in os.listdir(model_dir) if os.path.isdir(os.path.join(model_dir, d))]
    
    if not models:
        print("❌ No trained models found!")
        return
    
    for model_name in sorted(models):
        model_path = os.path.join(model_dir, model_name)
        
        # Check for model files
        files = ['model.pkl', 'model.h5', 'model.keras', 'scaler.pkl', 'label_encoder.pkl']
        existing_files = []
        newest_time = 0
        
        for file in files:
            file_path = os.path.join(model_path, file)
            if os.path.exists(file_path):
                existing_files.append(file)
                mtime = os.path.getmtime(file_path)
                if mtime > newest_time:
                    newest_time = mtime
        
        if newest_time > 0:
            last_modified = datetime.fromtimestamp(newest_time)
            age_days = (datetime.now() - last_modified).days
            age_hours = (datetime.now() - last_modified).total_seconds() / 3600
            
            print(f'📁 {model_name}:')
            print(f'   📅 Last trained: {last_modified.strftime("%Y-%m-%d %H:%M:%S")}')
            if age_days > 0:
                print(f'   ⏰ Age: {age_days} days')
            else:
                print(f'   ⏰ Age: {age_hours:.1f} hours')
            print(f'   📄 Files: {len(existing_files)} files')
            
            # Check if retraining is needed
            if age_days >= 7:
                print(f'   ⚠️  NEEDS RETRAINING (>7 days old)')
            elif age_days >= 3:
                print(f'   🔄 Consider retraining (>3 days old)')
            else:
                print(f'   ✅ Fresh model')
            print()
        else:
            print(f'❌ {model_name}: No model files found!')

if __name__ == "__main__":
    check_model_age()
