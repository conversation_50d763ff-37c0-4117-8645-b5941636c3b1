#!/usr/bin/env python3
"""
Memory Monitoring System for AI Trading System
Monitors RAM usage and triggers cleanup when threshold is exceeded.
"""

import os
import sys
import time
import psutil
import logging
from datetime import datetime, timedelta
from threading import Thread, Event

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/memory_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('MemoryMonitor')

class MemoryMonitorSystem:
    """Real-time memory monitoring with automatic cleanup triggers."""
    
    def __init__(self, threshold_percent=80, check_interval=300):
        """
        Initialize memory monitor.
        
        Args:
            threshold_percent: RAM usage percentage to trigger cleanup (default: 80%)
            check_interval: Check interval in seconds (default: 300 = 5 minutes)
        """
        self.threshold_percent = threshold_percent
        self.check_interval = check_interval
        self.monitoring = False
        self.stop_event = Event()
        self.monitor_thread = None
        
        self.stats = {
            'checks_performed': 0,
            'threshold_breaches': 0,
            'cleanups_triggered': 0,
            'max_memory_seen': 0,
            'avg_memory_usage': 0,
            'start_time': None
        }
    
    def get_memory_info(self) -> dict:
        """Get comprehensive memory information."""
        try:
            # System memory
            memory = psutil.virtual_memory()
            
            # Current process
            process = psutil.Process()
            proc_memory = process.memory_info()
            
            # Python processes
            python_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
                try:
                    if 'python' in proc.info['name'].lower():
                        memory_mb = proc.info['memory_info'].rss / (1024**2)
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'memory_mb': memory_mb
                        })
                except:
                    pass
            
            return {
                'system_total_gb': memory.total / (1024**3),
                'system_available_gb': memory.available / (1024**3),
                'system_used_gb': memory.used / (1024**3),
                'system_percent': memory.percent,
                'process_rss_mb': proc_memory.rss / (1024**2),
                'process_vms_mb': proc_memory.vms / (1024**2),
                'python_processes': python_processes,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Error getting memory info: {e}")
            return {}
    
    def check_memory_threshold(self) -> bool:
        """Check if memory usage exceeds threshold."""
        memory_info = self.get_memory_info()
        
        if not memory_info:
            return False
        
        current_percent = memory_info['system_percent']
        
        # Update statistics
        self.stats['checks_performed'] += 1
        self.stats['max_memory_seen'] = max(self.stats['max_memory_seen'], current_percent)
        
        # Calculate running average
        if self.stats['checks_performed'] == 1:
            self.stats['avg_memory_usage'] = current_percent
        else:
            # Simple moving average
            self.stats['avg_memory_usage'] = (
                (self.stats['avg_memory_usage'] * (self.stats['checks_performed'] - 1) + current_percent) 
                / self.stats['checks_performed']
            )
        
        # Check threshold
        if current_percent >= self.threshold_percent:
            self.stats['threshold_breaches'] += 1
            
            logger.warning(f"🚨 MEMORY THRESHOLD EXCEEDED!")
            logger.warning(f"   Current usage: {current_percent:.1f}%")
            logger.warning(f"   Threshold: {self.threshold_percent}%")
            logger.warning(f"   Available: {memory_info['system_available_gb']:.2f} GB")
            
            return True
        else:
            logger.info(f"📊 Memory check: {current_percent:.1f}% (OK)")
            return False
    
    def trigger_emergency_cleanup(self) -> bool:
        """Trigger emergency cleanup when memory threshold is exceeded."""
        logger.warning("🧹 TRIGGERING EMERGENCY CLEANUP")
        
        try:
            # Import cleanup system
            from daily_cache_cleanup_system import DailyCacheCleanupSystem
            
            # Run emergency cleanup
            cleanup_system = DailyCacheCleanupSystem()
            
            # Run lighter cleanup for emergency
            success = True
            
            # Clear temporary caches
            if not cleanup_system.clear_temporary_caches():
                success = False
            
            # Optimize memory
            if not cleanup_system.optimize_memory():
                success = False
            
            if success:
                logger.info("✅ Emergency cleanup completed successfully")
                self.stats['cleanups_triggered'] += 1
            else:
                logger.error("❌ Emergency cleanup failed")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error during emergency cleanup: {e}")
            return False
    
    def monitor_loop(self):
        """Main monitoring loop."""
        logger.info("🔍 Memory monitoring loop started")
        
        while not self.stop_event.is_set():
            try:
                # Check memory threshold
                if self.check_memory_threshold():
                    # Trigger emergency cleanup
                    self.trigger_emergency_cleanup()
                    
                    # Wait longer after cleanup
                    wait_time = self.check_interval * 2
                    logger.info(f"   Waiting {wait_time} seconds after cleanup...")
                else:
                    wait_time = self.check_interval
                
                # Wait for next check (or stop signal)
                if self.stop_event.wait(wait_time):
                    break
                    
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                # Wait before retrying
                if self.stop_event.wait(60):
                    break
        
        logger.info("🔍 Memory monitoring loop stopped")
    
    def start_monitoring(self):
        """Start memory monitoring in background thread."""
        if self.monitoring:
            logger.warning("Memory monitoring already running")
            return
        
        logger.info("🚀 STARTING MEMORY MONITORING SYSTEM")
        logger.info(f"   Threshold: {self.threshold_percent}%")
        logger.info(f"   Check interval: {self.check_interval} seconds")
        
        self.monitoring = True
        self.stats['start_time'] = datetime.now()
        self.stop_event.clear()
        
        # Start monitoring thread
        self.monitor_thread = Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("✅ Memory monitoring started successfully")
    
    def stop_monitoring(self):
        """Stop memory monitoring."""
        if not self.monitoring:
            logger.warning("Memory monitoring not running")
            return
        
        logger.info("🛑 Stopping memory monitoring...")
        
        self.monitoring = False
        self.stop_event.set()
        
        # Wait for thread to finish
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        logger.info("✅ Memory monitoring stopped")
    
    def get_monitoring_stats(self) -> dict:
        """Get monitoring statistics."""
        if self.stats['start_time']:
            runtime = datetime.now() - self.stats['start_time']
            runtime_hours = runtime.total_seconds() / 3600
        else:
            runtime_hours = 0
        
        return {
            **self.stats,
            'runtime_hours': runtime_hours,
            'threshold_percent': self.threshold_percent,
            'check_interval': self.check_interval,
            'currently_monitoring': self.monitoring
        }
    
    def generate_monitoring_report(self) -> str:
        """Generate monitoring report."""
        stats = self.get_monitoring_stats()
        memory_info = self.get_memory_info()
        
        report = f"""
🧠 MEMORY MONITORING REPORT
{'='*50}
Report Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Monitoring Status: {'🟢 ACTIVE' if self.monitoring else '🔴 STOPPED'}

📊 MONITORING STATISTICS:
- Runtime: {stats['runtime_hours']:.1f} hours
- Checks performed: {stats['checks_performed']:,}
- Threshold breaches: {stats['threshold_breaches']}
- Emergency cleanups: {stats['cleanups_triggered']}
- Max memory seen: {stats['max_memory_seen']:.1f}%
- Average memory: {stats['avg_memory_usage']:.1f}%

💾 CURRENT MEMORY STATUS:
- System total: {memory_info.get('system_total_gb', 0):.2f} GB
- System used: {memory_info.get('system_used_gb', 0):.2f} GB ({memory_info.get('system_percent', 0):.1f}%)
- System available: {memory_info.get('system_available_gb', 0):.2f} GB
- Process memory: {memory_info.get('process_rss_mb', 0):.2f} MB

⚙️ CONFIGURATION:
- Threshold: {self.threshold_percent}%
- Check interval: {self.check_interval} seconds
- Emergency cleanup: {'Enabled' if self.threshold_percent < 100 else 'Disabled'}
"""
        
        return report

def main():
    """Main memory monitor execution."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Memory Monitoring System')
    parser.add_argument('--threshold', type=int, default=80, help='Memory threshold percentage (default: 80)')
    parser.add_argument('--interval', type=int, default=300, help='Check interval in seconds (default: 300)')
    parser.add_argument('--test', action='store_true', help='Run single memory check and exit')
    parser.add_argument('--report', action='store_true', help='Generate monitoring report')
    
    args = parser.parse_args()
    
    monitor = MemoryMonitorSystem(args.threshold, args.interval)
    
    if args.test:
        # Single memory check
        logger.info("🧪 Running single memory check...")
        memory_info = monitor.get_memory_info()
        
        if memory_info:
            logger.info(f"📊 Current memory usage: {memory_info['system_percent']:.1f}%")
            logger.info(f"📊 Available memory: {memory_info['system_available_gb']:.2f} GB")
            logger.info(f"📊 Process memory: {memory_info['process_rss_mb']:.2f} MB")
            
            if memory_info['system_percent'] >= args.threshold:
                logger.warning(f"⚠️  Memory usage exceeds {args.threshold}% threshold!")
            else:
                logger.info(f"✅ Memory usage within {args.threshold}% threshold")
        
        return True
    
    elif args.report:
        # Generate report
        report = monitor.generate_monitoring_report()
        print(report)
        
        # Save report
        report_file = f"logs/memory_reports/memory_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w') as f:
            f.write(report)
        
        logger.info(f"📋 Report saved to {report_file}")
        return True
    
    else:
        # Start continuous monitoring
        try:
            monitor.start_monitoring()
            
            # Keep running until interrupted
            while True:
                time.sleep(60)
                
        except KeyboardInterrupt:
            logger.info("🛑 Monitoring interrupted by user")
            monitor.stop_monitoring()
            return True
        except Exception as e:
            logger.error(f"❌ Error in memory monitoring: {e}")
            monitor.stop_monitoring()
            return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
