#!/usr/bin/env python3
"""
Diagnose why the trading engine is stopping unexpectedly.
Check all possible shutdown triggers.
"""

import sys
import time

def diagnose_shutdown_causes():
    """Diagnose potential causes for trading engine shutdown."""
    print("🔍 DIAGNOSING TRADING ENGINE SHUTDOWN")
    print("=" * 60)
    
    try:
        # Import components
        from synthetic_data_collector import SyntheticDataCollector
        from order_execution_system import OrderExecutionSystem
        import config
        
        print("✅ Components imported successfully")
        
        # Initialize order executor to check current state
        print("\n🔧 Initializing Order Executor...")
        order_executor = OrderExecutionSystem(SyntheticDataCollector())
        
        if not order_executor.mt5_connected:
            print("❌ CRITICAL: MT5 NOT CONNECTED!")
            print("   This would cause the trading engine to stop")
            return False
        
        print("✅ MT5 connected successfully")
        
        # Check current daily P&L
        daily_pnl = order_executor.daily_pnl
        print(f"\n💰 CURRENT DAILY P&L: ${daily_pnl:.2f}")
        
        # Check loss limit (should be $0 now)
        max_drawdown = config.SYNTHETIC_RISK_RULES["circuit_breakers"]["max_daily_drawdown"]
        print(f"📊 Daily Loss Limit: ${max_drawdown:.2f}")
        
        if daily_pnl < -max_drawdown:
            print("🚨 ISSUE FOUND: Daily loss limit exceeded!")
            print(f"   Current P&L: ${daily_pnl:.2f}")
            print(f"   Loss Limit: -${max_drawdown:.2f}")
            print("   This would trigger emergency stop")
            
            if max_drawdown == 0.0:
                print("⚠️  PROBLEM: Loss limit is $0, so ANY loss triggers shutdown!")
                print("💡 SOLUTION: The issue is that $0 limit means NO losses allowed")
                return False
        else:
            print("✅ Daily loss limit OK")
        
        # Check active positions
        print(f"\n📊 ACTIVE POSITIONS:")
        print(f"   Count: {len(order_executor.active_positions)}")
        print(f"   Max Allowed: {order_executor.max_concurrent_positions}")
        
        if len(order_executor.active_positions) >= order_executor.max_concurrent_positions:
            print("⚠️  Position limit reached (but this shouldn't stop the engine)")
        
        # Check timeframe positions
        print(f"\n🕐 TIMEFRAME POSITIONS:")
        for timeframe, position_id in order_executor.timeframe_positions.items():
            status = f"OCCUPIED by {position_id}" if position_id else "AVAILABLE"
            print(f"   {timeframe}: {status}")
        
        # Check execution statistics
        exec_stats = order_executor.get_execution_statistics()
        print(f"\n📈 EXECUTION STATISTICS:")
        print(f"   Daily Trade Count: {exec_stats['daily_trade_count']}")
        print(f"   Fill Rate: {exec_stats['fill_rate']:.1%}")
        print(f"   Active Positions: {exec_stats['active_positions']}")
        
        # Check for other potential issues
        print(f"\n🔍 OTHER POTENTIAL ISSUES:")
        
        # Check if there are any errors in signal generation
        try:
            current_price = order_executor._get_current_price()
            if current_price is None:
                print("❌ ISSUE: Cannot get current price from MT5")
                print("   This could cause trading cycles to fail")
                return False
            else:
                print(f"✅ Current price available: {current_price:.2f}")
        except Exception as e:
            print(f"❌ ERROR getting current price: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during diagnosis: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_loss_limit_logic():
    """Check the specific loss limit logic that might be problematic."""
    print(f"\n🧮 CHECKING LOSS LIMIT LOGIC:")
    print("=" * 40)
    
    try:
        import config
        
        # Get the current limit
        max_drawdown = config.SYNTHETIC_RISK_RULES["circuit_breakers"]["max_daily_drawdown"]
        print(f"📊 Current max_daily_drawdown: ${max_drawdown:.2f}")
        
        # Simulate different P&L scenarios
        test_scenarios = [0.0, -0.01, -0.50, -1.00, -5.00]
        
        print(f"\n🧪 TESTING DIFFERENT P&L SCENARIOS:")
        for pnl in test_scenarios:
            would_trigger = pnl < -max_drawdown
            status = "🚨 WOULD STOP" if would_trigger else "✅ WOULD CONTINUE"
            print(f"   P&L: ${pnl:6.2f} | Limit: -${max_drawdown:.2f} | {status}")
        
        if max_drawdown == 0.0:
            print(f"\n⚠️  CRITICAL ISSUE IDENTIFIED:")
            print(f"   With $0.00 loss limit, ANY negative P&L triggers shutdown!")
            print(f"   Even a $0.01 loss would stop the system!")
            print(f"\n💡 RECOMMENDED FIX:")
            print(f"   Change max_daily_drawdown to a small negative value like -999.0")
            print(f"   This effectively disables the limit while allowing losses")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking loss limit logic: {e}")
        return False

def main():
    """Main diagnostic function."""
    print("🚨 TRADING ENGINE SHUTDOWN DIAGNOSIS")
    print("=" * 70)
    
    # Run diagnostics
    basic_check = diagnose_shutdown_causes()
    logic_check = check_loss_limit_logic()
    
    print(f"\n🎯 DIAGNOSIS RESULTS:")
    print("=" * 40)
    
    if not basic_check or not logic_check:
        print("❌ ISSUES FOUND!")
        print("\n💡 LIKELY CAUSE:")
        print("   The $0.00 loss limit is too restrictive!")
        print("   ANY loss (even $0.01) triggers emergency shutdown")
        print("\n🔧 RECOMMENDED SOLUTION:")
        print("   Change max_daily_drawdown from 0.0 to -999.0")
        print("   This effectively disables the limit while allowing the system to run")
        return False
    else:
        print("✅ No obvious issues found")
        print("   The shutdown might be caused by other factors")
        return True

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
