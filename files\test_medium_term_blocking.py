#!/usr/bin/env python3
"""
Test if MEDIUM TERM signals are being blocked when SHORT TERM positions exist.
"""

import sys
import MetaTrader5 as mt5
from datetime import datetime

def test_timeframe_independence():
    """Test if timeframes are truly independent."""
    print("🧪 TESTING TIMEFRAME INDEPENDENCE")
    print("=" * 60)
    
    try:
        # Import components
        from synthetic_data_collector import SyntheticDataCollector
        from order_execution_system import OrderExecutionSystem
        from trading_signal_generator import TradingSignal, SignalType
        
        # Initialize
        data_collector = SyntheticDataCollector()
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Components initialized")
        print(f"✅ MT5 Connected: {order_executor.mt5_connected}")
        
        # Check current timeframe state
        print(f"\n📊 CURRENT TIMEFRAME STATE:")
        for timeframe, position_id in order_executor.timeframe_positions.items():
            status = f"OCCUPIED by {position_id}" if position_id else "AVAILABLE"
            print(f"   {timeframe}: {status}")
        
        # Create a MEDIUM TERM test signal
        medium_signal = TradingSignal(
            signal_type=SignalType.STRONG_SELL,
            confidence=0.873,
            entry_price=55000.0,
            stop_loss=55150.0,
            take_profit=55100.0,
            position_size=0.01,
            risk_reward_ratio=2.0,
            timeframe=5,
            timestamp=datetime.now(),
            ai_predictions={"test": 0.873},
            market_regime="TEST",
            reasoning="Strong signal (-2) from medium_term_breakout_rf (medium_term) with 0.873 confidence"
        )
        
        print(f"\n🎯 TESTING MEDIUM TERM SIGNAL:")
        print(f"   Signal Type: {medium_signal.signal_type}")
        print(f"   Confidence: {medium_signal.confidence}")
        print(f"   Reasoning: {medium_signal.reasoning}")
        
        # Test timeframe detection
        detected_timeframe = order_executor._get_signal_timeframe(medium_signal)
        print(f"   Detected Timeframe: {detected_timeframe}")
        
        # Test execution permission
        can_execute = order_executor._can_execute_order(medium_signal)
        print(f"   Can Execute: {can_execute}")
        
        # Analyze the result
        print(f"\n📋 ANALYSIS:")
        if detected_timeframe == 'medium_term':
            print(f"   ✅ Timeframe detection working correctly")
            
            medium_position = order_executor.timeframe_positions.get('medium_term')
            if medium_position is None:
                print(f"   ✅ MEDIUM TERM slot is available")
                
                if can_execute:
                    print(f"   ✅ MEDIUM TERM signal should be allowed!")
                    print(f"   🎉 TIMEFRAME INDEPENDENCE IS WORKING!")
                else:
                    print(f"   ❌ MEDIUM TERM signal is being blocked!")
                    print(f"   🚨 BUG: Timeframes are NOT independent!")
                    
                    # Check what's blocking it
                    if len(order_executor.active_positions) >= order_executor.max_concurrent_positions:
                        print(f"   🚫 Blocked by: Max concurrent positions ({len(order_executor.active_positions)}/{order_executor.max_concurrent_positions})")
                    else:
                        print(f"   🚫 Blocked by: Unknown reason (check logs)")
            else:
                print(f"   ⚠️  MEDIUM TERM slot occupied by {medium_position}")
        else:
            print(f"   ❌ Timeframe detection failed: {detected_timeframe}")
        
        # Test SHORT TERM signal for comparison
        short_signal = TradingSignal(
            signal_type=SignalType.STRONG_BUY,
            confidence=0.998,
            entry_price=55000.0,
            stop_loss=54950.0,
            take_profit=55050.0,
            position_size=0.01,
            risk_reward_ratio=2.0,
            timeframe=5,
            timestamp=datetime.now(),
            ai_predictions={"test": 0.998},
            market_regime="TEST",
            reasoning="Strong signal (2) from short_term_pattern_nn (short_term) with 0.998 confidence"
        )
        
        print(f"\n🔥 TESTING SHORT TERM SIGNAL (for comparison):")
        short_timeframe = order_executor._get_signal_timeframe(short_signal)
        short_can_execute = order_executor._can_execute_order(short_signal)
        
        print(f"   Detected Timeframe: {short_timeframe}")
        print(f"   Can Execute: {short_can_execute}")
        
        if not short_can_execute:
            print(f"   ✅ SHORT TERM correctly blocked (slot occupied)")
        else:
            print(f"   ❌ SHORT TERM should be blocked but isn't!")
        
        # Final verdict
        print(f"\n🎯 FINAL VERDICT:")
        if detected_timeframe == 'medium_term' and can_execute and not short_can_execute:
            print(f"   ✅ TIMEFRAME INDEPENDENCE IS WORKING CORRECTLY!")
            print(f"   • MEDIUM TERM can execute (different timeframe)")
            print(f"   • SHORT TERM blocked (same timeframe occupied)")
        elif detected_timeframe == 'medium_term' and not can_execute:
            print(f"   ❌ BUG CONFIRMED: MEDIUM TERM INCORRECTLY BLOCKED!")
            print(f"   • MEDIUM TERM should be allowed but is blocked")
            print(f"   • This indicates a bug in timeframe independence")
        else:
            print(f"   ⚠️  INCONCLUSIVE: Need to check timeframe detection")
        
        return can_execute
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔍 MEDIUM TERM BLOCKING TEST")
    print("=" * 50)
    
    # Connect to MT5
    if not mt5.initialize():
        print("❌ MT5 initialization failed")
        return False
    
    try:
        result = test_timeframe_independence()
        return result
        
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
