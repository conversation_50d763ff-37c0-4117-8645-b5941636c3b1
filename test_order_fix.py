#!/usr/bin/env python3
"""
Test script to verify order execution fixes.
"""

import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_order_with_correct_filling():
    """Test order with correct filling mode."""
    try:
        import MetaTrader5 as mt5
        
        # Initialize MT5
        if not mt5.initialize():
            logger.error("MT5 initialization failed")
            return False
        
        symbol = "DEX 900 DOWN Index"
        symbol_info = mt5.symbol_info(symbol)
        tick = mt5.symbol_info_tick(symbol)
        
        if not symbol_info or not tick:
            logger.error("Could not get symbol info or tick")
            return False
        
        logger.info("=== Testing Order with Correct Filling Mode ===")
        
        # Test different filling modes
        filling_modes = [
            (mt5.ORDER_FILLING_FOK, "Fill or Kill"),
            (mt5.ORDER_FILLING_IOC, "Immediate or Cancel"),
            (mt5.ORDER_FILLING_RETURN, "Return"),
        ]
        
        test_volume = symbol_info.volume_min
        test_price = tick.ask
        
        for filling_mode, filling_name in filling_modes:
            logger.info(f"\nTesting {filling_name} (mode: {filling_mode})")
            
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": test_volume,
                "type": mt5.ORDER_TYPE_BUY,
                "price": test_price,
                "deviation": 20,
                "magic": 12345,
                "comment": f"Test order - {filling_name}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": filling_mode,
            }
            
            # Check order (but don't send)
            result = mt5.order_check(request)
            if result:
                logger.info(f"  Return code: {result.retcode}")
                logger.info(f"  Comment: {result.comment}")
                
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    logger.info(f"  ✅ {filling_name} is SUPPORTED")
                elif result.retcode == 10030:
                    logger.info(f"  ❌ {filling_name} is NOT SUPPORTED")
                else:
                    logger.info(f"  ⚠️ {filling_name} has issue: {result.retcode} - {result.comment}")
            else:
                logger.error(f"  ❌ Order check failed for {filling_name}")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        logger.error(f"Error testing order filling: {e}")
        return False

def test_volume_calculation_in_context():
    """Test the actual volume calculation from the trading system."""
    try:
        # Import the actual trading signal and order execution system
        from trading_signal_generator import TradingSignal, SignalType
        from order_execution_system import OrderExecutionSystem
        from data_collector import DataCollector
        
        # Create a mock data collector
        class MockDataCollector:
            def __init__(self):
                pass
        
        mock_data_collector = MockDataCollector()
        
        # Create order execution system
        execution_system = OrderExecutionSystem(mock_data_collector)
        
        # Create a test signal
        test_signal = TradingSignal(
            signal_type=SignalType.STRONG_SELL,
            confidence=0.8,
            entry_price=65000.0,
            stop_loss=65350.0,
            take_profit=64650.0,
            position_size=0.01,  # 1% position size
            risk_reward_ratio=2.0,
            timeframe=5,
            timestamp=datetime.now(),
            ai_predictions={},
            market_regime='NORMAL',
            reasoning='Test signal for volume calculation'
        )
        
        logger.info("=== Testing Volume Calculation in Context ===")
        logger.info(f"Test signal: {test_signal.signal_type.name}")
        logger.info(f"Position size: {test_signal.position_size}")
        logger.info(f"Entry price: {test_signal.entry_price}")
        
        # Test volume calculation
        calculated_volume = execution_system._calculate_volume(test_signal)
        logger.info(f"Calculated volume: {calculated_volume}")
        
        # Test if volume is valid
        import MetaTrader5 as mt5
        if not mt5.initialize():
            logger.error("MT5 initialization failed")
            return False
        
        symbol_info = mt5.symbol_info("DEX 900 DOWN Index")
        if symbol_info:
            is_valid = (symbol_info.volume_min <= calculated_volume <= symbol_info.volume_max and 
                       calculated_volume % symbol_info.volume_step == 0)
            logger.info(f"Volume valid: {is_valid}")
            logger.info(f"  Min: {symbol_info.volume_min}, Max: {symbol_info.volume_max}, Step: {symbol_info.volume_step}")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        logger.error(f"Error testing volume calculation in context: {e}")
        return False

def main():
    """Run order execution tests."""
    logger.info("🔧 ORDER EXECUTION FIX TESTING")
    logger.info("=" * 50)
    
    tests = [
        ("Order Filling Mode Test", test_order_with_correct_filling),
        ("Volume Calculation in Context", test_volume_calculation_in_context),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"Result: {status}")
        except Exception as e:
            logger.error(f"Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("🎉 All tests passed! Order execution should work correctly.")
    else:
        logger.warning("⚠️ Some tests failed. Check the logs above for details.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
