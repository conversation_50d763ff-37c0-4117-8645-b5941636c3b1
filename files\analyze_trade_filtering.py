#!/usr/bin/env python3
"""
Analyze trade filtering to separate AI bot trades from manual trades.
"""

import MetaTrader5 as mt5
from datetime import datetime

def analyze_trades():
    """Analyze all trades to identify AI bot vs manual trades."""
    print("🔍 Analyzing Trade Filtering...")
    
    # Initialize MT5
    if not mt5.initialize():
        print('❌ MT5 initialization failed')
        return

    # Get all historical data from May 1st
    start_time = datetime(2025, 5, 1)
    end_time = datetime.now()

    print(f'📅 Checking deals from {start_time} to {end_time}')

    # Get deals (closed trades) from MT5
    deals = mt5.history_deals_get(start_time, end_time, group='*')

    if deals is None:
        print('❌ No deals found')
        mt5.shutdown()
        return

    print(f'📊 Total deals found: {len(deals)}')
    
    ai_bot_trades = 0
    manual_trades = 0
    other_trades = 0
    
    print('\n🤖 === AI BOT TRADES (Magic=12345) ===')
    for deal in deals:
        # Check if this is an AI bot trade
        if (hasattr(deal, 'magic') and deal.magic == 12345 and
            hasattr(deal, 'symbol') and deal.symbol == 'DEX 900 DOWN Index' and
            hasattr(deal, 'entry') and deal.entry == 1):  # Entry = 1 means exit (close) trade
            
            comment = getattr(deal, 'comment', 'N/A')
            time_str = datetime.fromtimestamp(deal.time).strftime('%Y-%m-%d %H:%M:%S')
            print(f'  ✅ Profit={deal.profit:.2f}, Comment="{comment}", Time={time_str}')
            ai_bot_trades += 1
    
    print(f'\n👤 === MANUAL TRADES (Magic!=12345) ===')
    for deal in deals:
        # Check if this is a manual trade
        if (hasattr(deal, 'symbol') and deal.symbol == 'DEX 900 DOWN Index' and
            hasattr(deal, 'entry') and deal.entry == 1 and
            (not hasattr(deal, 'magic') or deal.magic != 12345)):
            
            magic = getattr(deal, 'magic', 'N/A')
            comment = getattr(deal, 'comment', 'N/A')
            time_str = datetime.fromtimestamp(deal.time).strftime('%Y-%m-%d %H:%M:%S')
            print(f'  🔸 Magic={magic}, Profit={deal.profit:.2f}, Comment="{comment}", Time={time_str}')
            manual_trades += 1
    
    print(f'\n❓ === OTHER TRADES (Different symbol/entry) ===')
    for deal in deals:
        # Check if this is some other trade
        if not (hasattr(deal, 'symbol') and deal.symbol == 'DEX 900 DOWN Index' and
                hasattr(deal, 'entry') and deal.entry == 1):
            
            symbol = getattr(deal, 'symbol', 'N/A')
            entry = getattr(deal, 'entry', 'N/A')
            magic = getattr(deal, 'magic', 'N/A')
            comment = getattr(deal, 'comment', 'N/A')
            time_str = datetime.fromtimestamp(deal.time).strftime('%Y-%m-%d %H:%M:%S')
            print(f'  ⚪ Symbol={symbol}, Entry={entry}, Magic={magic}, Comment="{comment}", Time={time_str}')
            other_trades += 1
    
    print(f'\n📈 SUMMARY:')
    print(f'🤖 AI Bot trades: {ai_bot_trades}')
    print(f'👤 Manual trades: {manual_trades}')
    print(f'❓ Other trades: {other_trades}')
    print(f'📊 Total: {ai_bot_trades + manual_trades + other_trades}')
    
    if manual_trades > 0:
        print(f'\n⚠️  ISSUE: {manual_trades} manual trades are being included!')
        print('🔧 Need to improve filtering to exclude manual trades.')
    else:
        print(f'\n✅ GOOD: Only AI bot trades are being counted.')

    mt5.shutdown()

if __name__ == "__main__":
    analyze_trades()
