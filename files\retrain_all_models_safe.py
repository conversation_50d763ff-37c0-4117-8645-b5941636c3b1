#!/usr/bin/env python3
"""
Safely retrain all 9 AI models with improved data collection and fixed dependencies.
This ensures all models benefit from optimized timeframe-specific data ranges.
"""

import sys
import time
from datetime import datetime

def retrain_all_models_comprehensive():
    """Comprehensively retrain all 9 AI models with full data collection."""
    print("🔄 COMPREHENSIVE AI MODEL RETRAINING")
    print("=" * 70)
    print("Retraining all 9 models with:")
    print("✅ Improved timeframe-specific data ranges")
    print("✅ Fixed imblearn class balancing")
    print("✅ Optimized 1-min, 5-min data collection")
    print("✅ Full historical data for all timeframes")
    
    try:
        # Import components
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        
        print("\n🔧 INITIALIZING COMPONENTS...")
        print("-" * 50)
        
        # Initialize components
        print("   Initializing Data Collector...")
        data_collector = SyntheticDataCollector()
        
        if not data_collector.mt5_connected:
            print("❌ MT5 not connected - cannot retrain models")
            return False
        
        print("   ✅ Data Collector initialized (MT5 connected)")
        
        print("   Initializing Pattern Detector...")
        pattern_detector = SyntheticPatternDetector(data_collector)
        print("   ✅ Pattern Detector initialized")
        
        print("   Initializing AI Model Manager...")
        ai_manager = AIModelManager(data_collector, pattern_detector)
        print("   ✅ AI Model Manager initialized")
        
        # Get all model configurations
        all_models = list(ai_manager.model_configs.keys())
        
        print(f"\n📊 ALL 9 MODELS TO RETRAIN:")
        print("-" * 50)
        
        # Group models by category for better display
        short_term_models = [m for m in all_models if 'short_term' in m]
        medium_term_models = [m for m in all_models if 'medium_term' in m]
        long_term_models = [m for m in all_models if 'long_term' in m]
        
        print("🏃 SHORT-TERM MODELS (1M, 5M, 15M):")
        for model in short_term_models:
            config = ai_manager.model_configs[model]
            print(f"   • {model}: {config['purpose']}")
        
        print("\n🚶 MEDIUM-TERM MODELS (15M, 30M, 1H):")
        for model in medium_term_models:
            config = ai_manager.model_configs[model]
            print(f"   • {model}: {config['purpose']}")
        
        print("\n🚀 LONG-TERM MODELS (1H, 4H, 1D):")
        for model in long_term_models:
            config = ai_manager.model_configs[model]
            print(f"   • {model}: {config['purpose']}")
        
        # Comprehensive data collection with new ranges
        print(f"\n📈 COMPREHENSIVE DATA COLLECTION...")
        print("=" * 60)
        
        print("Collecting fresh data with optimized timeframe ranges...")
        
        # Use the comprehensive data collection method
        success = data_collector.collect_historical_data()
        
        if not success:
            print("❌ Data collection failed - cannot proceed with training")
            return False
        
        print("✅ Comprehensive data collection completed")
        
        # Verify data availability for key timeframes
        print(f"\n🔍 VERIFYING DATA AVAILABILITY:")
        print("-" * 40)
        
        key_timeframes = [1, 5, 15, 30, 60, 240, 1440]
        data_status = {}
        
        for tf in key_timeframes:
            df = data_collector.get_latest_data(tf, count=100)
            if not df.empty:
                data_status[tf] = len(df)
                print(f"   ✅ {tf:4}min: {len(df):5} records available")
            else:
                data_status[tf] = 0
                print(f"   ⚠️  {tf:4}min: No data available")
        
        # Check if we have sufficient data
        critical_timeframes = [5, 15, 60]  # Most important for models
        sufficient_data = all(data_status.get(tf, 0) > 50 for tf in critical_timeframes)
        
        if not sufficient_data:
            print("\n⚠️  WARNING: Insufficient data for some critical timeframes")
            print("   Training will continue but some models may have limited performance")
        else:
            print("\n✅ Sufficient data available for all critical timeframes")
        
        # Start comprehensive model training
        print(f"\n🚀 STARTING COMPREHENSIVE MODEL TRAINING...")
        print("=" * 70)
        
        training_results = {}
        total_models = len(all_models)
        start_time = time.time()
        
        for i, model_name in enumerate(all_models, 1):
            print(f"\n🧠 TRAINING MODEL {i}/{total_models}: {model_name}")
            print("-" * 60)
            
            config = ai_manager.model_configs[model_name]
            timeframes = config['timeframes']
            model_type = config['model_class']
            
            print(f"   Model Type: {model_type}")
            print(f"   Timeframes: {timeframes}")
            print(f"   Purpose: {config['purpose']}")
            
            model_start_time = time.time()
            
            try:
                # Train the model
                success = ai_manager.train_model(model_name)
                
                training_time = time.time() - model_start_time
                
                if success:
                    # Get performance metrics
                    performance = ai_manager.model_performance.get(model_name, {})
                    accuracy = performance.get('accuracy', 0)
                    samples = performance.get('total_samples', 0)
                    
                    print(f"   ✅ SUCCESS: {model_name}")
                    print(f"      Training time: {training_time:.1f} seconds")
                    print(f"      Accuracy: {accuracy:.3f}")
                    print(f"      Training samples: {samples}")
                    print(f"      Model saved successfully")
                    
                    training_results[model_name] = {
                        'success': True,
                        'accuracy': accuracy,
                        'samples': samples,
                        'time': training_time,
                        'model_type': model_type
                    }
                else:
                    print(f"   ❌ FAILED: {model_name}")
                    print(f"      Training time: {training_time:.1f} seconds")
                    print(f"      Check logs for detailed error information")
                    
                    training_results[model_name] = {
                        'success': False,
                        'time': training_time,
                        'model_type': model_type
                    }
                    
            except Exception as e:
                training_time = time.time() - model_start_time
                print(f"   ❌ ERROR: {model_name}")
                print(f"      Exception: {e}")
                print(f"      Training time: {training_time:.1f} seconds")
                
                training_results[model_name] = {
                    'success': False,
                    'error': str(e),
                    'time': training_time,
                    'model_type': model_type
                }
        
        # Generate comprehensive summary
        total_time = time.time() - start_time
        
        print(f"\n📊 COMPREHENSIVE TRAINING SUMMARY:")
        print("=" * 70)
        
        successful_models = [name for name, result in training_results.items() if result['success']]
        failed_models = [name for name, result in training_results.items() if not result['success']]
        
        print(f"✅ SUCCESSFUL MODELS: {len(successful_models)}/{total_models}")
        print("-" * 50)
        
        # Group successful models by category
        successful_short = [m for m in successful_models if 'short_term' in m]
        successful_medium = [m for m in successful_models if 'medium_term' in m]
        successful_long = [m for m in successful_models if 'long_term' in m]
        
        if successful_short:
            print(f"🏃 Short-term models ({len(successful_short)}/3):")
            for model in successful_short:
                result = training_results[model]
                print(f"   • {model}: {result['accuracy']:.3f} accuracy ({result['samples']} samples)")
        
        if successful_medium:
            print(f"\n🚶 Medium-term models ({len(successful_medium)}/3):")
            for model in successful_medium:
                result = training_results[model]
                print(f"   • {model}: {result['accuracy']:.3f} accuracy ({result['samples']} samples)")
        
        if successful_long:
            print(f"\n🚀 Long-term models ({len(successful_long)}/3):")
            for model in successful_long:
                result = training_results[model]
                print(f"   • {model}: {result['accuracy']:.3f} accuracy ({result['samples']} samples)")
        
        if failed_models:
            print(f"\n❌ FAILED MODELS: {len(failed_models)}/{total_models}")
            print("-" * 40)
            for model in failed_models:
                result = training_results[model]
                error = result.get('error', 'Training failed')
                print(f"   • {model}: {error}")
        
        print(f"\n⏱️  TRAINING STATISTICS:")
        print("-" * 30)
        print(f"   Total time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
        print(f"   Average per model: {total_time/total_models:.1f} seconds")
        print(f"   Success rate: {len(successful_models)/total_models:.1%}")
        
        # Final assessment
        if len(successful_models) == total_models:
            print(f"\n🎉 PERFECT SUCCESS: ALL 9 MODELS RETRAINED!")
            print(f"   Complete AI trading system ready")
            print(f"   All timeframes optimized with new data ranges")
            print(f"   Enhanced performance expected across all strategies")
            return True
        elif len(successful_models) >= 6:
            print(f"\n✅ EXCELLENT SUCCESS: {len(successful_models)}/9 models retrained")
            print(f"   Trading system operational with enhanced models")
            print(f"   Failed models can be retrained individually if needed")
            return True
        elif len(successful_models) >= 3:
            print(f"\n⚠️  PARTIAL SUCCESS: {len(successful_models)}/9 models retrained")
            print(f"   Basic trading functionality available")
            print(f"   Consider investigating failed models")
            return True
        else:
            print(f"\n❌ INSUFFICIENT SUCCESS: Only {len(successful_models)}/9 models retrained")
            print(f"   System may have limited functionality")
            print(f"   Review errors and data availability")
            return False
        
    except Exception as e:
        print(f"❌ Error during comprehensive model retraining: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main retraining function."""
    print("🔄 COMPREHENSIVE AI MODEL RETRAINING SYSTEM")
    print("=" * 80)
    print("This will retrain ALL 9 AI models with:")
    print("• Improved timeframe-specific data collection")
    print("• Fixed imblearn class balancing")
    print("• Optimized 1-minute and 5-minute data ranges")
    print("• Full historical data for medium and long-term models")
    print()
    
    # Confirm with user
    response = input("Continue with comprehensive model retraining? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Comprehensive retraining cancelled.")
        return False
    
    print()
    
    # Start comprehensive retraining
    success = retrain_all_models_comprehensive()
    
    print(f"\n💡 WHAT HAPPENS NEXT:")
    print("=" * 50)
    
    if success:
        print("✅ Models are now optimized with improved data collection")
        print("✅ All timeframes benefit from appropriate historical ranges")
        print("✅ Class balancing improvements enhance model quality")
        print("✅ System ready for enhanced trading performance")
        print("✅ Restart your trading system to use the new models")
    else:
        print("❌ Retraining had issues - check error messages above")
        print("💡 You can still use existing models while troubleshooting")
        print("💡 Consider retraining individual models that failed")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
