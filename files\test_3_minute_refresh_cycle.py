#!/usr/bin/env python3
"""
Test script to verify the 3-minute refresh cycle is working properly.
This monitors the system for multiple cycles to ensure models are reanalyzing every 3 minutes.
"""

import sys
import logging
import time
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_3_minute_refresh_cycle():
    """Test that the system refreshes every 3 minutes with model reanalysis."""
    try:
        print("🔄 TESTING 3-MINUTE REFRESH CYCLE")
        print("=" * 60)
        print("⚠️  This will monitor the system for 10 minutes to verify refresh cycles")
        print("=" * 60)
        
        # Import required modules
        from trading_signal_generator import TradingSignalGenerator
        from order_execution_system import OrderExecutionSystem
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        
        print("📊 Initializing trading components...")
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Components initialized")
        
        # Get current price
        print("💰 Getting current market price...")
        import MetaTrader5 as mt5
        tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
        if tick is None:
            print("❌ Could not get current price!")
            return False
        current_price = (tick.bid + tick.ask) / 2
        print(f"✅ Current price: {current_price:.2f}")
        
        # Monitor for multiple 3-minute cycles
        print(f"\n🕐 STARTING 3-MINUTE CYCLE MONITORING")
        print(f"   Start time: {datetime.now()}")
        print(f"   Will monitor for 10 minutes (3+ cycles)")
        print(f"   Expected cycle interval: 180 seconds (3 minutes)")
        
        cycle_count = 0
        last_signal_time = None
        last_prediction_time = None
        cycle_times = []
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=10)  # Monitor for 10 minutes
        
        print(f"\n📋 CYCLE MONITORING LOG:")
        print(f"   {'Cycle':<6} {'Time':<20} {'Interval':<12} {'Signal':<15} {'Models':<10}")
        print(f"   {'-'*6} {'-'*20} {'-'*12} {'-'*15} {'-'*10}")
        
        while datetime.now() < end_time:
            try:
                current_time = datetime.now()
                
                # Generate signal to trigger model analysis
                signal = signal_generator.generate_signal(current_price)
                
                # Check if this is a new cycle (models reanalyzed)
                if signal or True:  # Always log for monitoring
                    cycle_count += 1
                    
                    # Calculate interval since last cycle
                    interval_str = "N/A"
                    if last_signal_time:
                        interval = (current_time - last_signal_time).total_seconds()
                        interval_str = f"{interval:.1f}s"
                        cycle_times.append(interval)
                    
                    # Signal info
                    signal_str = signal.signal_type.name if signal else "NO_SIGNAL"
                    
                    # Model status
                    loaded_models = len(ai_manager.models)
                    model_str = f"{loaded_models}/9"
                    
                    print(f"   {cycle_count:<6} {current_time.strftime('%H:%M:%S'):<20} {interval_str:<12} {signal_str:<15} {model_str:<10}")
                    
                    last_signal_time = current_time
                    
                    # Check if models are actually making new predictions
                    try:
                        # Get ensemble prediction to verify models are working
                        ensemble_pred = ai_manager.get_ensemble_prediction(current_price)
                        if ensemble_pred:
                            prediction_time = datetime.now()
                            if last_prediction_time:
                                pred_interval = (prediction_time - last_prediction_time).total_seconds()
                                if pred_interval < 200:  # Within reasonable range of 3 minutes
                                    print(f"      ✅ Models reanalyzed (interval: {pred_interval:.1f}s)")
                                else:
                                    print(f"      ⚠️  Long interval since last prediction: {pred_interval:.1f}s")
                            last_prediction_time = prediction_time
                    except Exception as e:
                        print(f"      ❌ Error getting model predictions: {e}")
                
                # Wait for next check (every 30 seconds to catch cycles)
                time.sleep(30)
                
            except KeyboardInterrupt:
                print("\n⏹️  Monitoring stopped by user")
                break
            except Exception as e:
                print(f"      ❌ Error in monitoring loop: {e}")
                time.sleep(30)
        
        # Analysis of results
        print(f"\n📊 CYCLE ANALYSIS RESULTS:")
        
        if len(cycle_times) >= 2:
            avg_interval = sum(cycle_times) / len(cycle_times)
            min_interval = min(cycle_times)
            max_interval = max(cycle_times)
            
            print(f"   Total cycles monitored: {cycle_count}")
            print(f"   Average cycle interval: {avg_interval:.1f} seconds")
            print(f"   Minimum interval: {min_interval:.1f} seconds")
            print(f"   Maximum interval: {max_interval:.1f} seconds")
            print(f"   Expected interval: 180 seconds (3 minutes)")
            
            # Check if intervals are close to 3 minutes (180 seconds)
            # Allow some tolerance (±30 seconds)
            intervals_in_range = [t for t in cycle_times if 150 <= t <= 210]
            compliance_rate = len(intervals_in_range) / len(cycle_times) * 100
            
            print(f"   Intervals within range (150-210s): {len(intervals_in_range)}/{len(cycle_times)}")
            print(f"   Compliance rate: {compliance_rate:.1f}%")
            
            if compliance_rate >= 80:
                print(f"   ✅ 3-MINUTE CYCLE COMPLIANCE: GOOD ({compliance_rate:.1f}%)")
                cycle_result = True
            elif compliance_rate >= 60:
                print(f"   ⚠️  3-MINUTE CYCLE COMPLIANCE: MODERATE ({compliance_rate:.1f}%)")
                cycle_result = True
            else:
                print(f"   ❌ 3-MINUTE CYCLE COMPLIANCE: POOR ({compliance_rate:.1f}%)")
                cycle_result = False
        else:
            print(f"   ⚠️  Insufficient data for analysis (only {len(cycle_times)} intervals)")
            cycle_result = False
        
        # Summary
        print(f"\n📋 REFRESH CYCLE SUMMARY:")
        if cycle_result:
            print(f"   ✅ System is refreshing approximately every 3 minutes")
            print(f"   ✅ Models are reanalyzing market conditions regularly")
            print(f"   ✅ Trading cycles are operating as expected")
        else:
            print(f"   ❌ System refresh cycles are not consistent with 3-minute target")
            print(f"   🔧 May need adjustment to trading engine timing")
        
        return cycle_result
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔄 3-MINUTE REFRESH CYCLE TEST")
    print("=" * 60)
    print("⚠️  This monitors the system for 10 minutes to verify refresh timing")
    print("=" * 60)
    
    success = test_3_minute_refresh_cycle()
    
    if success:
        print("\n🎉 3-MINUTE REFRESH CYCLE TEST PASSED!")
        print("✅ System is refreshing models and dashboard every 3 minutes!")
        print("✅ Real-time analysis and reanalysis working correctly!")
    else:
        print("\n❌ 3-MINUTE REFRESH CYCLE TEST FAILED!")
        print("🔧 System refresh timing may need adjustment.")
        
    sys.exit(0 if success else 1)
