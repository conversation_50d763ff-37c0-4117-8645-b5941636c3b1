#!/usr/bin/env python3
"""
Test script to find the REAL minimum SL/TP distance for DEX 900 DOWN Index.
This will test different distances to find what actually works.
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_find_real_minimum():
    """Find the real minimum SL/TP distance that works."""
    try:
        print("🔍 FINDING REAL MINIMUM SL/TP DISTANCE")
        print("=" * 60)
        print("⚠️  This will test different SL/TP distances to find what works")
        print("=" * 60)
        
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ MT5 initialization failed!")
            return False
            
        # Get current price and symbol info
        print("💰 Getting market information...")
        
        tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
        if tick is None:
            print("❌ Could not get current price!")
            return False
        current_price = (tick.bid + tick.ask) / 2
        
        symbol_info = mt5.symbol_info("DEX 900 DOWN Index")
        if symbol_info is None:
            print("❌ Could not get symbol information!")
            return False
            
        print(f"✅ Current Price: {current_price:.2f}")
        print(f"✅ Symbol Info:")
        print(f"   Stops Level: {symbol_info.trade_stops_level} points")
        print(f"   Freeze Level: {symbol_info.trade_freeze_level} points")
        print(f"   Point Value: {symbol_info.point}")
        print(f"   Spread: {symbol_info.spread} points")
        
        # Test different distances
        test_distances = [
            10.05,   # Calculated minimum
            15.0,    # 50% more
            20.0,    # Double
            25.0,    # 2.5x
            30.0,    # 3x
            50.0,    # 5x
        ]
        
        print(f"\n🧪 TESTING DIFFERENT SL/TP DISTANCES:")
        
        for distance in test_distances:
            print(f"\n📏 Testing distance: {distance:.2f} points")
            
            # Create test order request
            test_sl = current_price - distance
            test_tp = current_price + distance
            
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": "DEX 900 DOWN Index",
                "volume": 0.01,
                "type": mt5.ORDER_TYPE_BUY,
                "sl": test_sl,
                "tp": test_tp,
                "deviation": 20,
                "magic": 123456,
                "comment": f"Test_Distance_{distance}",
            }
            
            print(f"   SL: {test_sl:.2f} (distance: {distance:.2f})")
            print(f"   TP: {test_tp:.2f} (distance: {distance:.2f})")
            
            # Check the order (don't send it)
            result = mt5.order_check(request)
            
            if result:
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    print(f"   ✅ ACCEPTED: Distance {distance:.2f} is VALID")
                    print(f"      Margin required: ${result.margin:.2f}")
                    print(f"      Profit: ${result.profit:.2f}")
                    
                    # This distance works, let's try to find the exact minimum
                    if distance == test_distances[0]:
                        print(f"   🎯 First distance works! Testing smaller increments...")
                        
                        # Test smaller distances
                        for small_dist in [9.0, 9.5, 10.0, 10.1, 10.2, 10.3, 10.4, 10.5]:
                            small_sl = current_price - small_dist
                            small_tp = current_price + small_dist
                            
                            small_request = request.copy()
                            small_request["sl"] = small_sl
                            small_request["tp"] = small_tp
                            small_request["comment"] = f"Test_Small_{small_dist}"
                            
                            small_result = mt5.order_check(small_request)
                            if small_result and small_result.retcode == mt5.TRADE_RETCODE_DONE:
                                print(f"      ✅ {small_dist:.1f} points: VALID")
                            else:
                                error_msg = small_result.comment if small_result else "No result"
                                print(f"      ❌ {small_dist:.1f} points: INVALID ({error_msg})")
                    
                    break  # Found working distance
                    
                else:
                    error_msg = result.comment if result.comment else f"Error {result.retcode}"
                    print(f"   ❌ REJECTED: {error_msg}")
                    
            else:
                print(f"   ❌ NO RESULT from order_check")
        
        # Test with spread consideration
        print(f"\n📊 TESTING WITH SPREAD CONSIDERATION:")
        spread_points = symbol_info.spread
        spread_price = spread_points * symbol_info.point
        
        print(f"   Current Spread: {spread_points} points ({spread_price:.2f} price units)")
        
        # Try distance = stops_level + spread
        combined_distance = (symbol_info.trade_stops_level * symbol_info.point) + spread_price
        print(f"   Trying stops_level + spread: {combined_distance:.2f}")
        
        combined_sl = current_price - combined_distance
        combined_tp = current_price + combined_distance
        
        combined_request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": "DEX 900 DOWN Index",
            "volume": 0.01,
            "type": mt5.ORDER_TYPE_BUY,
            "sl": combined_sl,
            "tp": combined_tp,
            "deviation": 20,
            "magic": 123456,
            "comment": "Test_Combined",
        }
        
        combined_result = mt5.order_check(combined_request)
        if combined_result and combined_result.retcode == mt5.TRADE_RETCODE_DONE:
            print(f"   ✅ COMBINED DISTANCE WORKS: {combined_distance:.2f}")
        else:
            error_msg = combined_result.comment if combined_result else "No result"
            print(f"   ❌ Combined distance failed: {error_msg}")
        
        # Test without SL/TP (market order only)
        print(f"\n🎯 TESTING MARKET ORDER WITHOUT SL/TP:")
        
        market_request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": "DEX 900 DOWN Index",
            "volume": 0.01,
            "type": mt5.ORDER_TYPE_BUY,
            "deviation": 20,
            "magic": 123456,
            "comment": "Test_Market_Only",
        }
        
        market_result = mt5.order_check(market_request)
        if market_result and market_result.retcode == mt5.TRADE_RETCODE_DONE:
            print(f"   ✅ MARKET ORDER (no SL/TP) WORKS")
            print(f"      This confirms the issue is with SL/TP distances")
        else:
            error_msg = market_result.comment if market_result else "No result"
            print(f"   ❌ Even market order failed: {error_msg}")
        
        # Summary
        print(f"\n📋 SUMMARY:")
        print(f"   Symbol: DEX 900 DOWN Index")
        print(f"   Current Price: {current_price:.2f}")
        print(f"   Stops Level: {symbol_info.trade_stops_level} points ({symbol_info.trade_stops_level * symbol_info.point:.2f} price)")
        print(f"   Spread: {symbol_info.spread} points ({spread_price:.2f} price)")
        print(f"   Point Value: {symbol_info.point}")
        
        print(f"\n💡 RECOMMENDATIONS:")
        print(f"   1. Try using larger distances (20+ points)")
        print(f"   2. Consider spread in calculations")
        print(f"   3. Test with market orders first, add SL/TP after")
        print(f"   4. Check if broker has special requirements for synthetic indices")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 FINDING REAL MINIMUM SL/TP DISTANCE")
    print("=" * 60)
    print("⚠️  This test finds the actual minimum SL/TP distance")
    print("=" * 60)
    
    success = test_find_real_minimum()
    
    if success:
        print("\n🎉 MINIMUM DISTANCE INVESTIGATION COMPLETE!")
        print("✅ Check the results above for working distances!")
    else:
        print("\n❌ MINIMUM DISTANCE INVESTIGATION FAILED!")
        print("🔧 Please check the error messages above.")
        
    sys.exit(0 if success else 1)
