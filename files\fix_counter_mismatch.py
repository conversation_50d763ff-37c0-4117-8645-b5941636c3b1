#!/usr/bin/env python3
"""
Fix the counter mismatch by resetting counters to match actual MT5 trades.
"""

import MetaTrader5 as mt5
import json
import os
from datetime import datetime

def fix_counter_mismatch():
    """Reset counters to match actual MT5 trades."""
    print("🔧 FIXING COUNTER MISMATCH...")
    print("=" * 60)
    
    # Initialize MT5
    if not mt5.initialize():
        print('❌ MT5 initialization failed')
        return
    
    # Get today's trades
    today = datetime.now().date()
    start_time = datetime.combine(today, datetime.min.time())
    end_time = datetime.now()
    
    print(f"📅 Checking actual trades from {start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%H:%M')}")
    
    # Get deals from MT5
    deals = mt5.history_deals_get(start_time, end_time, group="*")
    
    if deals is None:
        print("❌ No deals found")
        mt5.shutdown()
        return
    
    # Count ACTUAL opening trades by timeframe
    actual_counts = {'short_term': 0, 'medium_term': 0, 'long_term': 0}
    trade_details = []
    
    for deal in deals:
        if (hasattr(deal, 'magic') and deal.magic == 54321 and
            hasattr(deal, 'symbol') and deal.symbol == 'DEX 900 DOWN Index' and
            hasattr(deal, 'entry') and deal.entry == 0):  # Opening trades only
            
            comment = getattr(deal, 'comment', '')
            time_str = datetime.fromtimestamp(deal.time).strftime('%H:%M:%S')
            
            # Determine timeframe from comment
            timeframe = None
            comment_upper = comment.upper()
            if 'AI_BOT_SHORT' in comment_upper:
                timeframe = 'short_term'
                actual_counts['short_term'] += 1
            elif 'AI_BOT_MEDIUM' in comment_upper:
                timeframe = 'medium_term'
                actual_counts['medium_term'] += 1
            elif 'AI_BOT_LONG' in comment_upper:
                timeframe = 'long_term'
                actual_counts['long_term'] += 1
            
            if timeframe:
                trade_details.append({
                    'time': time_str,
                    'timeframe': timeframe,
                    'comment': comment,
                    'ticket': deal.ticket
                })
    
    print(f"\n📊 ACTUAL MT5 TRADES TODAY:")
    print(f"   Short Term: {actual_counts['short_term']}")
    print(f"   Medium Term: {actual_counts['medium_term']}")
    print(f"   Long Term: {actual_counts['long_term']}")
    print(f"   Total: {sum(actual_counts.values())}")
    
    # Show current counter file
    counter_file = "data/timeframe_counters.json"
    current_counts = {'short_term': 0, 'medium_term': 0, 'long_term': 0}
    
    if os.path.exists(counter_file):
        with open(counter_file, 'r') as f:
            counter_data = json.load(f)
        current_counts = counter_data.get('daily', {})
        
        print(f"\n📁 CURRENT COUNTER FILE:")
        print(f"   Short Term: {current_counts.get('short_term', 0)}")
        print(f"   Medium Term: {current_counts.get('medium_term', 0)}")
        print(f"   Long Term: {current_counts.get('long_term', 0)}")
        print(f"   Total: {sum(current_counts.values())}")
    
    # Check if correction is needed
    needs_correction = (
        actual_counts['short_term'] != current_counts.get('short_term', 0) or
        actual_counts['medium_term'] != current_counts.get('medium_term', 0) or
        actual_counts['long_term'] != current_counts.get('long_term', 0)
    )
    
    if needs_correction:
        print(f"\n⚠️  MISMATCH DETECTED!")
        print(f"   Difference Short: {actual_counts['short_term'] - current_counts.get('short_term', 0)}")
        print(f"   Difference Medium: {actual_counts['medium_term'] - current_counts.get('medium_term', 0)}")
        print(f"   Difference Long: {actual_counts['long_term'] - current_counts.get('long_term', 0)}")
        
        # Ask for confirmation to fix
        print(f"\n🔧 FIXING COUNTERS...")
        
        # Update counter file to match actual trades
        if os.path.exists(counter_file):
            with open(counter_file, 'r') as f:
                counter_data = json.load(f)
        else:
            counter_data = {}
        
        # Update daily counts to match actual MT5 trades
        counter_data['daily'] = actual_counts.copy()
        counter_data['last_update'] = datetime.now().isoformat()
        counter_data['last_daily_reset'] = today.isoformat()
        
        # Keep monthly counts or initialize if missing
        if 'monthly' not in counter_data:
            counter_data['monthly'] = actual_counts.copy()
        
        # Save corrected counter file
        os.makedirs("data", exist_ok=True)
        with open(counter_file, 'w') as f:
            json.dump(counter_data, f, indent=2)
        
        print(f"✅ COUNTERS CORRECTED!")
        print(f"   New Short Term: {actual_counts['short_term']}")
        print(f"   New Medium Term: {actual_counts['medium_term']}")
        print(f"   New Long Term: {actual_counts['long_term']}")
        
    else:
        print(f"\n✅ COUNTERS ARE ALREADY CORRECT!")
    
    # Show recent trade details
    if trade_details:
        print(f"\n📋 RECENT ACTUAL TRADES:")
        print(f"{'Time':>8} | {'Timeframe':>10} | {'Ticket':>8} | Comment")
        print("-" * 60)
        for trade in trade_details[-10:]:  # Show last 10
            print(f"{trade['time']:>8} | {trade['timeframe']:>10} | {trade['ticket']:>8} | {trade['comment']}")
    
    mt5.shutdown()
    print(f"\n✅ Counter correction complete!")
    print(f"💡 Dashboard should now show correct counts after refresh.")

if __name__ == "__main__":
    fix_counter_mismatch()
