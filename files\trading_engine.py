"""
Main Trading Engine for Synthetic DEX 900 DOWN Index Trading System.
Integrates all components for automated trading with 5-minute cycles.
"""

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import signal
import sys

import config
from synthetic_data_collector import SyntheticDataCollector
from synthetic_pattern_detector import SyntheticPatternDetector
from ai_model_manager import AIModelManager
from trading_signal_generator import TradingSignalGenerator
from order_execution_system import OrderExecutionSystem

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trading_engine.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("TradingEngine")

class TradingEngine:
    """
    Main trading engine that orchestrates all components for automated trading.
    Runs 5-minute trading cycles with real-time monitoring and risk management.
    """
    
    def __init__(self):
        """Initialize the Trading Engine."""
        logger.info("Initializing Trading Engine...")
        
        # System state
        self.running = False
        self.trading_active = True
        self.emergency_stop_triggered = False
        
        # Components
        self.data_collector = None
        self.pattern_detector = None
        self.ai_manager = None
        self.signal_generator = None
        self.order_executor = None
        
        # Threading
        self.main_thread = None
        self.monitoring_thread = None
        
        # Performance tracking
        self.cycle_count = 0
        self.total_signals = 0
        self.total_trades = 0
        self.start_time = None
        
        # Trading statistics
        self.trading_stats = {
            "cycles_completed": 0,
            "signals_generated": 0,
            "trades_executed": 0,
            "total_pnl": 0.0,
            "win_rate": 0.0,
            "avg_trade_duration": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0
        }
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("Trading Engine initialized")
        
    def initialize_components(self) -> bool:
        """Initialize all trading system components."""
        try:
            logger.info("Initializing system components...")
            
            # Initialize data collector
            logger.info("Initializing Data Collector...")
            self.data_collector = SyntheticDataCollector()
            if not self.data_collector.mt5_connected:
                logger.error("Failed to connect to MT5")
                return False
            logger.info("Data Collector initialized")

            # Initialize pattern detector
            logger.info("Initializing Pattern Detector...")
            self.pattern_detector = SyntheticPatternDetector(self.data_collector)
            logger.info("Pattern Detector initialized")

            # Initialize AI model manager
            logger.info("Initializing AI Model Manager...")
            self.ai_manager = AIModelManager(self.data_collector, self.pattern_detector)

            # Load existing models or prompt for training
            model_status = self.ai_manager.get_model_status()
            if len(model_status["loaded_models"]) == 0:
                logger.warning("No trained models found!")
                logger.warning("Please run 'python train_all_models.py' first to train the AI models.")
                logger.warning("This will train all 9 models and save them for instant loading.")

                # Try to load any saved models from disk
                logger.info("Attempting to load any saved models from disk...")
                loaded_count = 0
                for model_name in self.ai_manager.model_configs.keys():
                    if self.ai_manager.load_model(model_name):
                        loaded_count += 1
                        logger.info(f"Loaded saved model: {model_name}")

                if loaded_count == 0:
                    logger.error("No saved models found. Please train models first using 'python train_all_models.py'")
                    return False
                else:
                    logger.info(f"Successfully loaded {loaded_count} saved AI models")
            else:
                logger.info(f"Loaded {len(model_status['loaded_models'])} existing AI models")

            # Initialize signal generator
            logger.info("Initializing Signal Generator...")
            self.signal_generator = TradingSignalGenerator(
                self.ai_manager, self.pattern_detector, self.data_collector, self
            )
            logger.info("Signal Generator initialized")

            # Initialize order executor
            logger.info("Initializing Order Executor...")
            self.order_executor = OrderExecutionSystem(self.data_collector, trading_engine=self)
            if not self.order_executor.mt5_connected:
                logger.error("Failed to connect Order Executor to MT5")
                return False
            logger.info("Order Executor initialized")

            logger.info("All components initialized successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing components: {e}")
            return False
            
    def start_trading(self) -> bool:
        """Start the automated trading system."""
        try:
            if self.running:
                logger.warning("Trading system is already running")
                return False
                
            # Initialize components if not already done
            if self.data_collector is None:
                if not self.initialize_components():
                    logger.error("Failed to initialize components")
                    return False
                    
            self.running = True
            self.start_time = datetime.now()
            
            logger.info("Starting automated trading system...")
            logger.info(f"   Symbol: {config.SYMBOL}")
            logger.info(f"   Trading cycle: 3 minutes")
            logger.info(f"   Daily trade limits: REMOVED (unlimited trades)")
            logger.info(f"   Risk per trade: {config.BASE_RISK_PER_TRADE:.1%}")

            # Start main trading loop in separate thread
            self.main_thread = threading.Thread(target=self._main_trading_loop, daemon=False)
            self.main_thread.start()

            # Start monitoring thread
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()

            logger.info("Trading system started successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error starting trading system: {e}")
            return False
            
    def stop_trading(self, reason: str = "Manual stop"):
        """Stop the trading system gracefully."""
        logger.info(f"Stopping trading system. Reason: {reason}")
        
        self.running = False
        self.trading_active = False
        
        # Close all positions if emergency
        if "emergency" in reason.lower():
            if self.order_executor:
                self.order_executor.emergency_stop()
                
        # Wait for threads to finish
        if self.main_thread and self.main_thread.is_alive():
            logger.info("Waiting for main trading loop to finish...")
            self.main_thread.join(timeout=30)
            
        # Cleanup components
        self._cleanup_components()
        
        # Log final statistics
        self._log_final_statistics()
        
        logger.info("Trading system stopped")
        
    def _main_trading_loop(self):
        """Main trading loop - runs every 5 minutes."""
        logger.info("Main trading loop started")
        
        while self.running and not self.emergency_stop_triggered:
            try:
                cycle_start = time.time()
                
                # Execute trading cycle
                self._execute_trading_cycle()
                
                # Update cycle count
                self.cycle_count += 1
                self.trading_stats["cycles_completed"] = self.cycle_count
                
                # Calculate cycle duration
                cycle_duration = time.time() - cycle_start
                
                # Log cycle completion
                if self.cycle_count % 10 == 0:  # Log every 10 cycles
                    logger.info(f"Completed cycle {self.cycle_count} in {cycle_duration:.2f}s")
                    
                # Wait for next cycle (3 minutes = 180 seconds)
                sleep_time = 180 - cycle_duration
                if sleep_time > 0:
                    time.sleep(sleep_time)
                else:
                    logger.warning(f"Cycle took longer than 3 minutes: {cycle_duration:.2f}s")
                    
            except Exception as e:
                logger.error(f"Error in main trading loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying
                
        logger.info("Main trading loop ended")
        
    def _execute_trading_cycle(self):
        """Execute one complete trading cycle."""
        try:
            # Check if trading is active
            if not self.trading_active:
                return
                
            # Get current price
            current_price = self.order_executor._get_current_price()
            if current_price is None:
                logger.warning("Could not get current price, skipping cycle")
                return
                
            # Generate trading signal
            signal = self.signal_generator.generate_signal(current_price)
            
            if signal:
                self.total_signals += 1
                self.trading_stats["signals_generated"] = self.total_signals
                
                logger.info(f"Generated {signal.signal_type.name} signal:")
                logger.info(f"  Confidence: {signal.confidence:.3f}")
                logger.info(f"  Entry: {signal.entry_price:.2f}")
                logger.info(f"  Stop Loss: {signal.stop_loss:.2f}")
                logger.info(f"  Take Profit: {signal.take_profit:.2f}")
                logger.info(f"  Risk/Reward: {signal.risk_reward_ratio:.2f}")
                logger.info(f"  Reasoning: {signal.reasoning}")
                
                # Execute the signal
                order = self.order_executor.execute_signal(signal)
                
                if order and order.status.name == "FILLED":
                    self.total_trades += 1
                    self.trading_stats["trades_executed"] = self.total_trades
                    logger.info(f"Trade executed: Order {order.order_id}")
                elif order:
                    logger.warning(f"Trade failed: {order.status.name}")
                else:
                    logger.warning("Trade execution failed")
            else:
                logger.debug("No trading signal generated this cycle")
                
        except Exception as e:
            logger.error(f"Error in trading cycle: {e}")

    def _execute_signal(self, signal):
        """Execute a trading signal directly (used by signal generator for multiple signals)."""
        try:
            if not self.order_executor:
                logger.error("Order executor not available")
                return None

            # Execute the signal
            order = self.order_executor.execute_signal(signal)

            if order and order.status.name == "FILLED":
                self.total_trades += 1
                self.trading_stats["trades_executed"] = self.total_trades
                logger.info(f"Direct signal execution successful: Order {order.order_id}")
                return order
            elif order:
                logger.warning(f"Direct signal execution failed: {order.status.name}")
                return order
            else:
                logger.warning("Direct signal execution failed - no order returned")
                return None

        except Exception as e:
            logger.error(f"Error executing signal directly: {e}")
            return None

    def _monitoring_loop(self):
        """Monitoring loop for risk management and performance tracking."""
        logger.info("Monitoring loop started")

        # Startup grace period - wait for auto fresh start to take effect
        startup_grace_period = 10  # 10 seconds
        logger.info(f"Startup grace period: {startup_grace_period} seconds (allowing auto fresh start to complete)")
        time.sleep(startup_grace_period)
        logger.info("Startup grace period completed - monitoring active")

        while self.running:
            try:
                # Check emergency conditions
                self._check_emergency_conditions()

                # Update trading statistics
                self._update_trading_statistics()

                # Log status every 3 minutes
                if self.cycle_count % 1 == 0:  # Log every cycle
                    self._log_status()

                # Sleep for 30 seconds
                time.sleep(30)

            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)

        logger.info("Monitoring loop ended")
        
    def reset_daily_emergency_flags(self):
        """Reset emergency stop flags for new trading day."""
        if self.emergency_stop_triggered:
            logger.info("🔄 Daily reset: Clearing emergency stop flag")
            self.emergency_stop_triggered = False
            self.trading_active = True
            logger.info("✅ Emergency stop flag cleared - Trading can resume")

    def _check_emergency_conditions(self):
        """Check for emergency stop conditions."""
        if not self.order_executor:
            return

        # Check daily drawdown
        daily_pnl = self.order_executor.daily_pnl
        max_drawdown = config.SYNTHETIC_RISK_RULES["circuit_breakers"]["max_daily_drawdown"]

        if daily_pnl < -max_drawdown:
            logger.critical(f"EMERGENCY: Daily drawdown limit exceeded: {daily_pnl:.3f}")
            self.emergency_stop_triggered = True
            self.stop_trading("Emergency: Daily drawdown limit exceeded")
            return

        # Check daily profit limit
        max_profit = config.SYNTHETIC_RISK_RULES["circuit_breakers"]["max_daily_profit"]

        if daily_pnl > max_profit:
            logger.critical(f"EMERGENCY: Daily profit limit exceeded: {daily_pnl:.3f}")
            self.emergency_stop_triggered = True
            self.stop_trading("Emergency: Daily profit limit exceeded")
            return
            
        # Check consecutive losses (simplified check)
        execution_stats = self.order_executor.get_execution_statistics()
        if execution_stats["daily_trade_count"] > 10:  # Only check after some trades
            recent_pnl = daily_pnl
            if recent_pnl < -0.01:  # 1% loss threshold for emergency
                logger.warning(f"High losses detected: {recent_pnl:.3f}")
                
    def _update_trading_statistics(self):
        """Update comprehensive trading statistics."""
        if not self.order_executor:
            return
            
        try:
            # Get execution statistics
            exec_stats = self.order_executor.get_execution_statistics()
            position_summary = self.order_executor.get_position_summary()
            
            # Update basic stats
            self.trading_stats.update({
                "cycles_completed": self.cycle_count,
                "signals_generated": self.total_signals,
                "trades_executed": self.total_trades,
                "total_pnl": exec_stats["daily_pnl"],
                "active_positions": exec_stats["active_positions"],
                "fill_rate": exec_stats["fill_rate"],
                "avg_execution_time_ms": exec_stats["avg_execution_time_ms"]
            })
            
            # Calculate win rate (simplified)
            if self.total_trades > 0:
                # This is a simplified calculation - in production you'd track individual trade outcomes
                win_rate = max(0, min(1, (exec_stats["daily_pnl"] + 0.01) / 0.02))  # Rough estimate
                self.trading_stats["win_rate"] = win_rate
                
        except Exception as e:
            logger.error(f"Error updating statistics: {e}")
            
    def _log_status(self):
        """Log current system status."""
        if not self.order_executor:
            return
            
        try:
            exec_stats = self.order_executor.get_execution_statistics()
            
            logger.info("=" * 60)
            logger.info("TRADING SYSTEM STATUS")
            logger.info("=" * 60)
            logger.info(f"Uptime: {datetime.now() - self.start_time if self.start_time else 'Unknown'}")
            logger.info(f"Cycles completed: {self.cycle_count}")
            logger.info(f"Signals generated: {self.total_signals}")
            logger.info(f"Trades executed: {self.total_trades}")
            logger.info(f"Daily P&L: {exec_stats['daily_pnl']:.4f}")
            logger.info(f"Active positions: {exec_stats['active_positions']}")
            logger.info(f"Fill rate: {exec_stats['fill_rate']:.1%}")
            logger.info(f"Avg execution time: {exec_stats['avg_execution_time_ms']:.1f}ms")
            logger.info("=" * 60)
            
        except Exception as e:
            logger.error(f"Error logging status: {e}")
            
    def _signal_handler(self, signum, frame):
        """Handle system signals for graceful shutdown."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.stop_trading("System signal received")
        
    def _cleanup_components(self):
        """Clean up all system components."""
        logger.info("Cleaning up system components...")
        
        try:
            if self.order_executor:
                self.order_executor.cleanup()
                
            if self.ai_manager:
                self.ai_manager.cleanup()
                
            if self.data_collector:
                self.data_collector.stop_real_time_collection()
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            
    def _log_final_statistics(self):
        """Log final trading statistics."""
        logger.info("=" * 80)
        logger.info("FINAL TRADING STATISTICS")
        logger.info("=" * 80)
        
        if self.start_time:
            total_runtime = datetime.now() - self.start_time
            logger.info(f"Total runtime: {total_runtime}")
            
        for key, value in self.trading_stats.items():
            if isinstance(value, float):
                logger.info(f"{key}: {value:.4f}")
            else:
                logger.info(f"{key}: {value}")
                
        logger.info("=" * 80)
        
    def get_system_status(self) -> Dict:
        """Get comprehensive system status."""
        status = {
            "running": self.running,
            "trading_active": self.trading_active,
            "emergency_stop": self.emergency_stop_triggered,
            "uptime": str(datetime.now() - self.start_time) if self.start_time else "Not started",
            "components_initialized": all([
                self.data_collector is not None,
                self.pattern_detector is not None,
                self.ai_manager is not None,
                self.signal_generator is not None,
                self.order_executor is not None
            ]),
            "trading_stats": self.trading_stats.copy()
        }
        
        # Add component-specific status
        if self.order_executor:
            status["execution_stats"] = self.order_executor.get_execution_statistics()
            status["position_summary"] = self.order_executor.get_position_summary()
            
        if self.ai_manager:
            status["ai_model_status"] = self.ai_manager.get_model_status()
            
        return status
