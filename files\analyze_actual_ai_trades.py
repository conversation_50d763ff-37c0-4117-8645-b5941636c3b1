#!/usr/bin/env python3
"""
Analyze actual AI system trades based on the specific comment patterns shown in MT5.
"""

import MetaTrader5 as mt5
from datetime import datetime

def analyze_actual_ai_trades():
    """Analyze trades to find ONLY the current AI system trades."""
    print("🔍 Analyzing ACTUAL AI System Trades...")
    
    # Initialize MT5
    if not mt5.initialize():
        print('❌ MT5 initialization failed')
        return

    # Get all historical data from May 1st
    start_time = datetime(2025, 5, 1)
    end_time = datetime.now()

    print(f'📅 Checking deals from {start_time} to {end_time}')

    # Get deals (closed trades) from MT5
    deals = mt5.history_deals_get(start_time, end_time, group='*')

    if deals is None:
        print('❌ No deals found')
        mt5.shutdown()
        return

    print(f'📊 Total deals found: {len(deals)}')
    
    # Based on your MT5 screenshot, the ACTUAL AI system trades have these patterns:
    actual_ai_patterns = [
        'AI_Signal_WEAK_B',
        'AI_Signal_WEAK_S', 
        'AI_Signal_STRONG',
        'Simple_Test_Orde',
        'SL_TP_Test_Orde'
    ]
    
    actual_ai_trades = 0
    old_system_trades = 0
    manual_trades = 0
    other_trades = 0
    
    actual_ai_pnl = 0.0
    actual_ai_wins = 0
    actual_ai_losses = 0
    
    print('\n🤖 === ACTUAL AI SYSTEM TRADES ===')
    for deal in deals:
        if (hasattr(deal, 'magic') and deal.magic == 12345 and
            hasattr(deal, 'symbol') and deal.symbol == 'DEX 900 DOWN Index' and
            hasattr(deal, 'entry') and deal.entry == 1):
            
            comment = getattr(deal, 'comment', '')
            time_str = datetime.fromtimestamp(deal.time).strftime('%Y-%m-%d %H:%M:%S')
            
            # Check if this is an ACTUAL AI system trade
            is_actual_ai = any(pattern in comment for pattern in actual_ai_patterns)
            
            if is_actual_ai:
                print(f'  ✅ Profit={deal.profit:.2f}, Comment="{comment}", Time={time_str}')
                actual_ai_trades += 1
                actual_ai_pnl += deal.profit
                if deal.profit > 0:
                    actual_ai_wins += 1
                elif deal.profit < 0:
                    actual_ai_losses += 1
    
    print(f'\n📊 OLD SYSTEM TRADES (Magic=12345, but different comments) ===')
    for deal in deals:
        if (hasattr(deal, 'magic') and deal.magic == 12345 and
            hasattr(deal, 'symbol') and deal.symbol == 'DEX 900 DOWN Index' and
            hasattr(deal, 'entry') and deal.entry == 1):
            
            comment = getattr(deal, 'comment', '')
            time_str = datetime.fromtimestamp(deal.time).strftime('%Y-%m-%d %H:%M:%S')
            
            # Check if this is an OLD system trade (not current AI)
            is_actual_ai = any(pattern in comment for pattern in actual_ai_patterns)
            
            if not is_actual_ai:
                print(f'  🔸 Profit={deal.profit:.2f}, Comment="{comment}", Time={time_str}')
                old_system_trades += 1
    
    actual_ai_win_rate = (actual_ai_wins / actual_ai_trades * 100) if actual_ai_trades > 0 else 0.0
    
    print(f'\n📈 SUMMARY:')
    print(f'🤖 ACTUAL AI System trades: {actual_ai_trades}')
    print(f'📊 Total P&L: ${actual_ai_pnl:.2f}')
    print(f'🏆 Winning trades: {actual_ai_wins}')
    print(f'📉 Losing trades: {actual_ai_losses}')
    print(f'📊 Win rate: {actual_ai_win_rate:.1f}%')
    print(f'🔸 Old system trades (Magic=12345): {old_system_trades}')
    print(f'📊 Total Magic=12345 trades: {actual_ai_trades + old_system_trades}')
    
    if actual_ai_trades != 244:
        print(f'\n⚠️  ISSUE: Dashboard shows 244 trades but actual AI system has {actual_ai_trades} trades!')
        print('🔧 Need to update filtering to show ONLY actual AI system trades.')
    else:
        print(f'\n✅ CORRECT: Dashboard correctly shows {actual_ai_trades} actual AI system trades.')

    mt5.shutdown()

if __name__ == "__main__":
    analyze_actual_ai_trades()
