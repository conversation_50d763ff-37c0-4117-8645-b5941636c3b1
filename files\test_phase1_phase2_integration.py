"""
Comprehensive Integration Test for Phase 1 + Phase 2
Tests the complete pipeline from data collection to AI predictions.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.getcwd())

import config
from synthetic_data_collector import SyntheticDataCollector
from synthetic_pattern_detector import SyntheticPatternDetector
from ai_model_manager import AIModelManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_complete_integration():
    """Test complete Phase 1 + Phase 2 integration."""
    print("=" * 80)
    print("PHASE 1 + PHASE 2 INTEGRATION TEST")
    print("SYNTHETIC DEX 900 DOWN AI TRADING SYSTEM")
    print("=" * 80)
    print()
    
    start_time = time.time()
    
    try:
        # ========================================
        # PHASE 1: INITIALIZATION & DATA COLLECTION
        # ========================================
        print("🔄 PHASE 1: INITIALIZING DATA COLLECTION SYSTEM")
        print("=" * 60)
        
        # Step 1: Initialize Data Collector
        print("Step 1: Initializing Data Collector...")
        data_collector = SyntheticDataCollector()
        print(f"✅ Data Collector initialized")
        print(f"   MT5 Connected: {data_collector.mt5_connected}")
        print(f"   Symbol: {config.SYMBOL}")
        print()
        
        # Step 2: Initialize Pattern Detector
        print("Step 2: Initializing Pattern Detector...")
        pattern_detector = SyntheticPatternDetector(data_collector)
        print(f"✅ Pattern Detector initialized")
        print(f"   Regimes configured: {len(pattern_detector.regimes)}")
        print()
        
        # Step 3: Collect Historical Data
        print("Step 3: Collecting Historical Data...")
        historical_data = {}
        for timeframe in [15, 30, 60]:  # Test key timeframes
            print(f"   Collecting {timeframe}min data...")
            df = data_collector.get_latest_data(timeframe, count=1000)
            if not df.empty:
                historical_data[timeframe] = df
                print(f"   ✅ {timeframe}min: {len(df)} records")
            else:
                print(f"   ⚠️ {timeframe}min: No data")
        
        total_records = sum(len(df) for df in historical_data.values())
        print(f"✅ Historical data collected: {total_records} total records")
        print()
        
        # Step 4: Test Pattern Detection
        print("Step 4: Testing Pattern Detection...")
        pattern_results = {}
        for timeframe, df in historical_data.items():
            print(f"   Analyzing {timeframe}min patterns...")
            df_with_indicators = pattern_detector.calculate_synthetic_indicators(df)
            
            # Get latest regime and indicators
            latest_regime = df_with_indicators.iloc[-1]['regime_state']
            regime_name = pattern_detector.regimes.get(latest_regime, 'UNKNOWN')
            jumpiness = df_with_indicators.iloc[-1]['jumpiness_score']
            volatility = df_with_indicators.iloc[-1]['volatility']
            
            pattern_results[timeframe] = {
                'regime': regime_name,
                'jumpiness': jumpiness,
                'volatility': volatility,
                'indicators_count': len([col for col in df_with_indicators.columns if col not in df.columns])
            }
            
            print(f"   ✅ {timeframe}min: {regime_name} regime, {pattern_results[timeframe]['indicators_count']} indicators")
        
        print(f"✅ Pattern detection working across all timeframes")
        print()
        
        # ========================================
        # PHASE 2: AI MODEL INITIALIZATION
        # ========================================
        print("🤖 PHASE 2: INITIALIZING AI MODEL SYSTEM")
        print("=" * 60)
        
        # Step 5: Initialize AI Model Manager
        print("Step 5: Initializing AI Model Manager...")
        ai_manager = AIModelManager(data_collector, pattern_detector)
        print(f"✅ AI Model Manager initialized")
        print(f"   Models configured: {len(ai_manager.model_configs)}")
        
        from ai_model_manager import TENSORFLOW_AVAILABLE
        print(f"   TensorFlow available: {TENSORFLOW_AVAILABLE}")
        print()
        
        # Step 6: Test Data Preparation for AI
        print("Step 6: Testing AI Data Preparation...")
        test_models = ["short_term_momentum_rf", "medium_term_breakout_rf"]
        data_prep_results = {}
        
        for model_name in test_models:
            print(f"   Preparing data for {model_name}...")
            try:
                X, y = ai_manager.prepare_training_data(model_name)
                data_prep_results[model_name] = {
                    'samples': X.shape[0],
                    'features': X.shape[1],
                    'target_classes': len(np.unique(y)),
                    'success': True
                }
                print(f"   ✅ {model_name}: {X.shape[0]} samples, {X.shape[1]} features")
            except Exception as e:
                data_prep_results[model_name] = {'success': False, 'error': str(e)}
                print(f"   ❌ {model_name}: {e}")
        
        successful_prep = sum(1 for result in data_prep_results.values() if result['success'])
        print(f"✅ Data preparation: {successful_prep}/{len(test_models)} models ready")
        print()
        
        # Step 7: Train AI Models
        print("Step 7: Training AI Models...")
        training_results = {}
        
        for model_name in test_models:
            if data_prep_results[model_name]['success']:
                print(f"   Training {model_name}...")
                start_train = time.time()
                success = ai_manager.train_model(model_name)
                train_time = time.time() - start_train
                
                if success:
                    performance = ai_manager.model_performance.get(model_name, {})
                    training_results[model_name] = {
                        'success': True,
                        'accuracy': performance.get('accuracy', 0),
                        'samples': performance.get('total_samples', 0),
                        'train_time': train_time
                    }
                    print(f"   ✅ {model_name}: {performance.get('accuracy', 0):.3f} accuracy ({train_time:.1f}s)")
                else:
                    training_results[model_name] = {'success': False}
                    print(f"   ❌ {model_name}: Training failed")
            else:
                print(f"   ⏭️ {model_name}: Skipped (data prep failed)")
        
        successful_training = sum(1 for result in training_results.values() if result['success'])
        print(f"✅ Model training: {successful_training}/{len(test_models)} models trained")
        print()
        
        # ========================================
        # INTEGRATION TESTING
        # ========================================
        print("🔗 INTEGRATION TESTING: REAL-TIME PIPELINE")
        print("=" * 60)
        
        # Step 8: Test Real-time Data → AI Pipeline
        print("Step 8: Testing Real-time Data → AI Prediction Pipeline...")
        
        if successful_training > 0:
            # Get latest real-time data
            print("   Fetching latest market data...")
            latest_data = data_collector.get_latest_data(timeframe=15, count=100)
            
            if not latest_data.empty:
                print(f"   ✅ Latest data: {len(latest_data)} records")
                
                # Calculate indicators
                print("   Calculating synthetic indicators...")
                df_indicators = pattern_detector.calculate_synthetic_indicators(latest_data)
                print(f"   ✅ Indicators calculated: {len(df_indicators.columns)} total columns")
                
                # Extract features for AI
                print("   Extracting features for AI prediction...")
                latest_row = df_indicators.iloc[-1]
                
                # Test each trained model
                prediction_results = {}
                for model_name in ai_manager.models.keys():
                    print(f"   Testing {model_name} prediction...")
                    
                    # Get feature count for this model
                    if model_name in ai_manager.scalers:
                        feature_count = ai_manager.scalers[model_name].n_features_in_
                        
                        # Create feature vector (simplified for testing)
                        features = []
                        
                        # Add basic price features
                        if len(latest_data) >= 10:
                            features.extend([
                                latest_row['close'] / latest_data['close'].iloc[-10] - 1,  # 10-period return
                                latest_row['high'] / latest_row['close'] - 1,  # High deviation
                                latest_row['low'] / latest_row['close'] - 1,   # Low deviation
                                latest_data['volume'].iloc[-5:].mean(),        # Recent volume
                                len(latest_data[latest_data['close'] > latest_data['open']]) / len(latest_data)  # Bullish ratio
                            ])
                        
                        # Add synthetic indicators
                        synthetic_features = [
                            latest_row.get('jumpiness_score', 0),
                            latest_row.get('volatility', 0),
                            latest_row.get('price_acceleration', 0),
                            latest_row.get('regime_state', 0) / 4,
                            latest_row.get('volatility_compression', 0)
                        ]
                        features.extend(synthetic_features)
                        
                        # Pad or trim to required feature count
                        while len(features) < feature_count:
                            features.append(0.0)
                        features = features[:feature_count]
                        
                        # Make prediction
                        feature_array = np.array(features)
                        prediction = ai_manager.predict(model_name, feature_array)
                        
                        if prediction:
                            prediction_results[model_name] = prediction
                            print(f"   ✅ {model_name}: Signal {prediction['signal']}, Confidence {prediction['confidence']:.3f}")
                        else:
                            print(f"   ❌ {model_name}: Prediction failed")
                
                # Test ensemble prediction
                if prediction_results:
                    print("   Testing ensemble prediction...")
                    # Use features from first successful model
                    first_model = list(prediction_results.keys())[0]
                    feature_count = ai_manager.scalers[first_model].n_features_in_
                    test_features = np.random.randn(feature_count)  # Simplified for testing
                    
                    ensemble_result = ai_manager.get_ensemble_prediction(test_features)
                    print(f"   ✅ Ensemble: Signal {ensemble_result['ensemble_signal']}, Confidence {ensemble_result['confidence']:.3f}")
                    print(f"      Consensus: {ensemble_result['consensus']}, Models: {ensemble_result.get('total_models', 0)}")
                else:
                    print("   ⚠️ No successful predictions for ensemble test")
                    
            else:
                print("   ❌ No latest data available")
        else:
            print("   ⏭️ Skipping real-time test (no trained models)")
        
        print()
        
        # ========================================
        # PERFORMANCE TESTING
        # ========================================
        print("⚡ PERFORMANCE TESTING")
        print("=" * 60)
        
        # Step 9: Test System Performance
        print("Step 9: Testing System Performance...")
        
        if successful_training > 0:
            # Test prediction speed
            model_name = list(ai_manager.models.keys())[0]
            feature_count = ai_manager.scalers[model_name].n_features_in_
            test_features = np.random.randn(feature_count)
            
            # Time multiple predictions
            prediction_times = []
            for i in range(10):
                start_pred = time.time()
                prediction = ai_manager.predict(model_name, test_features)
                pred_time = time.time() - start_pred
                prediction_times.append(pred_time)
            
            avg_prediction_time = np.mean(prediction_times) * 1000  # Convert to ms
            print(f"   ✅ Average prediction time: {avg_prediction_time:.1f}ms")
            
            # Test data processing speed
            start_data = time.time()
            test_df = data_collector.get_latest_data(timeframe=15, count=100)
            if not test_df.empty:
                df_processed = pattern_detector.calculate_synthetic_indicators(test_df)
            data_time = time.time() - start_data
            
            print(f"   ✅ Data processing time: {data_time*1000:.1f}ms (100 records)")
            
        print()
        
        # ========================================
        # FINAL INTEGRATION SUMMARY
        # ========================================
        total_time = time.time() - start_time
        
        print("=" * 80)
        print("INTEGRATION TEST SUMMARY")
        print("=" * 80)
        
        print(f"🕒 Total test time: {total_time:.1f} seconds")
        print()
        
        print("📊 PHASE 1 RESULTS:")
        print(f"   ✅ Data Collection: {len(historical_data)} timeframes")
        print(f"   ✅ Pattern Detection: {len(pattern_results)} timeframes analyzed")
        print(f"   ✅ Historical Records: {total_records} total")
        print()
        
        print("🤖 PHASE 2 RESULTS:")
        print(f"   ✅ AI Models Configured: {len(ai_manager.model_configs)}")
        print(f"   ✅ Data Preparation: {successful_prep}/{len(test_models)} models")
        print(f"   ✅ Model Training: {successful_training}/{len(test_models)} models")
        
        if training_results:
            avg_accuracy = np.mean([r['accuracy'] for r in training_results.values() if r['success']])
            total_samples = sum([r['samples'] for r in training_results.values() if r['success']])
            print(f"   ✅ Average Accuracy: {avg_accuracy:.3f}")
            print(f"   ✅ Total Training Samples: {total_samples:,}")
        print()
        
        print("🔗 INTEGRATION RESULTS:")
        if successful_training > 0:
            print(f"   ✅ Real-time Pipeline: OPERATIONAL")
            print(f"   ✅ Prediction System: WORKING")
            print(f"   ✅ Ensemble System: WORKING")
            if 'avg_prediction_time' in locals():
                print(f"   ✅ Prediction Speed: {avg_prediction_time:.1f}ms")
        else:
            print(f"   ⚠️ Real-time Pipeline: PARTIAL (no trained models)")
        print()
        
        # Final verdict
        if successful_training >= 1 and total_records > 1000:
            print("🎉 INTEGRATION TEST: ✅ PASSED")
            print("   Phase 1 + Phase 2 integration is FULLY OPERATIONAL")
            print("   System ready for Phase 3 (Trading Integration)")
        elif successful_training >= 1:
            print("⚠️ INTEGRATION TEST: ✅ MOSTLY PASSED")
            print("   Phase 1 + Phase 2 integration is OPERATIONAL")
            print("   Consider collecting more historical data for better performance")
        else:
            print("❌ INTEGRATION TEST: FAILED")
            print("   Issues detected that need resolution before proceeding")
        
        print()
        
        # Cleanup
        print("🧹 CLEANUP")
        print("=" * 60)
        ai_manager.cleanup()
        data_collector.stop_real_time_collection()
        print("✅ Cleanup completed")
        
        return successful_training >= 1 and total_records > 1000
        
    except Exception as e:
        print(f"❌ Integration test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_integration()
    exit(0 if success else 1)
