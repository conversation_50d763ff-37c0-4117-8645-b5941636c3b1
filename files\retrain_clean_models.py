#!/usr/bin/env python3
"""
Retrain Models with Clean Features
Retrain all volume-contaminated models with new clean features.
"""

import sys
import os
import time
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/clean_model_retraining.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('CleanModelRetraining')

def setup_retraining_environment():
    """Setup environment for clean model retraining."""
    print("🔧 SETTING UP RETRAINING ENVIRONMENT")
    print("=" * 60)
    
    try:
        # Ensure logs directory exists
        os.makedirs('logs', exist_ok=True)
        
        # Create backup directory for old models
        backup_dir = f"models/backup_volume_contaminated_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        
        print(f"✅ Logs directory ready")
        print(f"✅ Backup directory created: {backup_dir}")
        
        return backup_dir
        
    except Exception as e:
        print(f"❌ Environment setup failed: {e}")
        return None

def backup_contaminated_models(backup_dir):
    """Backup existing volume-contaminated models."""
    print(f"\n💾 BACKING UP CONTAMINATED MODELS")
    print("=" * 60)
    
    contaminated_models = [
        "short_term_pattern_nn",
        "short_term_momentum_rf", 
        "medium_term_trend_lstm",
        "medium_term_breakout_rf"
    ]
    
    models_dir = "models/saved"
    backed_up = 0
    
    try:
        if os.path.exists(models_dir):
            for model_name in contaminated_models:
                model_files = [
                    f"{model_name}.pkl",
                    f"{model_name}.keras",
                    f"{model_name}_scaler.pkl"
                ]
                
                for model_file in model_files:
                    source_path = os.path.join(models_dir, model_file)
                    if os.path.exists(source_path):
                        backup_path = os.path.join(backup_dir, model_file)
                        
                        # Copy file
                        import shutil
                        shutil.copy2(source_path, backup_path)
                        
                        print(f"   ✅ Backed up: {model_file}")
                        backed_up += 1
                    else:
                        print(f"   ⚠️  Not found: {model_file}")
        
        print(f"\n📊 Backup summary: {backed_up} files backed up")
        return True
        
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return False

def initialize_training_components():
    """Initialize components needed for training."""
    print(f"\n🔧 INITIALIZING TRAINING COMPONENTS")
    print("=" * 60)
    
    try:
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        
        print("   Initializing Data Collector...")
        data_collector = SyntheticDataCollector()
        print("   ✅ Data Collector initialized")
        
        print("   Initializing Pattern Detector...")
        pattern_detector = SyntheticPatternDetector(data_collector)
        print("   ✅ Pattern Detector initialized")
        
        print("   Initializing AI Model Manager...")
        ai_manager = AIModelManager(data_collector, pattern_detector)
        print("   ✅ AI Model Manager initialized")
        
        return data_collector, pattern_detector, ai_manager
        
    except Exception as e:
        print(f"❌ Component initialization failed: {e}")
        return None, None, None

def retrain_single_model(ai_manager, model_name):
    """Retrain a single model with clean features."""
    print(f"\n🧠 RETRAINING {model_name.upper()}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # Get model configuration
        config = ai_manager.model_configs.get(model_name, {})
        features = config.get("features", [])
        purpose = config.get("purpose", "Unknown")
        
        print(f"   Purpose: {purpose}")
        print(f"   Clean Features: {features}")
        
        # Check for clean features
        clean_features = ["volatility_regime", "volatility_adjusted_momentum", "trend_sustainability", "false_breakout_filter"]
        has_clean_features = any(cf in features for cf in clean_features)
        
        if not has_clean_features:
            print(f"   ⚠️  Model doesn't use clean features - skipping")
            return True
        
        print(f"   ✅ Model uses clean features - proceeding with retraining")
        
        # Train the model
        print(f"   🔄 Starting training...")
        success = ai_manager.train_model(model_name)
        
        training_time = time.time() - start_time
        
        if success:
            print(f"   ✅ Training completed successfully in {training_time:.1f}s")
            
            # Validate the model
            print(f"   🔍 Validating model...")
            model_loaded = ai_manager.load_model(model_name)
            
            if model_loaded:
                print(f"   ✅ Model validation successful")
                return True
            else:
                print(f"   ❌ Model validation failed")
                return False
        else:
            print(f"   ❌ Training failed after {training_time:.1f}s")
            return False
            
    except Exception as e:
        training_time = time.time() - start_time
        print(f"   ❌ Training error after {training_time:.1f}s: {e}")
        return False

def retrain_all_clean_models():
    """Retrain all models with clean features."""
    print(f"\n🚀 RETRAINING ALL CLEAN MODELS")
    print("=" * 60)
    
    # Setup environment
    backup_dir = setup_retraining_environment()
    if not backup_dir:
        return False
    
    # Backup contaminated models
    if not backup_contaminated_models(backup_dir):
        return False
    
    # Initialize components
    data_collector, pattern_detector, ai_manager = initialize_training_components()
    if not ai_manager:
        return False
    
    # Models to retrain (volume-contaminated models)
    models_to_retrain = [
        "short_term_pattern_nn",      # volume → volatility_regime
        "short_term_momentum_rf",     # volume_velocity → volatility_adjusted_momentum  
        "medium_term_trend_lstm",     # volume_trend → trend_sustainability
        "medium_term_breakout_rf"     # volume_breakout → false_breakout_filter
    ]
    
    print(f"📋 Models to retrain: {len(models_to_retrain)}")
    for model in models_to_retrain:
        print(f"   • {model}")
    
    # Retrain each model
    successful_retrains = 0
    failed_retrains = 0
    total_start_time = time.time()
    
    for i, model_name in enumerate(models_to_retrain, 1):
        print(f"\n🔄 RETRAINING {i}/{len(models_to_retrain)}: {model_name}")
        print("=" * 60)
        
        success = retrain_single_model(ai_manager, model_name)
        
        if success:
            successful_retrains += 1
            print(f"✅ {model_name} retraining SUCCESSFUL")
        else:
            failed_retrains += 1
            print(f"❌ {model_name} retraining FAILED")
        
        # Brief pause between models
        if i < len(models_to_retrain):
            print(f"⏳ Pausing 5 seconds before next model...")
            time.sleep(5)
    
    total_time = time.time() - total_start_time
    
    # Generate summary
    print(f"\n📊 RETRAINING SUMMARY")
    print("=" * 50)
    print(f"Total models: {len(models_to_retrain)}")
    print(f"Successful: {successful_retrains}")
    print(f"Failed: {failed_retrains}")
    print(f"Success rate: {successful_retrains/len(models_to_retrain)*100:.1f}%")
    print(f"Total time: {total_time/60:.1f} minutes")
    print(f"Backup location: {backup_dir}")
    
    if successful_retrains == len(models_to_retrain):
        print(f"\n🎉 ALL MODELS RETRAINED SUCCESSFULLY!")
        print("✅ Volume contamination eliminated")
        print("✅ Clean features implemented")
        print("✅ Models ready for improved trading")
        return True
    else:
        print(f"\n⚠️  PARTIAL RETRAINING SUCCESS")
        print(f"❌ {failed_retrains} models need attention")
        return False

def validate_clean_models():
    """Validate that retrained models work properly."""
    print(f"\n🔍 VALIDATING CLEAN MODELS")
    print("=" * 60)
    
    try:
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        from trading_signal_generator import TradingSignalGenerator
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(data_collector, ai_manager, pattern_detector)
        
        # Get test data
        print("📈 Getting test data...")
        test_data = data_collector.get_latest_data(5, 100)
        
        if test_data.empty:
            print("❌ No test data available")
            return False
        
        print(f"✅ Got {len(test_data)} data points")
        
        # Test signal generation
        print("🧠 Testing signal generation...")
        df_indicators = pattern_detector.calculate_synthetic_indicators(test_data)
        
        if df_indicators.empty:
            print("❌ No indicators calculated")
            return False
        
        # Test feature extraction for retrained models
        retrained_models = ["short_term_pattern_nn", "short_term_momentum_rf", 
                           "medium_term_trend_lstm", "medium_term_breakout_rf"]
        
        all_working = True
        for model_name in retrained_models:
            try:
                features = signal_generator._extract_features_for_ai(df_indicators)
                
                if model_name in features and len(features[model_name]) > 0:
                    print(f"   ✅ {model_name}: {len(features[model_name])} features extracted")
                else:
                    print(f"   ❌ {model_name}: No features extracted")
                    all_working = False
                    
            except Exception as e:
                print(f"   ❌ {model_name}: Error - {e}")
                all_working = False
        
        if all_working:
            print(f"\n✅ ALL CLEAN MODELS VALIDATED SUCCESSFULLY!")
        else:
            print(f"\n❌ Some models need attention")
        
        return all_working
        
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

def main():
    """Main retraining function."""
    print("🚀 CLEAN MODEL RETRAINING - PHASE 1 COMPLETION")
    print("=" * 80)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Retrain all clean models
    retraining_success = retrain_all_clean_models()
    
    if retraining_success:
        # Validate the retrained models
        validation_success = validate_clean_models()
        
        if validation_success:
            print(f"\n🎉 PHASE 1 VOLUME REMOVAL COMPLETE!")
            print("=" * 50)
            print("✅ All volume contamination eliminated")
            print("✅ Clean features successfully implemented")
            print("✅ Models retrained and validated")
            print("✅ System ready for improved performance")
            print("🚀 Ready to proceed with Phase 2 enhancements!")
            return True
        else:
            print(f"\n⚠️  RETRAINING SUCCESSFUL BUT VALIDATION ISSUES")
            print("🔧 Models retrained but need validation fixes")
            return False
    else:
        print(f"\n❌ RETRAINING INCOMPLETE")
        print("🔧 Some models failed to retrain properly")
        return False

if __name__ == "__main__":
    success = main()
    input(f"\nPress Enter to exit...")
    sys.exit(0 if success else 1)
