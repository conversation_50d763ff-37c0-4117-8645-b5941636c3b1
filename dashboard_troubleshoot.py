#!/usr/bin/env python3
"""
Dashboard troubleshooting utility for the AI Trading System.
"""

import sys
import time
import subprocess
import logging
import requests
import os

# Add current directory to path
sys.path.append('.')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dashboard_status():
    """Check current dashboard status."""
    logger.info("🔍 Checking dashboard status...")
    
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            logger.info("✅ Dashboard is running and accessible")
            logger.info(f"   Status: {response.status_code}")
            logger.info(f"   Response size: {len(response.text)} characters")
            return True
        else:
            logger.warning(f"⚠️ Dashboard responded with status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        logger.error("❌ Dashboard is not accessible (connection refused)")
        return False
    except requests.exceptions.Timeout:
        logger.error("❌ Dashboard connection timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False

def check_dashboard_process():
    """Check if dashboard process is running."""
    logger.info("🔍 Checking dashboard processes...")
    
    try:
        # Check for Python processes
        result = subprocess.run([
            'tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'
        ], capture_output=True, text=True, shell=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            python_processes = len(lines) - 1  # Subtract header
            logger.info(f"Found {python_processes} Python processes running")
            return python_processes > 0
        else:
            logger.error("Failed to check running processes")
            return False
            
    except Exception as e:
        logger.error(f"Error checking processes: {e}")
        return False

def start_dashboard():
    """Start the dashboard server."""
    logger.info("🚀 Starting dashboard server...")
    
    try:
        # Check if dashboard_server.py exists
        if not os.path.exists('dashboard_server.py'):
            logger.error("❌ dashboard_server.py not found in current directory")
            return False
        
        # Start dashboard server
        process = subprocess.Popen([
            sys.executable, 'dashboard_server.py'
        ])
        
        logger.info(f"Started dashboard process with PID: {process.pid}")
        logger.info("⏳ Waiting for dashboard to initialize (this may take 1-2 minutes)...")
        
        # Wait and check periodically
        for i in range(24):  # Check for up to 2 minutes
            time.sleep(5)
            
            if process.poll() is not None:
                logger.error("❌ Dashboard process terminated unexpectedly")
                return False
            
            # Test connection
            if check_dashboard_status():
                logger.info("✅ Dashboard started successfully!")
                return True
            
            if i % 6 == 0:  # Log every 30 seconds
                logger.info(f"   Still waiting... ({(i+1)*5}s elapsed)")
        
        logger.error("❌ Dashboard failed to start within 2 minutes")
        return False
        
    except Exception as e:
        logger.error(f"Error starting dashboard: {e}")
        return False

def test_system_orchestrator():
    """Test SystemOrchestrator dashboard validation."""
    logger.info("🔍 Testing SystemOrchestrator dashboard validation...")
    
    try:
        from system_orchestrator import AITradingSystemOrchestrator
        
        orchestrator = AITradingSystemOrchestrator()
        result = orchestrator.validate_dashboard_connection()
        
        if result:
            logger.info("✅ SystemOrchestrator validation PASSED")
        else:
            logger.error("❌ SystemOrchestrator validation FAILED")
        
        return result
        
    except Exception as e:
        logger.error(f"Error testing SystemOrchestrator: {e}")
        return False

def main():
    """Main troubleshooting function."""
    logger.info("=" * 60)
    logger.info("DASHBOARD TROUBLESHOOTING UTILITY")
    logger.info("=" * 60)
    
    # Step 1: Check current status
    logger.info("\n1. Checking current dashboard status...")
    dashboard_running = check_dashboard_status()
    
    # Step 2: Check processes
    logger.info("\n2. Checking dashboard processes...")
    processes_running = check_dashboard_process()
    
    # Step 3: Analysis and action
    if dashboard_running:
        logger.info("\n✅ Dashboard is working correctly!")
        
        # Test SystemOrchestrator
        logger.info("\n3. Testing SystemOrchestrator integration...")
        orchestrator_works = test_system_orchestrator()
        
        if orchestrator_works:
            logger.info("\n🎉 All dashboard components are working correctly!")
            logger.info("Dashboard URL: http://localhost:5000")
            return True
        else:
            logger.error("\n⚠️ Dashboard works but SystemOrchestrator validation fails")
            return False
    
    elif processes_running:
        logger.warning("\n⚠️ Dashboard processes are running but not accessible")
        logger.info("This might indicate the dashboard is still starting up...")
        logger.info("Waiting 30 seconds and checking again...")
        
        time.sleep(30)
        if check_dashboard_status():
            logger.info("✅ Dashboard is now accessible!")
            return True
        else:
            logger.error("❌ Dashboard still not accessible after waiting")
            return False
    
    else:
        logger.error("\n❌ Dashboard is not running")
        
        # Offer to start it
        try:
            response = input("Would you like to start the dashboard? (y/N): ").strip().lower()
            if response == 'y':
                logger.info("\n3. Starting dashboard...")
                if start_dashboard():
                    logger.info("\n4. Testing SystemOrchestrator integration...")
                    orchestrator_works = test_system_orchestrator()
                    
                    if orchestrator_works:
                        logger.info("\n🎉 Dashboard started and all components working!")
                        return True
                    else:
                        logger.error("\n⚠️ Dashboard started but SystemOrchestrator validation fails")
                        return False
                else:
                    logger.error("\n❌ Failed to start dashboard")
                    return False
            else:
                logger.info("Dashboard startup cancelled by user")
                return False
        except KeyboardInterrupt:
            logger.info("\nOperation cancelled by user")
            return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ DASHBOARD TROUBLESHOOTING COMPLETED SUCCESSFULLY")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ DASHBOARD ISSUES DETECTED")
        print("Please check the logs above for specific problems")
        print("=" * 60)
    
    sys.exit(0 if success else 1)
