#!/usr/bin/env python3
"""
Test script to verify dashboard updates:
1. Removal of max drawdown and profit factor blocks
2. Updated daily trade limits (Short: 20, Medium: 20, Long: 5)
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config

def test_dashboard_updates():
    """Test the dashboard updates and trade limit changes."""
    print("🧪 TESTING DASHBOARD UPDATES & TRADE LIMITS")
    print("=" * 60)
    
    try:
        # Test 1: Check config.py updates (REMOVED DAILY LIMITS)
        print("📊 TESTING CONFIG.PY UPDATES:")
        print("-" * 40)

        print(f"✅ MAX_DAILY_TRADES: REMOVED (unlimited trades)")
        print(f"✅ MAX_DAILY_TRADES_PER_TIMEFRAME: REMOVED (unlimited trades)")

        # Verify the limits are removed
        if hasattr(config, 'MAX_DAILY_TRADES'):
            print("❌ Daily trade limits still exist in config")
            return False
        else:
            print("✅ Daily trade limits successfully removed from config")
        
        # Test 2: Check HTML template changes
        print("\n🎨 TESTING HTML TEMPLATE CHANGES:")
        print("-" * 40)
        
        html_file = "dashboard/templates/dashboard.html"
        if os.path.exists(html_file):
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # Check if max drawdown and profit factor blocks are removed
            if 'max-drawdown' not in html_content:
                print("✅ Max Drawdown block removed")
            else:
                print("❌ Max Drawdown block still present")
            
            if 'profit-factor' not in html_content:
                print("✅ Profit Factor block removed")
            else:
                print("❌ Profit Factor block still present")
            
            # Check if Active Positions card is enhanced
            if 'active-positions-details' in html_content:
                print("✅ Enhanced Active Positions card present")
            else:
                print("❌ Enhanced Active Positions card missing")
        else:
            print("❌ HTML template file not found")
        
        # Test 3: Check JavaScript updates
        print("\n📜 TESTING JAVASCRIPT UPDATES:")
        print("-" * 40)
        
        js_file = "dashboard/static/dashboard.js"
        if os.path.exists(js_file):
            with open(js_file, 'r', encoding='utf-8') as f:
                js_content = f.read()
            
            # Check if removed metric updates are gone
            if 'max-drawdown' not in js_content:
                print("✅ Max Drawdown JavaScript removed")
            else:
                print("❌ Max Drawdown JavaScript still present")
            
            if 'profit-factor' not in js_content:
                print("✅ Profit Factor JavaScript removed")
            else:
                print("❌ Profit Factor JavaScript still present")
            
            # Check if new trade limits are implemented
            if 'dailyLimits' in js_content and "'short_term': 20" in js_content:
                print("✅ New daily trade limits implemented in JavaScript")
            else:
                print("❌ New daily trade limits missing in JavaScript")
            
            # Check if enhanced positions method exists
            if 'updateActivePositionsDetails' in js_content:
                print("✅ Enhanced Active Positions method present")
            else:
                print("❌ Enhanced Active Positions method missing")
        else:
            print("❌ JavaScript file not found")
        
        # Test 4: Check dashboard server updates
        print("\n🖥️ TESTING DASHBOARD SERVER UPDATES:")
        print("-" * 40)
        
        server_file = "dashboard_server.py"
        if os.path.exists(server_file):
            with open(server_file, 'r', encoding='utf-8') as f:
                server_content = f.read()
            
            # Check if circuit breakers are updated
            if '"max_daily_trades": "45 total"' in server_content:
                print("✅ Total daily trades updated to 45")
            else:
                print("❌ Total daily trades not updated")
            
            if '"max_per_timeframe": "Short: 20, Medium: 20, Long: 5"' in server_content:
                print("✅ Per-timeframe limits updated")
            else:
                print("❌ Per-timeframe limits not updated")
            
            # Check if position details are included
            if 'position_details' in server_content:
                print("✅ Position details integration present")
            else:
                print("❌ Position details integration missing")
        else:
            print("❌ Dashboard server file not found")
        
        # Test 5: Verify trade limits are removed
        print("\n🧮 TESTING TRADE LIMIT REMOVAL:")
        print("-" * 40)

        print("✅ Daily trade limits successfully removed")
        print("✅ System now allows unlimited trades per day")
        print("✅ Other circuit breakers remain active")
        
        print("\n📋 SUMMARY OF CHANGES:")
        print("-" * 30)
        print("✅ Removed: Max Drawdown and Profit Factor blocks")
        print("✅ Enhanced: Active Positions display with detailed info")
        print("✅ Updated: Daily trade limits per timeframe")
        print("   • Short Term: 20 trades/day")
        print("   • Medium Term: 20 trades/day") 
        print("   • Long Term: 5 trades/day")
        print("   • Total System: 45 trades/day")
        
        print("\n🎯 DASHBOARD DISPLAY CHANGES:")
        print("-" * 35)
        print("• Timeframe cards now show: Daily: X/20 (Short/Medium) or X/5 (Long)")
        print("• Circuit breakers show: 'Short: 20, Medium: 20, Long: 5'")
        print("• Active Positions shows detailed trade information")
        print("• Removed performance metrics that were not needed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_dashboard_updates()
    if success:
        print("\n✅ DASHBOARD UPDATES TEST COMPLETED")
        print("\n🚀 Changes implemented successfully:")
        print("   • Max Drawdown and Profit Factor blocks removed")
        print("   • Daily trade limits updated: Short=20, Medium=20, Long=5")
        print("   • Enhanced Active Positions display with model info")
        print("   • Dashboard will show new trade limits correctly")
    else:
        print("\n❌ DASHBOARD UPDATES TEST FAILED")
        sys.exit(1)
