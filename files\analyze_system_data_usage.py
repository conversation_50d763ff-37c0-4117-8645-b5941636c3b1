#!/usr/bin/env python3
"""
Analyze current data usage patterns in the AI trading system.
Check databases, logs, caches, and memory usage to understand cleanup needs.
"""

import os
import sys
import sqlite3
import psutil
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_database_usage():
    """Analyze database sizes and data retention."""
    print("📊 DATABASE USAGE ANALYSIS")
    print("=" * 60)
    
    db_files = [
        'data/synthetic_cache/synthetic_data.db',
        'data/synthetic_cache/dex_900_down_data.db'
    ]
    
    total_db_size = 0
    
    for db_file in db_files:
        if os.path.exists(db_file):
            size_mb = os.path.getsize(db_file) / (1024 * 1024)
            total_db_size += size_mb
            print(f"\n📁 {db_file}")
            print(f"   Size: {size_mb:.2f} MB")
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Get table names
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                    count = cursor.fetchone()[0]
                    
                    # Try to get date range
                    try:
                        cursor.execute(f'SELECT MIN(timestamp), MAX(timestamp) FROM {table_name}')
                        min_ts, max_ts = cursor.fetchone()
                        if min_ts and max_ts:
                            min_date = datetime.fromtimestamp(min_ts)
                            max_date = datetime.fromtimestamp(max_ts)
                            days = (max_date - min_date).days
                            print(f"   📈 {table_name}: {count:,} records")
                            print(f"      Date range: {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')} ({days} days)")
                        else:
                            print(f"   📈 {table_name}: {count:,} records (no timestamp data)")
                    except:
                        print(f"   📈 {table_name}: {count:,} records (no timestamp column)")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ Error reading database: {e}")
        else:
            print(f"\n📁 {db_file}: Not found")
    
    print(f"\n💾 Total database size: {total_db_size:.2f} MB")
    return total_db_size

def analyze_log_usage():
    """Analyze log file sizes and retention."""
    print(f"\n📝 LOG FILE USAGE ANALYSIS")
    print("=" * 60)
    
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        print("❌ Logs directory not found")
        return 0
    
    total_log_size = 0
    log_files = []
    
    # Analyze all log files
    for root, dirs, files in os.walk(log_dir):
        for file in files:
            if file.endswith('.log'):
                file_path = os.path.join(root, file)
                try:
                    size_mb = os.path.getsize(file_path) / (1024 * 1024)
                    total_log_size += size_mb
                    
                    # Get file modification time
                    mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    days_old = (datetime.now() - mod_time).days
                    
                    log_files.append((file_path, size_mb, days_old, mod_time))
                except:
                    pass
    
    # Sort by size
    log_files.sort(key=lambda x: x[1], reverse=True)
    
    print(f"📊 Total log size: {total_log_size:.2f} MB")
    print(f"📁 Number of log files: {len(log_files)}")
    
    print(f"\n🔝 Largest log files:")
    for file_path, size_mb, days_old, mod_time in log_files[:10]:
        print(f"   {file_path}: {size_mb:.2f} MB ({days_old} days old)")
    
    # Analyze by age
    recent_logs = [f for f in log_files if f[2] <= 7]  # Last 7 days
    old_logs = [f for f in log_files if f[2] > 7]
    
    recent_size = sum(f[1] for f in recent_logs)
    old_size = sum(f[1] for f in old_logs)
    
    print(f"\n📅 Log age analysis:")
    print(f"   Recent (≤7 days): {len(recent_logs)} files, {recent_size:.2f} MB")
    print(f"   Old (>7 days): {len(old_logs)} files, {old_size:.2f} MB")
    
    return total_log_size

def analyze_cache_usage():
    """Analyze cache file sizes."""
    print(f"\n💾 CACHE FILE USAGE ANALYSIS")
    print("=" * 60)
    
    cache_dirs = [
        'data/synthetic_cache',
        'data',
        'models/saved'
    ]
    
    total_cache_size = 0
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            print(f"\n📁 {cache_dir}:")
            
            dir_size = 0
            file_count = 0
            
            for root, dirs, files in os.walk(cache_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        size_mb = os.path.getsize(file_path) / (1024 * 1024)
                        dir_size += size_mb
                        file_count += 1
                        
                        if size_mb > 1:  # Show files larger than 1MB
                            print(f"   {file}: {size_mb:.2f} MB")
                    except:
                        pass
            
            print(f"   📊 Total: {file_count} files, {dir_size:.2f} MB")
            total_cache_size += dir_size
        else:
            print(f"\n📁 {cache_dir}: Not found")
    
    print(f"\n💾 Total cache size: {total_cache_size:.2f} MB")
    return total_cache_size

def analyze_memory_usage():
    """Analyze current memory usage."""
    print(f"\n🧠 CURRENT MEMORY USAGE ANALYSIS")
    print("=" * 60)
    
    try:
        # System memory
        memory = psutil.virtual_memory()
        print(f"💾 System Memory:")
        print(f"   Total: {memory.total / (1024**3):.2f} GB")
        print(f"   Available: {memory.available / (1024**3):.2f} GB")
        print(f"   Used: {memory.used / (1024**3):.2f} GB ({memory.percent:.1f}%)")
        
        # Current process
        process = psutil.Process()
        proc_memory = process.memory_info()
        print(f"\n🐍 Current Process:")
        print(f"   RSS: {proc_memory.rss / (1024**2):.2f} MB")
        print(f"   VMS: {proc_memory.vms / (1024**2):.2f} MB")
        
        # Python processes
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
            try:
                if 'python' in proc.info['name'].lower():
                    memory_mb = proc.info['memory_info'].rss / (1024**2)
                    python_processes.append((proc.info['pid'], proc.info['name'], memory_mb))
            except:
                pass
        
        if python_processes:
            python_processes.sort(key=lambda x: x[2], reverse=True)
            print(f"\n🐍 Python Processes:")
            for pid, name, memory_mb in python_processes[:5]:
                print(f"   PID {pid} ({name}): {memory_mb:.2f} MB")
        
        return memory.percent
        
    except Exception as e:
        print(f"❌ Error analyzing memory: {e}")
        return 0

def generate_cleanup_recommendations(db_size, log_size, cache_size, memory_percent):
    """Generate cleanup recommendations based on analysis."""
    print(f"\n🔧 CLEANUP RECOMMENDATIONS")
    print("=" * 60)
    
    total_storage = db_size + log_size + cache_size
    
    print(f"📊 Storage Summary:")
    print(f"   Databases: {db_size:.2f} MB")
    print(f"   Logs: {log_size:.2f} MB")
    print(f"   Caches: {cache_size:.2f} MB")
    print(f"   Total: {total_storage:.2f} MB")
    print(f"   Memory usage: {memory_percent:.1f}%")
    
    recommendations = []
    
    # Database recommendations
    if db_size > 100:  # More than 100MB
        recommendations.append("🗄️  Database cleanup: Consider archiving old data (>30 days)")
    
    # Log recommendations
    if log_size > 50:  # More than 50MB
        recommendations.append("📝 Log cleanup: Archive or delete logs older than 7 days")
    
    # Memory recommendations
    if memory_percent > 70:
        recommendations.append("🧠 Memory cleanup: Implement periodic garbage collection")
    
    # Cache recommendations
    if cache_size > 200:  # More than 200MB
        recommendations.append("💾 Cache cleanup: Clear temporary calculation caches")
    
    if recommendations:
        print(f"\n💡 Recommended actions:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
    else:
        print(f"\n✅ System looks healthy - no immediate cleanup needed")
        print(f"   Proactive daily cleanup at 00:00 is still recommended")
    
    return recommendations

def main():
    """Main analysis function."""
    print("🔍 AI TRADING SYSTEM DATA USAGE ANALYSIS")
    print("=" * 80)
    print(f"Analysis time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Analyze each component
    db_size = analyze_database_usage()
    log_size = analyze_log_usage()
    cache_size = analyze_cache_usage()
    memory_percent = analyze_memory_usage()
    
    # Generate recommendations
    recommendations = generate_cleanup_recommendations(db_size, log_size, cache_size, memory_percent)
    
    print(f"\n🎯 ANALYSIS COMPLETE")
    print("=" * 40)
    
    if memory_percent < 80 and (db_size + log_size + cache_size) < 500:
        print("✅ System is running efficiently")
        print("✅ Daily 00:00 cleanup will be sufficient")
        print("✅ 80% RAM threshold is appropriate")
    else:
        print("⚠️  System may benefit from more aggressive cleanup")
        print("💡 Consider implementing the recommendations above")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        input("\nPress Enter to exit...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nAnalysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during analysis: {e}")
        sys.exit(1)
