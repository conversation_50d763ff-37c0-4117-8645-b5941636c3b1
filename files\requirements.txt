# Core dependencies for Synthetic DEX 900 DOWN AI Trading System
# Data processing and analysis
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# Machine Learning
scikit-learn>=1.1.0
tensorflow>=2.10.0
torch>=1.12.0
xgboost>=1.6.0

# MetaTrader 5 integration
MetaTrader5>=5.0.37

# Database - sqlite3 is built into Python

# Web framework for dashboard
flask>=2.2.0
flask-socketio>=5.3.0

# Plotting and visualization
plotly>=5.10.0
matplotlib>=3.5.0
seaborn>=0.11.0

# Utilities
python-dateutil>=2.8.0
pytz>=2022.1
requests>=2.28.0

# Logging and monitoring
colorlog>=6.7.0

# Cache management and system monitoring
psutil>=5.9.0
schedule>=1.2.0

# Development and testing
pytest>=7.1.0
pytest-cov>=3.0.0

# Optional: For advanced features
# stable-baselines3>=1.6.0  # For reinforcement learning
# ta-lib>=0.4.24  # For additional technical indicators
