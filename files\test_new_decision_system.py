#!/usr/bin/env python3
"""
Test New Decision System
Test the enhanced logging and confidence-based trading logic.
"""

import logging
import time
from datetime import datetime

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_decision_system():
    """Test the new decision logging and confidence-based trading system."""
    print("🔍 TESTING NEW DECISION SYSTEM")
    print("=" * 60)
    print("✅ Enhanced Logging System")
    print("✅ Timeframe-Specific Ensemble Voting") 
    print("✅ Confidence-Based Trading Logic")
    print("=" * 60)
    
    try:
        # Import components
        from ai_model_manager import AIModelManager
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from trading_signal_generator import TradingSignalGenerator
        from model_decision_logger import decision_logger
        
        print("🔧 Initializing AI components...")
        
        # Initialize components
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        
        print("📚 Loading AI models...")
        
        # Load models
        loaded_models = []
        for model_name in ai_manager.model_configs.keys():
            if ai_manager.load_model(model_name):
                loaded_models.append(model_name)
                
        print(f"✅ {len(loaded_models)} models loaded: {loaded_models}")
        
        if not loaded_models:
            print("⚠️ No models loaded - training models first...")
            training_results = ai_manager.train_all_models()
            successful_models = [name for name, success in training_results.items() if success]
            print(f"✅ {len(successful_models)} models trained successfully")
            
        print("\n🧠 TESTING AI MODEL DECISIONS")
        print("-" * 40)
        
        # Test signal generation with new logging
        test_price = 53727.26
        
        print(f"💰 Testing with price: {test_price}")
        print("🔄 Generating signal with enhanced logging...")
        print()
        
        # Generate signal (this will trigger all the new logging)
        signal = signal_generator.generate_signal(test_price)
        
        print()
        print("📊 SIGNAL GENERATION RESULT:")
        if signal:
            print(f"   🚀 Signal Type: {signal.signal_type.name}")
            print(f"   🎯 Confidence: {signal.confidence:.3f}")
            print(f"   💰 Entry Price: {signal.entry_price:.2f}")
            print(f"   🛡️ Stop Loss: {signal.stop_loss:.2f}")
            print(f"   🎯 Take Profit: {signal.take_profit:.2f}")
            print(f"   📊 Position Size: {signal.position_size:.4f}")
            print(f"   ⚖️ Risk/Reward: {signal.risk_reward_ratio:.2f}")
            print(f"   ⏰ Timeframe: {signal.timeframe} minutes")
            print(f"   💭 Reasoning: {signal.reasoning}")
        else:
            print("   📊 No signal generated (normal behavior)")
            
        print()
        print("📈 SESSION SUMMARY:")
        summary = decision_logger.get_session_summary()
        
        if "message" in summary:
            print(f"   {summary['message']}")
        else:
            print(f"   📊 Total Decisions: {summary['total_decisions']}")
            print(f"   🎯 Ensemble Results: {summary['total_ensemble_results']}")
            print(f"   🚀 Trade Triggers: {summary['total_trade_triggers']}")
            print(f"   ⏱️ Session Duration: {summary['session_duration']}")
            
            if summary['model_statistics']:
                print("\n   🧠 Model Statistics:")
                for model_name, stats in summary['model_statistics'].items():
                    category = stats['timeframe_category']
                    decisions = stats['total_decisions']
                    avg_conf = stats['avg_confidence']
                    signals = stats['signal_distribution']
                    print(f"      • {model_name} ({category}): {decisions} decisions, avg conf: {avg_conf:.3f}")
                    print(f"        Signals: {signals}")
        
        print()
        print("💾 SAVING SESSION DATA...")
        decision_logger.save_session()
        
        print()
        print("✅ TEST COMPLETED SUCCESSFULLY!")
        print()
        print("🎯 WHAT'S NEW:")
        print("1. 📊 Every model decision is now logged with details")
        print("2. 🎯 Ensemble voting shows individual model contributions")
        print("3. ⚡ Strong signals (2/-2) trigger immediate trades")
        print("4. 🕐 Only relevant timeframe models vote on decisions")
        print("5. 📈 Comprehensive session tracking and analysis")
        print()
        print("🔍 TO SEE DETAILED LOGS:")
        print("   • Check console output above for real-time decisions")
        print("   • View logs/model_decisions/ for persistent logs")
        print("   • Run the system to see continuous decision tracking")
        
    except Exception as e:
        print(f"❌ Error testing decision system: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        try:
            data_collector.cleanup()
        except:
            pass

if __name__ == "__main__":
    test_decision_system()
