"""
AI Model Manager for Synthetic DEX 900 DOWN Index Trading System.
Manages 9 specialized AI models across different timeframes and trading strategies.
"""

import os
import numpy as np
import pandas as pd
import pickle
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import joblib
from meta_model_ensemble import MetaModelEnsemble

# Machine Learning imports
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix
import xgboost as xgb

# Deep Learning imports (optional - will handle if not available)
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import LSTM, Dense, Dropout, Input, Attention
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    logging.warning("TensorFlow not available. Deep learning models will be disabled.")

import config
from synthetic_data_collector import Synthetic<PERSON><PERSON><PERSON>ollector
from synthetic_pattern_detector import SyntheticPatternDetector


# Set up logging
logger = logging.getLogger("AIModelManager")

class AIModelManager:
    """
    Manages 9 specialized AI models for synthetic trading:
    - 3 Short-term models (1M, 5M, 15M): Scalping specialists
    - 3 Medium-term models (15M, 30M, 1H): Swing trading specialists  
    - 3 Long-term models (1H, 4H, 1D): Position trading specialists
    """
    
    def __init__(self, data_collector: SyntheticDataCollector, pattern_detector: SyntheticPatternDetector):
        """Initialize the AI Model Manager."""
        self.data_collector = data_collector
        self.pattern_detector = pattern_detector
        
        # Model storage
        self.models = {}
        self.scalers = {}
        self.label_encoders = {}
        self.model_performance = {}

        # Performance tracking
        self.performance_history = {}
        self.prediction_logs = {}
        self.accuracy_tracker = {}

        # Model configurations
        self.model_configs = self._initialize_model_configs()

        # Initialize meta-model ensemble
        model_names = list(self.model_configs.keys())
        self.meta_model = MetaModelEnsemble(model_names)
        logger.info(f"Meta-model ensemble initialized with {len(model_names)} base models")



        # Ensure model directory exists
        os.makedirs("models/saved", exist_ok=True)
        os.makedirs("models/performance", exist_ok=True)

        # Load existing performance data
        self.load_performance_data()

        logger.info("AI Model Manager initialized")

        
    def _initialize_model_configs(self) -> Dict:
        """Initialize configurations for all 9 models with enhanced trend direction awareness."""
        return {
            # SHORT-TERM MODELS (Scalping Specialists) - Enhanced with trend direction
            "short_term_pattern_nn": {
                "type": "neural_network",
                "timeframes": [1, 5, 15],
                "purpose": "Pattern recognition for scalping with trend direction awareness",
                "features": ["price_action", "micro_patterns", "volatility_regime", "short_term_trend_direction", "trend_alignment"],
                "target": "entry_signal",
                "lookback": 50,
                "model_class": "lstm" if TENSORFLOW_AVAILABLE else "random_forest",
                "trend_direction_features": ["1min_trend", "5min_trend", "15min_trend", "trend_momentum_short"]
            },

            "short_term_momentum_rf": {
                "type": "random_forest",
                "timeframes": [1, 5, 15],
                "purpose": "Momentum detection for quick trades with trend direction filtering",
                "features": ["momentum", "rsi", "macd", "volatility_adjusted_momentum", "short_term_trend_direction", "momentum_trend_alignment"],
                "target": "momentum_signal",
                "lookback": 30,
                "model_class": "random_forest",
                "trend_direction_features": ["1min_trend", "5min_trend", "15min_trend", "momentum_trend_consistency"]
            },
            
            "short_term_reversion_gb": {
                "type": "gradient_boosting",
                "timeframes": [1, 5, 15],
                "purpose": "Mean reversion scalping with trend context",
                "features": ["deviation", "bollinger", "rsi_extreme", "short_term_trend_direction", "reversion_trend_context"],
                "target": "reversion_signal",
                "lookback": 20,
                "model_class": "gradient_boosting",
                "trend_direction_features": ["1min_trend", "5min_trend", "15min_trend", "reversion_probability"]
            },
            
            # MEDIUM-TERM MODELS (Swing Trading Specialists) - Enhanced with trend direction
            "medium_term_trend_lstm": {
                "type": "lstm",
                "timeframes": [15, 30, 60],
                "purpose": "Trend continuation detection with multi-timeframe analysis",
                "features": ["trend_strength", "moving_averages", "trend_sustainability", "medium_term_trend_direction", "cross_timeframe_alignment"],
                "target": "trend_signal",
                "lookback": 100,
                "model_class": "lstm" if TENSORFLOW_AVAILABLE else "random_forest",
                "trend_direction_features": ["15min_trend", "30min_trend", "1h_trend", "trend_persistence"]
            },

            "medium_term_breakout_rf": {
                "type": "random_forest",
                "timeframes": [15, 30, 60],
                "purpose": "Breakout detection with trend direction confirmation",
                "features": ["support_resistance", "false_breakout_filter", "volatility", "medium_term_trend_direction", "breakout_trend_alignment"],
                "target": "breakout_signal",
                "lookback": 50,
                "model_class": "random_forest",
                "trend_direction_features": ["15min_trend", "30min_trend", "1h_trend", "breakout_sustainability"]
            },
            
            "medium_term_volatility_xgb": {
                "type": "xgboost",
                "timeframes": [15, 30, 60],
                "purpose": "Volatility trading with trend-aware positioning",
                "features": ["volatility_regime", "atr", "bollinger_width", "medium_term_trend_direction", "volatility_trend_context"],
                "target": "volatility_signal",
                "lookback": 75,
                "model_class": "xgboost",
                "trend_direction_features": ["15min_trend", "30min_trend", "1h_trend", "volatility_expansion_direction"]
            },
            
            # LONG-TERM MODELS (Position Trading Specialists) - Enhanced with trend direction
            "long_term_macro_dnn": {
                "type": "deep_neural_network",
                "timeframes": [60, 240, 1440],
                "purpose": "Major trend analysis with macro direction awareness",
                "features": ["macro_trend", "regime_changes", "long_patterns", "long_term_trend_direction", "macro_trend_alignment"],
                "target": "position_signal",
                "lookback": 200,
                "model_class": "deep_nn" if TENSORFLOW_AVAILABLE else "random_forest",
                "trend_direction_features": ["1h_trend", "4h_trend", "daily_trend", "weekly_trend", "macro_trend_strength"]
            },
            
            "long_term_levels_rf": {
                "type": "random_forest",
                "timeframes": [60, 240, 1440],
                "purpose": "Support/resistance trading with trend-aware levels",
                "features": ["key_levels", "institutional_zones", "fibonacci", "long_term_trend_direction", "level_trend_context"],
                "target": "level_signal",
                "lookback": 150,
                "model_class": "random_forest",
                "trend_direction_features": ["1h_trend", "4h_trend", "daily_trend", "level_sustainability"]
            },
            
            "long_term_portfolio_gb": {
                "type": "gradient_boosting",
                "timeframes": [60, 240, 1440],
                "purpose": "Portfolio allocation optimization with trend-based sizing",
                "features": ["volatility_regime", "atr", "trend_strength", "long_term_trend_direction", "portfolio_trend_alignment"],
                "target": "allocation_signal",
                "lookback": 100,
                "model_class": "gradient_boosting",
                "trend_direction_features": ["1h_trend", "4h_trend", "daily_trend", "allocation_confidence"]
            }
        }
        
    def prepare_training_data(self, model_name: str) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare training data for a specific model."""
        logger.info(f"Preparing training data for {model_name}")
        
        config = self.model_configs[model_name]
        timeframes = config["timeframes"]
        lookback = config["lookback"]
        
        # Collect data from all relevant timeframes
        all_features = []
        all_targets = []
        
        for timeframe in timeframes:
            # Get historical data
            df = self.data_collector.get_latest_data(timeframe, count=5000)  # Get substantial data
            
            if df.empty:
                logger.warning(f"No data available for {timeframe}min timeframe")
                continue
                
            # Calculate synthetic indicators
            df_with_indicators = self.pattern_detector.calculate_synthetic_indicators(df)
            
            # Generate features based on model purpose
            features = self._generate_features(df_with_indicators, config)
            
            # Generate targets based on model purpose
            targets = self._generate_targets(df_with_indicators, config)
            
            if len(features) > 0 and len(targets) > 0:
                all_features.extend(features)
                all_targets.extend(targets)
                
        if len(all_features) == 0:
            raise ValueError(f"No training data generated for {model_name}")
            
        # Convert to numpy arrays
        X = np.array(all_features)
        y = np.array(all_targets)
        
        logger.info(f"Training data prepared: {X.shape[0]} samples, {X.shape[1]} features")
        return X, y
        
    def _generate_features(self, df: pd.DataFrame, config: Dict) -> List[List[float]]:
        """Generate features based on model configuration using LIVE feature extraction."""
        features = []
        lookback = config["lookback"]
        feature_types = config["features"]

        # Ensure we have enough data
        if len(df) < lookback + 10:
            return features

        # Import live feature extraction methods
        from trading_signal_generator import TradingSignalGenerator

        # Create a temporary signal generator to use its feature extraction methods
        temp_signal_gen = TradingSignalGenerator(None, None, None)

        for i in range(lookback, len(df) - 5):  # Leave some data for target calculation
            feature_vector = []

            # Get current row and historical context
            current_row = df.iloc[i]
            df_context = df.iloc[max(0, i-50):i+1]  # Get enough context for feature calculation

            # Use LIVE feature extraction methods to ensure consistency
            if "price_action" in feature_types:
                feature_vector.extend(temp_signal_gen._get_price_action_features(df_context, current_row))

            if "volatility_regime" in feature_types:
                feature_vector.extend(temp_signal_gen._get_volatility_regime_features(df_context, current_row))

            if "micro_patterns" in feature_types:
                feature_vector.extend(temp_signal_gen._get_micro_pattern_features(df_context, current_row))

            if "momentum" in feature_types:
                feature_vector.extend(temp_signal_gen._get_momentum_features(df_context, current_row))

            if "rsi" in feature_types:
                feature_vector.extend(temp_signal_gen._get_rsi_features(df_context, current_row))

            if "macd" in feature_types:
                feature_vector.extend(temp_signal_gen._get_macd_features(df_context, current_row))

            if "volatility_adjusted_momentum" in feature_types:
                feature_vector.extend(temp_signal_gen._get_volatility_adjusted_momentum_features(df_context, current_row))

            if "deviation" in feature_types:
                feature_vector.extend(temp_signal_gen._get_deviation_features(df_context, current_row))

            if "bollinger" in feature_types:
                feature_vector.extend(temp_signal_gen._get_bollinger_features(df_context, current_row))

            if "rsi_extreme" in feature_types:
                feature_vector.extend(temp_signal_gen._get_rsi_extreme_features(df_context, current_row))

            if "trend_strength" in feature_types:
                feature_vector.extend(temp_signal_gen._get_trend_strength_features(df_context, current_row))

            if "moving_averages" in feature_types:
                feature_vector.extend(temp_signal_gen._get_moving_average_features(df_context, current_row))

            if "trend_sustainability" in feature_types:
                feature_vector.extend(temp_signal_gen._get_trend_sustainability_features(df_context, current_row))

            if "support_resistance" in feature_types:
                feature_vector.extend(temp_signal_gen._get_support_resistance_features(df_context, current_row))

            if "false_breakout_filter" in feature_types:
                feature_vector.extend(temp_signal_gen._get_false_breakout_filter_features(df_context, current_row))

            if "volatility" in feature_types:
                feature_vector.extend(temp_signal_gen._get_volatility_features(df_context, current_row))

            if "volatility_regime" in feature_types:
                feature_vector.extend(temp_signal_gen._get_volatility_regime_features(df_context, current_row))

            if "atr" in feature_types:
                feature_vector.extend(temp_signal_gen._get_atr_features(df_context, current_row))

            if "bollinger_width" in feature_types:
                feature_vector.extend(temp_signal_gen._get_bollinger_width_features(df_context, current_row))

            # ENHANCED TREND DIRECTION FEATURES
            if "short_term_trend_direction" in feature_types:
                feature_vector.extend(self._get_short_term_trend_direction_features(df_context, current_row, config))

            if "medium_term_trend_direction" in feature_types:
                feature_vector.extend(self._get_medium_term_trend_direction_features(df_context, current_row, config))

            if "long_term_trend_direction" in feature_types:
                feature_vector.extend(self._get_long_term_trend_direction_features(df_context, current_row, config))

            if "trend_alignment" in feature_types:
                feature_vector.extend(self._get_trend_alignment_features(df_context, current_row, config))

            if "momentum_trend_alignment" in feature_types:
                feature_vector.extend(self._get_momentum_trend_alignment_features(df_context, current_row, config))

            if "reversion_trend_context" in feature_types:
                feature_vector.extend(self._get_reversion_trend_context_features(df_context, current_row, config))

            if "cross_timeframe_alignment" in feature_types:
                feature_vector.extend(self._get_cross_timeframe_alignment_features(df_context, current_row, config))

            if "breakout_trend_alignment" in feature_types:
                feature_vector.extend(self._get_breakout_trend_alignment_features(df_context, current_row, config))

            if "volatility_trend_context" in feature_types:
                feature_vector.extend(self._get_volatility_trend_context_features(df_context, current_row, config))

            if "macro_trend_alignment" in feature_types:
                feature_vector.extend(self._get_macro_trend_alignment_features(df_context, current_row, config))

            if "level_trend_context" in feature_types:
                feature_vector.extend(self._get_level_trend_context_features(df_context, current_row, config))

            if "portfolio_trend_alignment" in feature_types:
                feature_vector.extend(self._get_portfolio_trend_alignment_features(df_context, current_row, config))

            # Always add synthetic-specific features (same as live system)
            feature_vector.extend(temp_signal_gen._get_synthetic_features(df_context, current_row))

            # Only add if we have valid features
            if len(feature_vector) > 0 and not any(np.isnan(feature_vector)):
                features.append(feature_vector)

        return features
        
    def _generate_targets(self, df: pd.DataFrame, config: Dict) -> List[int]:
        """Generate target labels based on model configuration."""
        targets = []
        lookback = config["lookback"]
        target_type = config["target"]
        
        if len(df) < lookback + 10:
            return targets
            
        for i in range(lookback, len(df) - 5):
            # Look ahead to determine target
            future_window = df.iloc[i:i+5]  # Next 5 periods
            
            if target_type == "entry_signal":
                target = self._calculate_entry_signal(df.iloc[i], future_window)
            elif target_type == "momentum_signal":
                target = self._calculate_momentum_signal(df.iloc[i], future_window)
            elif target_type == "reversion_signal":
                target = self._calculate_reversion_signal(df.iloc[i], future_window)
            elif target_type == "trend_signal":
                target = self._calculate_trend_signal(df.iloc[i], future_window)
            elif target_type == "breakout_signal":
                target = self._calculate_breakout_signal(df.iloc[i], future_window)
            elif target_type == "volatility_signal":
                target = self._calculate_volatility_signal(df.iloc[i], future_window)
            else:
                target = 0  # Default: no signal
                
            targets.append(target)
            
        return targets
        
    def _extract_price_action_features(self, window: pd.DataFrame) -> List[float]:
        """Extract price action features."""
        if window.empty:
            return [0.0] * 10
            
        features = []
        
        # Basic price features
        features.append(window['close'].iloc[-1] / window['close'].iloc[0] - 1)  # Total return
        features.append(window['high'].max() / window['close'].iloc[-1] - 1)     # High deviation
        features.append(window['low'].min() / window['close'].iloc[-1] - 1)      # Low deviation
        features.append(window['volume'].mean())                                 # Average volume
        
        # Price patterns
        features.append(len(window[window['close'] > window['open']]) / len(window))  # Bullish ratio
        features.append(window['close'].std() / window['close'].mean())               # Price volatility
        
        # Recent momentum
        if len(window) >= 5:
            features.append(window['close'].iloc[-5:].mean() / window['close'].iloc[-10:-5].mean() - 1)
        else:
            features.append(0.0)
            
        # Range analysis
        features.append((window['high'] - window['low']).mean() / window['close'].mean())
        features.append(window['close'].iloc[-1] / window[['high', 'low', 'close']].mean().mean() - 1)
        features.append(window['volume'].iloc[-5:].mean() / window['volume'].iloc[:-5].mean() - 1)
        
        return features
        
    def _extract_volume_features(self, window: pd.DataFrame) -> List[float]:
        """Extract volume-based features."""
        if window.empty or 'volume' not in window.columns:
            return [0.0] * 5
            
        features = []
        
        # Volume trends
        features.append(window['volume'].iloc[-5:].mean() / window['volume'].mean() - 1)
        features.append(window['volume'].std() / window['volume'].mean())
        features.append(len(window[window['volume'] > window['volume'].mean()]) / len(window))
        
        # Volume-price relationship
        up_volume = window[window['close'] > window['open']]['volume'].sum()
        down_volume = window[window['close'] <= window['open']]['volume'].sum()
        total_volume = up_volume + down_volume
        
        if total_volume > 0:
            features.append(up_volume / total_volume)
            features.append(down_volume / total_volume)
        else:
            features.extend([0.5, 0.5])
            
        return features
        
    def _extract_momentum_features(self, window: pd.DataFrame) -> List[float]:
        """Extract momentum indicators."""
        if window.empty:
            return [0.0] * 8
            
        features = []
        
        # RSI-like calculation
        if 'returns' in window.columns:
            gains = window['returns'].where(window['returns'] > 0, 0)
            losses = -window['returns'].where(window['returns'] < 0, 0)
            
            avg_gain = gains.rolling(window=14, min_periods=1).mean().iloc[-1]
            avg_loss = losses.rolling(window=14, min_periods=1).mean().iloc[-1]
            
            if avg_loss != 0:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            else:
                rsi = 100 if avg_gain > 0 else 50
                
            features.append(rsi / 100)  # Normalize to 0-1
        else:
            features.append(0.5)
            
        # MACD-like calculation
        if len(window) >= 26:
            ema12 = window['close'].ewm(span=12).mean().iloc[-1]
            ema26 = window['close'].ewm(span=26).mean().iloc[-1]
            macd = (ema12 - ema26) / window['close'].iloc[-1]
            features.append(macd)
        else:
            features.append(0.0)
            
        # Price momentum
        if len(window) >= 10:
            momentum_5 = window['close'].iloc[-1] / window['close'].iloc[-5] - 1
            momentum_10 = window['close'].iloc[-1] / window['close'].iloc[-10] - 1
            features.extend([momentum_5, momentum_10])
        else:
            features.extend([0.0, 0.0])
            
        # Acceleration
        if 'price_acceleration' in window.columns:
            features.append(window['price_acceleration'].iloc[-1])
            features.append(window['price_acceleration'].mean())
            features.append(window['price_acceleration'].std())
        else:
            features.extend([0.0, 0.0, 0.0])
            
        # Velocity
        if 'tick_velocity' in window.columns:
            features.append(window['tick_velocity'].iloc[-1])
        else:
            features.append(0.0)
            
        return features
        
    def _extract_volatility_features(self, window: pd.DataFrame) -> List[float]:
        """Extract volatility-based features."""
        if window.empty:
            return [0.0] * 6
            
        features = []
        
        # Basic volatility
        if 'volatility' in window.columns:
            features.append(window['volatility'].iloc[-1])
            features.append(window['volatility'].mean())
            features.append(window['volatility'].std())
        else:
            features.extend([0.0, 0.0, 0.0])
            
        # Volatility regime
        if 'regime_state' in window.columns:
            features.append(window['regime_state'].iloc[-1] / 4)  # Normalize to 0-1
        else:
            features.append(0.0)
            
        # Volatility compression
        if 'volatility_compression' in window.columns:
            features.append(window['volatility_compression'].iloc[-1])
        else:
            features.append(0.0)
            
        # Jumpiness
        if 'jumpiness_score' in window.columns:
            features.append(window['jumpiness_score'].iloc[-1])
        else:
            features.append(0.0)
            
        return features
        
    def _extract_trend_features(self, window: pd.DataFrame) -> List[float]:
        """Extract trend-based features."""
        if window.empty:
            return [0.0] * 5
            
        features = []
        
        # Moving average trends
        if len(window) >= 20:
            ma_short = window['close'].rolling(window=5).mean().iloc[-1]
            ma_long = window['close'].rolling(window=20).mean().iloc[-1]
            features.append((ma_short - ma_long) / window['close'].iloc[-1])
        else:
            features.append(0.0)
            
        # Trend strength
        if len(window) >= 10:
            trend_up = len(window[window['close'] > window['close'].shift(1)]) / len(window)
            features.append(trend_up)
        else:
            features.append(0.5)
            
        # Price position in range
        if len(window) >= 20:
            high_20 = window['high'].rolling(window=20).max().iloc[-1]
            low_20 = window['low'].rolling(window=20).min().iloc[-1]
            if high_20 != low_20:
                position = (window['close'].iloc[-1] - low_20) / (high_20 - low_20)
            else:
                position = 0.5
            features.append(position)
        else:
            features.append(0.5)
            
        # Trend consistency
        if 'returns' in window.columns and len(window) >= 10:
            positive_returns = len(window[window['returns'] > 0])
            consistency = abs(positive_returns - len(window)/2) / (len(window)/2)
            features.append(consistency)
        else:
            features.append(0.0)
            
        # Recent trend change
        if len(window) >= 6:
            recent_trend = window['close'].iloc[-3:].mean() / window['close'].iloc[-6:-3].mean() - 1
            features.append(recent_trend)
        else:
            features.append(0.0)
            
        return features
        
    def _extract_synthetic_features(self, window: pd.DataFrame) -> List[float]:
        """Extract features specific to synthetic trading."""
        features = []
        
        # Synthetic-specific indicators
        synthetic_cols = ['jumpiness_score', 'volatility_compression', 'price_acceleration', 
                         'tick_velocity', 'mean_reversion_signal', 'regime_state']
        
        for col in synthetic_cols:
            if col in window.columns:
                features.append(window[col].iloc[-1] if not pd.isna(window[col].iloc[-1]) else 0.0)
                features.append(window[col].mean() if not pd.isna(window[col].mean()) else 0.0)
            else:
                features.extend([0.0, 0.0])
                
        return features

    def _calculate_entry_signal(self, current_row: pd.Series, future_window: pd.DataFrame) -> int:
        """Calculate entry signal for scalping models."""
        if future_window.empty:
            return 0

        # Look for profitable entry opportunities
        current_price = current_row['close']
        future_high = future_window['high'].max()
        future_low = future_window['low'].min()

        # Calculate potential profit/loss
        upside_potential = (future_high - current_price) / current_price
        downside_risk = (current_price - future_low) / current_price

        # Entry signals based on risk/reward (AGGRESSIVE THRESHOLDS)
        if upside_potential > 0.0008 and upside_potential > downside_risk * 1.1:  # 0.08% profit, 1.1:1 R/R
            return 2  # Strong buy signal
        elif upside_potential > 0.0005 and upside_potential > downside_risk * 0.9:
            return 1  # Weak buy signal
        elif downside_risk > 0.0008 and downside_risk > upside_potential * 1.1:
            return -2  # Strong sell signal
        elif downside_risk > 0.0005 and downside_risk > upside_potential * 0.9:
            return -1  # Weak sell signal
        else:
            return 0  # No signal

    def _calculate_momentum_signal(self, current_row: pd.Series, future_window: pd.DataFrame) -> int:
        """Calculate momentum signal."""
        if future_window.empty:
            return 0

        # Check if momentum continues
        current_momentum = current_row.get('price_acceleration', 0)
        future_returns = future_window['close'].pct_change().sum()

        # Strong momentum signals
        if current_momentum > 1.0 and future_returns > 0.003:
            return 2  # Strong momentum up
        elif current_momentum > 0.5 and future_returns > 0.001:
            return 1  # Weak momentum up
        elif current_momentum < -1.0 and future_returns < -0.003:
            return -2  # Strong momentum down
        elif current_momentum < -0.5 and future_returns < -0.001:
            return -1  # Weak momentum down
        else:
            return 0  # No momentum signal

    def _calculate_reversion_signal(self, current_row: pd.Series, future_window: pd.DataFrame) -> int:
        """Calculate mean reversion signal."""
        if future_window.empty:
            return 0

        # Check for mean reversion after extremes
        current_reversion = current_row.get('mean_reversion_signal', 0)
        future_mean_return = future_window['close'].iloc[-1] / future_window['close'].iloc[0] - 1

        # Reversion signals
        if current_reversion > 0.5 and future_mean_return < -0.001:
            return 2  # Strong reversion down
        elif current_reversion > 0.3 and future_mean_return < 0:
            return 1  # Weak reversion down
        elif current_reversion > 0.5 and future_mean_return > 0.001:
            return -2  # Failed reversion (continue trend)
        else:
            return 0  # No reversion signal

    def _calculate_trend_signal(self, current_row: pd.Series, future_window: pd.DataFrame) -> int:
        """Calculate trend continuation signal."""
        if future_window.empty:
            return 0

        # Check trend continuation
        future_trend = future_window['close'].iloc[-1] / future_window['close'].iloc[0] - 1
        current_regime = current_row.get('regime_state', 0)

        # Trend signals based on regime and future movement
        if current_regime in [1, 2] and future_trend > 0.005:  # PRE_JUMP or JUMPING
            return 2  # Strong trend up
        elif current_regime == 0 and abs(future_trend) < 0.001:  # QUIET
            return 0  # Range bound
        elif future_trend > 0.002:
            return 1  # Weak trend up
        elif future_trend < -0.002:
            return -1  # Weak trend down
        else:
            return 0  # No trend

    def _calculate_breakout_signal(self, current_row: pd.Series, future_window: pd.DataFrame) -> int:
        """Calculate breakout signal."""
        if future_window.empty:
            return 0

        # Check for breakout continuation
        current_price = current_row['close']
        future_high = future_window['high'].max()
        future_low = future_window['low'].min()

        # Breakout signals
        breakout_up = (future_high - current_price) / current_price
        breakout_down = (current_price - future_low) / current_price

        if breakout_up > 0.005:  # 0.5% breakout up
            return 2
        elif breakout_up > 0.002:
            return 1
        elif breakout_down > 0.005:  # 0.5% breakout down
            return -2
        elif breakout_down > 0.002:
            return -1
        else:
            return 0

    def _calculate_volatility_signal(self, current_row: pd.Series, future_window: pd.DataFrame) -> int:
        """Calculate volatility trading signal."""
        if future_window.empty:
            return 0

        # Check volatility expansion/contraction
        current_vol = current_row.get('volatility', 0)
        current_compression = current_row.get('volatility_compression', 0)
        future_range = (future_window['high'].max() - future_window['low'].min()) / current_row['close']

        # Volatility signals
        if current_compression > 0.5 and future_range > 0.01:  # Compression followed by expansion
            return 2  # Strong volatility expansion expected
        elif current_vol > 0.01 and future_range < 0.005:  # High vol followed by contraction
            return -1  # Volatility contraction expected
        elif current_compression > 0.3 and future_range > 0.005:
            return 1  # Moderate expansion expected
        else:
            return 0  # No volatility signal

    def train_model(self, model_name: str) -> bool:
        """Train a specific model."""
        try:
            logger.info(f"Training model: {model_name}")

            # Prepare training data
            X, y = self.prepare_training_data(model_name)

            if len(X) < 100:  # Minimum samples required
                logger.warning(f"Insufficient training data for {model_name}: {len(X)} samples")
                return False

            # Get model configuration
            config = self.model_configs[model_name]
            model_class = config["model_class"]

            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # Handle class imbalance before encoding
            X_scaled, y = self._handle_class_imbalance(X_scaled, y, model_name)

            # Encode labels
            label_encoder = LabelEncoder()
            y_encoded = label_encoder.fit_transform(y)

            # Create and train model based on type
            if model_class == "random_forest":
                model = self._create_random_forest_model()
                model.fit(X_scaled, y_encoded)

            elif model_class == "gradient_boosting":
                model = self._create_gradient_boosting_model()
                # Use class weights for gradient boosting
                sample_weights = self._calculate_sample_weights(y_encoded)
                model.fit(X_scaled, y_encoded, sample_weight=sample_weights)

            elif model_class == "xgboost":
                model = self._create_xgboost_model()
                model.fit(X_scaled, y_encoded)

            elif model_class == "lstm" and TENSORFLOW_AVAILABLE:
                model = self._create_lstm_model(X_scaled.shape[1])
                X_lstm = self._prepare_lstm_data(X_scaled)

                # Optimize training for large datasets
                if len(X_lstm) > 10000:
                    # Use smaller batch size and fewer epochs for large datasets
                    batch_size = 64
                    epochs = 20
                    logger.info(f"Large dataset detected ({len(X_lstm)} samples), using optimized training parameters")
                else:
                    batch_size = 32
                    epochs = 50

                # Add progress logging
                logger.info(f"Starting LSTM training: {len(X_lstm)} sequences, {epochs} epochs, batch_size={batch_size}")

                model.fit(X_lstm, y_encoded, epochs=epochs, batch_size=batch_size, verbose=1,
                         validation_split=0.2, callbacks=self._get_keras_callbacks())

            elif model_class == "deep_nn" and TENSORFLOW_AVAILABLE:
                model = self._create_deep_nn_model(X_scaled.shape[1])
                model.fit(X_scaled, y_encoded, epochs=100, batch_size=64, verbose=0,
                         validation_split=0.2, callbacks=self._get_keras_callbacks())
            else:
                # Fallback to Random Forest if deep learning not available
                logger.warning(f"Falling back to Random Forest for {model_name}")
                model = self._create_random_forest_model()
                model.fit(X_scaled, y_encoded)

            # Store model and preprocessors
            self.models[model_name] = model
            self.scalers[model_name] = scaler
            self.label_encoders[model_name] = label_encoder

            # Evaluate model performance
            performance = self._evaluate_model(model, X_scaled, y_encoded, model_class)
            self.model_performance[model_name] = performance

            # Save model to disk
            self._save_model(model_name, model, scaler, label_encoder)

            logger.info(f"Model {model_name} trained successfully. Accuracy: {performance.get('accuracy', 0):.3f}")
            return True

        except Exception as e:
            logger.error(f"Error training model {model_name}: {e}")
            return False

    def _create_random_forest_model(self):
        """Create Random Forest model."""
        return RandomForestClassifier(
            n_estimators=200,
            max_depth=15,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=42,
            n_jobs=-1
        )

    def _create_gradient_boosting_model(self):
        """Create Gradient Boosting model."""
        return GradientBoostingClassifier(
            n_estimators=150,
            max_depth=8,
            learning_rate=0.1,
            subsample=0.8,
            random_state=42
        )

    def _create_xgboost_model(self):
        """Create XGBoost model."""
        return xgb.XGBClassifier(
            n_estimators=200,
            max_depth=8,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='mlogloss',
            base_score=0.5  # Fix: Set proper base score for logistic loss
        )

    def _handle_class_imbalance(self, X: np.ndarray, y: np.ndarray, model_name: str) -> Tuple[np.ndarray, np.ndarray]:
        """Handle class imbalance in training data."""
        try:
            from sklearn.utils.class_weight import compute_class_weight
            from imblearn.over_sampling import SMOTE

            # Check class distribution
            unique_classes, class_counts = np.unique(y, return_counts=True)
            logger.info(f"Class distribution for {model_name}: {dict(zip(unique_classes, class_counts))}")

            # Handle case with only one class
            if len(unique_classes) < 2:
                logger.warning(f"Only one class found for {model_name}, creating synthetic minority class")
                minority_size = max(10, len(y) // 10)  # At least 10 samples or 10% of data

                # Create synthetic samples for the minority class
                minority_class = 1 - unique_classes[0] if unique_classes[0] in [0, 1] else 1

                # Add noise to existing features to create minority samples
                noise_factor = 0.1
                synthetic_X = []
                synthetic_y = []

                for i in range(minority_size):
                    # Select random sample and add noise
                    base_idx = np.random.randint(0, len(X))
                    noise = np.random.normal(0, noise_factor, X.shape[1])
                    synthetic_sample = X[base_idx] + noise
                    synthetic_X.append(synthetic_sample)
                    synthetic_y.append(minority_class)

                # Combine original and synthetic data
                X = np.vstack([X, np.array(synthetic_X)])
                y = np.hstack([y, np.array(synthetic_y)])

                logger.info(f"Added {minority_size} synthetic samples for class {minority_class}")

            # Apply SMOTE for better balance if we have at least 2 classes
            unique_classes_after = np.unique(y)
            if len(unique_classes_after) >= 2:
                try:
                    # Map negative labels to positive for SMOTE (SMOTE requires non-negative labels)
                    y_mapped = y.copy()
                    label_mapping = {}
                    reverse_mapping = {}

                    for i, label in enumerate(sorted(unique_classes_after)):
                        label_mapping[label] = i
                        reverse_mapping[i] = label
                        y_mapped[y == label] = i

                    # Calculate minimum neighbors available
                    min_class_count = min(np.bincount(y_mapped.astype(int)))
                    k_neighbors = min(5, max(1, min_class_count - 1))

                    smote = SMOTE(random_state=42, k_neighbors=k_neighbors)
                    X_resampled, y_mapped_resampled = smote.fit_resample(X, y_mapped)

                    # Map back to original labels
                    y_resampled = y_mapped_resampled.copy()
                    for mapped_label, original_label in reverse_mapping.items():
                        y_resampled[y_mapped_resampled == mapped_label] = original_label

                    X, y = X_resampled, y_resampled
                    logger.info(f"Applied SMOTE to {model_name}. New class distribution: {dict(zip(*np.unique(y, return_counts=True)))}")
                except Exception as smote_error:
                    logger.warning(f"SMOTE failed for {model_name}: {smote_error}, proceeding with manual balancing")

            return X, y

        except Exception as e:
            logger.error(f"Error handling class imbalance for {model_name}: {e}")
            return X, y

    def _calculate_sample_weights(self, y_encoded: np.ndarray) -> np.ndarray:
        """Calculate sample weights for class balancing."""
        try:
            from sklearn.utils.class_weight import compute_class_weight

            # Compute class weights
            unique_classes = np.unique(y_encoded)
            class_weights = compute_class_weight('balanced', classes=unique_classes, y=y_encoded)
            class_weight_dict = dict(zip(unique_classes, class_weights))

            # Apply sample weights
            sample_weights = np.array([class_weight_dict[label] for label in y_encoded])

            return sample_weights

        except Exception as e:
            logger.error(f"Error calculating sample weights: {e}")
            # Return uniform weights as fallback
            return np.ones(len(y_encoded))

    def _create_lstm_model(self, input_features: int):
        """Create LSTM model for time series prediction."""
        if not TENSORFLOW_AVAILABLE:
            raise ValueError("TensorFlow not available for LSTM model")

        # Use Input layer to avoid the warning
        sequence_length = 50

        # Create model with Input layer (fixes the warning)
        inputs = Input(shape=(sequence_length, input_features))
        x = LSTM(64, return_sequences=True)(inputs)  # Reduced size for stability
        x = Dropout(0.2)(x)
        x = LSTM(32, return_sequences=False)(x)  # Reduced size for stability
        x = Dropout(0.2)(x)
        x = Dense(16, activation='relu')(x)  # Reduced size for stability
        x = Dropout(0.1)(x)
        outputs = Dense(5, activation='softmax')(x)  # 5 classes: -2, -1, 0, 1, 2

        model = Model(inputs=inputs, outputs=outputs)

        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )

        return model

    def _create_deep_nn_model(self, input_features: int):
        """Create Deep Neural Network model."""
        if not TENSORFLOW_AVAILABLE:
            raise ValueError("TensorFlow not available for Deep NN model")

        model = Sequential([
            Dense(256, activation='relu', input_shape=(input_features,)),
            Dropout(0.3),
            Dense(128, activation='relu'),
            Dropout(0.2),
            Dense(64, activation='relu'),
            Dropout(0.1),
            Dense(32, activation='relu'),
            Dense(5, activation='softmax')  # 5 classes: -2, -1, 0, 1, 2
        ])

        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )

        return model

    def _prepare_lstm_data(self, X: np.ndarray, sequence_length: int = 50) -> np.ndarray:
        """Prepare data for LSTM model with optimizations for large datasets."""
        if len(X) < sequence_length:
            # Pad with zeros if not enough data
            padding = np.zeros((sequence_length - len(X), X.shape[1]))
            X_padded = np.vstack([padding, X])
            return X_padded.reshape(1, sequence_length, X.shape[1])

        # For large datasets, use sampling to reduce memory usage
        max_sequences = 15000  # Limit to prevent memory issues
        total_possible_sequences = len(X) - sequence_length + 1

        if total_possible_sequences > max_sequences:
            # Sample sequences evenly across the dataset
            step_size = total_possible_sequences // max_sequences
            indices = range(0, total_possible_sequences, step_size)[:max_sequences]
            logger.info(f"Large dataset: sampling {len(indices)} sequences from {total_possible_sequences} possible")
        else:
            indices = range(total_possible_sequences)

        # Create sequences more efficiently
        sequences = np.zeros((len(indices), sequence_length, X.shape[1]))
        for idx, i in enumerate(indices):
            sequences[idx] = X[i:i + sequence_length]

        return sequences

    def _get_keras_callbacks(self):
        """Get Keras callbacks for training."""
        return [
            EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=0.0001
            )
        ]

    def _evaluate_model(self, model, X: np.ndarray, y: np.ndarray, model_class: str) -> Dict:
        """Evaluate model performance."""
        try:
            if model_class in ["lstm", "deep_nn"]:
                # For Keras models
                if model_class == "lstm":
                    X_eval = self._prepare_lstm_data(X)
                    if len(X_eval) > 0:
                        predictions = model.predict(X_eval, verbose=0)
                        y_pred = np.argmax(predictions, axis=1)
                        # Adjust y to match predictions length
                        y_eval = y[-len(y_pred):] if len(y) > len(y_pred) else y
                    else:
                        return {"accuracy": 0.0, "error": "No evaluation data"}
                else:
                    predictions = model.predict(X, verbose=0)
                    y_pred = np.argmax(predictions, axis=1)
                    y_eval = y
            else:
                # For sklearn models
                y_pred = model.predict(X)
                y_eval = y

            # Calculate accuracy
            accuracy = np.mean(y_pred == y_eval)

            # Calculate class distribution
            unique_classes, class_counts = np.unique(y_eval, return_counts=True)
            class_distribution = dict(zip(unique_classes, class_counts))

            return {
                "accuracy": accuracy,
                "class_distribution": class_distribution,
                "total_samples": len(y_eval),
                "model_type": model_class
            }

        except Exception as e:
            logger.error(f"Error evaluating model: {e}")
            return {"accuracy": 0.0, "error": str(e)}

    def _save_model(self, model_name: str, model, scaler, label_encoder):
        """Save model and preprocessors to disk."""
        try:
            model_dir = f"models/saved/{model_name}"
            os.makedirs(model_dir, exist_ok=True)

            # Save model
            if hasattr(model, 'save'):  # Keras model
                model.save(f"{model_dir}/model.keras")  # Use modern Keras format
            else:  # Sklearn model
                joblib.dump(model, f"{model_dir}/model.pkl")

            # Save preprocessors
            joblib.dump(scaler, f"{model_dir}/scaler.pkl")
            joblib.dump(label_encoder, f"{model_dir}/label_encoder.pkl")

            # Save model configuration
            config = self.model_configs[model_name].copy()
            with open(f"{model_dir}/config.pkl", 'wb') as f:
                pickle.dump(config, f)

            logger.info(f"Model {model_name} saved to {model_dir}")

        except Exception as e:
            logger.error(f"Error saving model {model_name}: {e}")

    def load_model(self, model_name: str) -> bool:
        """Load a saved model."""
        try:
            model_dir = f"models/saved/{model_name}"

            if not os.path.exists(model_dir):
                logger.warning(f"Model directory not found: {model_dir}")
                return False

            # Load configuration
            with open(f"{model_dir}/config.pkl", 'rb') as f:
                config = pickle.load(f)

            # Load preprocessors
            scaler = joblib.load(f"{model_dir}/scaler.pkl")
            label_encoder = joblib.load(f"{model_dir}/label_encoder.pkl")

            # Load model
            if config["model_class"] in ["lstm", "deep_nn"] and TENSORFLOW_AVAILABLE:
                # Try new .keras format first, fallback to .h5 for compatibility
                keras_file = f"{model_dir}/model.keras"
                h5_file = f"{model_dir}/model.h5"

                if os.path.exists(keras_file):
                    model = tf.keras.models.load_model(keras_file)
                elif os.path.exists(h5_file):
                    model = tf.keras.models.load_model(h5_file)
                else:
                    raise FileNotFoundError(f"No model file found in {model_dir}")
            else:
                model = joblib.load(f"{model_dir}/model.pkl")

            # Store in memory
            self.models[model_name] = model
            self.scalers[model_name] = scaler
            self.label_encoders[model_name] = label_encoder
            self.model_configs[model_name] = config

            logger.info(f"Model {model_name} loaded successfully")
            return True

        except Exception as e:
            logger.error(f"Error loading model {model_name}: {e}")
            return False

    def predict(self, model_name: str, features: np.ndarray) -> Optional[Dict]:
        """Make prediction using a specific model."""
        try:
            if model_name not in self.models:
                logger.warning(f"Model {model_name} not loaded")
                return None

            model = self.models[model_name]
            scaler = self.scalers[model_name]
            label_encoder = self.label_encoders[model_name]
            config = self.model_configs[model_name]

            # Scale features
            features_scaled = scaler.transform(features.reshape(1, -1))

            # Make prediction based on model type
            if config["model_class"] in ["lstm", "deep_nn"]:
                if config["model_class"] == "lstm":
                    # For LSTM, we need sequence data - create a sequence from current features
                    # Repeat the current features to create a sequence of length 50
                    sequence_length = 50
                    features_sequence = np.tile(features_scaled, (sequence_length, 1))
                    features_lstm = features_sequence.reshape(1, sequence_length, features_scaled.shape[1])
                    prediction_proba = model.predict(features_lstm, verbose=0)[0]
                else:
                    # For Deep NN, use features as-is
                    prediction_proba = model.predict(features_scaled, verbose=0)[0]

                predicted_class = np.argmax(prediction_proba)
                confidence = float(np.max(prediction_proba))
            else:
                # Sklearn models
                predicted_class = model.predict(features_scaled)[0]
                if hasattr(model, 'predict_proba'):
                    prediction_proba = model.predict_proba(features_scaled)[0]
                    confidence = float(np.max(prediction_proba))
                else:
                    prediction_proba = None
                    confidence = 0.5

            # Decode prediction
            try:
                signal = label_encoder.inverse_transform([predicted_class])[0]
                signal_int = int(signal)
            except (ValueError, TypeError) as e:
                logger.warning(f"Error decoding prediction for {model_name}: {e}, using raw prediction")
                signal_int = int(predicted_class) - 2  # Convert from 0-4 to -2 to 2 range

            # Track prediction for performance monitoring
            prediction_result = {
                "model_name": model_name,
                "signal": signal_int,
                "confidence": confidence,
                "probabilities": prediction_proba.tolist() if prediction_proba is not None else None,
                "model_type": config["model_class"],
                "purpose": config["purpose"],
                "timestamp": pd.Timestamp.now(),
                "predicted_class": int(predicted_class),
                "timeframe_category": self._get_model_timeframe_category(model_name)
            }

            # Log prediction for performance tracking
            self._log_prediction(model_name, prediction_result)

            return prediction_result

        except Exception as e:
            logger.error(f"Error making prediction with {model_name}: {e}")
            return None

    def get_live_performance_weight(self, model_name: str) -> float:
        """Get live performance weight for a model."""
        if model_name in self.accuracy_tracker:
            tracker = self.accuracy_tracker[model_name]
            if tracker["total_predictions"] > 10:  # Only use live accuracy after enough predictions
                live_accuracy = tracker["correct_predictions"] / tracker["total_predictions"]
                # Penalize models with low accuracy
                if live_accuracy < 0.4:
                    return 0.1  # Very low weight
                return live_accuracy
        # Fallback to training accuracy if not enough live data
        return self.model_performance.get(model_name, {}).get("accuracy", 0.5)

    def get_ensemble_prediction(self, features: np.ndarray) -> Dict:
        """Get ensemble prediction from all loaded models."""
        try:
            predictions = []

            # Get predictions from all loaded models
            for model_name in self.models.keys():
                prediction = self.predict(model_name, features)
                if prediction:
                    predictions.append(prediction)

            if not predictions:
                return {
                    "ensemble_signal": 0,
                    "confidence": 0.0,
                    "individual_predictions": [],
                    "consensus": "no_models"
                }

            # Calculate weighted ensemble
            signals = []
            confidences = []
            weights = []

            for pred in predictions:
                signals.append(pred["signal"])
                confidences.append(pred["confidence"])

                # Weight based on model performance and confidence
                model_accuracy = self.get_live_performance_weight(pred["model_name"])
                weight = model_accuracy * pred["confidence"]
                weights.append(weight)

            # Prepare base predictions for meta-model
            base_predictions = {}
            for pred in predictions:
                base_predictions[pred["model_name"]] = {
                    'signal': pred["signal"],
                    'confidence': pred["confidence"]
                }

            # Create market features for meta-model
            market_features = self._extract_market_features(features)

            # Get meta-model prediction
            meta_prediction = self.meta_model.predict(base_predictions, market_features)

            # Use meta-model prediction if available, otherwise fallback to weighted voting
            if meta_prediction.get('meta_model_used', False):
                ensemble_signal = meta_prediction['signal']
                meta_confidence = meta_prediction['confidence']
                logger.debug(f"Using meta-model prediction: {ensemble_signal} (confidence: {meta_confidence:.3f})")
            else:
                # Fallback to weighted voting
                if sum(weights) > 0:
                    weighted_signals = [s * w for s, w in zip(signals, weights)]
                    ensemble_signal = sum(weighted_signals) / sum(weights)

                    # Round to nearest integer signal
                    ensemble_signal = int(round(ensemble_signal))
                    ensemble_signal = max(-2, min(2, ensemble_signal))  # Clamp to valid range
                else:
                    ensemble_signal = 0
                meta_confidence = np.mean(confidences) if confidences else 0.0

            # Calculate consensus strength
            signal_counts = {}
            for signal in signals:
                signal_counts[signal] = signal_counts.get(signal, 0) + 1

            max_count = max(signal_counts.values()) if signal_counts else 0
            consensus_strength = max_count / len(signals) if signals else 0

            # Determine consensus type
            if consensus_strength >= 0.8:
                consensus = "strong"
            elif consensus_strength >= 0.6:
                consensus = "moderate"
            elif consensus_strength >= 0.4:
                consensus = "weak"
            else:
                consensus = "conflicted"

            return {
                "ensemble_signal": ensemble_signal,
                "confidence": meta_confidence,
                "consensus_strength": consensus_strength,
                "consensus": consensus,
                "individual_predictions": predictions,
                "signal_distribution": signal_counts,
                "total_models": len(predictions),
                "meta_model_used": meta_prediction.get('meta_model_used', False)
            }

        except Exception as e:
            logger.error(f"Error getting ensemble prediction: {e}")
            return {
                "ensemble_signal": 0,
                "confidence": 0.0,
                "individual_predictions": [],
                "consensus": "error",
                "error": str(e)
            }

    def _extract_market_features(self, features: np.ndarray) -> Dict:
        """Extract market context features for meta-model."""
        try:
            # Convert features array to market context
            # This is a simplified extraction - in practice, you'd have more sophisticated feature engineering

            if len(features) < 4:
                return {'volatility': 0, 'trend_strength': 0, 'volume_ratio': 0, 'price_momentum': 0}

            # Extract basic market features from the feature vector
            # Assuming features contain price-related information
            volatility = np.std(features[-10:]) if len(features) >= 10 else np.std(features)
            trend_strength = abs(np.mean(np.diff(features[-5:]))) if len(features) >= 5 else 0
            volume_ratio = features[-1] / np.mean(features) if np.mean(features) != 0 else 1.0
            price_momentum = np.mean(np.diff(features[-3:])) if len(features) >= 3 else 0

            return {
                'volatility': float(volatility),
                'trend_strength': float(trend_strength),
                'volume_ratio': float(volume_ratio),
                'price_momentum': float(price_momentum)
            }

        except Exception as e:
            logger.error(f"Error extracting market features: {e}")
            return {'volatility': 0, 'trend_strength': 0, 'volume_ratio': 0, 'price_momentum': 0}

    def update_meta_model_with_outcome(self, base_predictions: Dict, market_features: Dict,
                                     actual_outcome: int, prediction_time: Optional[datetime] = None):
        """Update meta-model with actual trading outcome."""
        try:
            self.meta_model.collect_training_data(
                base_predictions=base_predictions,
                market_features=market_features,
                actual_outcome=actual_outcome,
                prediction_time=prediction_time
            )
            logger.debug(f"Meta-model updated with outcome: {actual_outcome}")

        except Exception as e:
            logger.error(f"Error updating meta-model with outcome: {e}")

    def get_ensemble_prediction_with_features(self, model_features: Dict[str, np.ndarray], target_timeframe: int = 5) -> Dict:
        """Get ensemble prediction using model-specific features."""
        try:
            if not model_features:
                logger.warning("No model features provided")
                return {"ensemble_signal": 0, "confidence": 0.0, "consensus": "no_features",
                       "consensus_strength": 0.0, "total_models": 0, "predictions": {}}

            predictions = {}
            valid_predictions = []

            # Get timeframe-specific models
            relevant_models = self._get_relevant_models_for_timeframe(target_timeframe)

            # Import decision logger
            from model_decision_logger import decision_logger

            # Track strong signals for immediate trading
            strong_signals = []  # Signals of 2 or -2 that should trigger immediate trades

            # Get predictions from each model with its specific features
            for model_name, features in model_features.items():
                # Check for strong signals from ALL models (regardless of timeframe relevance)
                if model_name in self.models and model_name in self.scalers:
                    try:
                        # Use the proper predict method that handles input shapes correctly
                        prediction_result = self.predict(model_name, features)

                        if prediction_result:
                            # Get timeframe weight for this model
                            weight = self._get_timeframe_weight(model_name, target_timeframe)
                            signal = prediction_result["signal"]
                            confidence = prediction_result["confidence"]

                            predictions[model_name] = {
                                "prediction": signal,
                                "confidence": confidence,
                                "weight": weight,
                                "timeframe_category": self._get_model_timeframe_category(model_name),
                                "purpose": self.model_configs[model_name].get("purpose", "Unknown")
                            }

                            # Log individual model decision
                            decision_logger.log_model_decision(model_name, {
                                "signal": signal,
                                "confidence": confidence,
                                "timeframe_category": self._get_model_timeframe_category(model_name),
                                "weight": weight,
                                "features_count": len(features) if hasattr(features, '__len__') else 0,
                                "purpose": self.model_configs[model_name].get("purpose", "Unknown")
                            })

                            # Check for ultra-strong signals (single model can trigger trade)
                            # Ultra-strong signal: ±2 with confidence ≥ 0.6
                            if abs(signal) == 2 and confidence >= 0.6:
                                strong_signals.append({
                                    "model_name": model_name,
                                    "signal": signal,
                                    "confidence": confidence,
                                    "timeframe_category": self._get_model_timeframe_category(model_name),
                                    "purpose": self.model_configs[model_name].get("purpose", "Unknown")
                                })

                            # Add to valid predictions ONLY if model is timeframe-relevant for ensemble voting
                            if model_name in relevant_models:
                                valid_predictions.append(signal)
                        else:
                            logger.warning(f"No prediction result from {model_name}")

                    except Exception as e:
                        logger.error(f"Error making prediction with {model_name}: {e}")

            if not valid_predictions:
                logger.warning("No valid predictions from any model")
                return {"ensemble_signal": 0, "confidence": 0.0, "consensus": "no_predictions",
                       "consensus_strength": 0.0, "total_models": 0, "predictions": {}}

            # Check for timeframe consensus signals (±1 with confidence ≥0.7, need 2+ models)
            timeframe_consensus_signals = self._check_timeframe_consensus(predictions, relevant_models)

            # Calculate ensemble result
            ensemble_signal = self._calculate_ensemble_signal(valid_predictions)
            avg_confidence = np.mean([pred["confidence"] for pred in predictions.values()])
            consensus, consensus_strength, consensus_signal = self._calculate_consensus(valid_predictions)

            ensemble_result = {
                "ensemble_signal": ensemble_signal,
                "confidence": float(avg_confidence),
                "consensus": consensus,
                "consensus_strength": float(consensus_strength),
                "consensus_signal": self._signal_to_text(consensus_signal),
                "total_models": len(valid_predictions),
                "relevant_models": len(relevant_models),
                "target_timeframe": target_timeframe,
                "predictions": predictions,
                "strong_signals": strong_signals,  # Models with ultra-strong signals (±2)
                "timeframe_consensus_signals": timeframe_consensus_signals  # Models with timeframe consensus (±1)
            }

            # Log ensemble result
            decision_logger.log_ensemble_result(ensemble_result)

            logger.debug(f"Timeframe-specific ensemble ({target_timeframe}min): {ensemble_signal} with {avg_confidence:.3f} confidence from {len(valid_predictions)}/{len(relevant_models)} models")
            return ensemble_result

        except Exception as e:
            logger.error(f"Error in ensemble prediction with features: {e}")
            return {"ensemble_signal": 0, "confidence": 0.0, "consensus": "error",
                   "consensus_strength": 0.0, "total_models": 0, "predictions": {}}

    def _calculate_ensemble_signal(self, predictions: List[int]) -> int:
        """Calculate ensemble signal from individual predictions."""
        if not predictions:
            return 0

        # Simple majority voting with weighted average
        signal_sum = sum(predictions)
        avg_signal = signal_sum / len(predictions)

        # Round to nearest integer and clamp to valid range
        ensemble_signal = int(round(avg_signal))
        return max(-2, min(2, ensemble_signal))

    def _calculate_consensus(self, predictions: List[int]) -> Tuple[str, float, int]:
        """Calculate consensus strength, type, and signal."""
        if not predictions:
            return "no_predictions", 0.0, 0

        # Count occurrences of each prediction
        signal_counts = {}
        for pred in predictions:
            signal_counts[pred] = signal_counts.get(pred, 0) + 1

        # Find most common prediction
        max_count = max(signal_counts.values())
        consensus_strength = max_count / len(predictions)

        # Find which signal has the consensus
        consensus_signal = max(signal_counts.keys(), key=lambda k: signal_counts[k])

        # Determine consensus type
        if consensus_strength >= 0.8:
            consensus = "strong"
        elif consensus_strength >= 0.6:
            consensus = "moderate"
        elif consensus_strength >= 0.4:
            consensus = "weak"
        else:
            consensus = "conflicted"

        return consensus, consensus_strength, consensus_signal

    def _check_timeframe_consensus(self, predictions: Dict, relevant_models: List[str]) -> List[Dict]:
        """Check for timeframe consensus signals (±1 with confidence ≥0.7, need 2+ models)."""
        try:
            # Group models by timeframe category
            timeframe_groups = {
                "short_term": [],
                "medium_term": [],
                "long_term": []
            }

            # Categorize relevant models
            for model_name in relevant_models:
                if model_name in predictions:
                    timeframe_category = self._get_model_timeframe_category(model_name)
                    if timeframe_category in timeframe_groups:
                        timeframe_groups[timeframe_category].append(model_name)

            consensus_signals = []

            # Check each timeframe group for consensus
            for timeframe_category, model_names in timeframe_groups.items():
                if len(model_names) < 2:  # Need at least 2 models for consensus
                    continue

                # Find models with ±1 signals and confidence ≥0.7
                strong_consensus_models = []
                for model_name in model_names:
                    pred = predictions[model_name]
                    signal = pred["prediction"]
                    confidence = pred["confidence"]

                    if abs(signal) == 1 and confidence >= 0.7:
                        strong_consensus_models.append({
                            "model_name": model_name,
                            "signal": signal,
                            "confidence": confidence,
                            "timeframe_category": timeframe_category,
                            "purpose": self.model_configs[model_name].get("purpose", "Unknown")
                        })

                # Check if we have 2+ models with same signal direction
                if len(strong_consensus_models) >= 2:
                    # Group by signal direction
                    buy_signals = [m for m in strong_consensus_models if m["signal"] > 0]
                    sell_signals = [m for m in strong_consensus_models if m["signal"] < 0]

                    # If 2+ models agree on direction, add to consensus signals
                    if len(buy_signals) >= 2:
                        consensus_signals.extend(buy_signals)
                    elif len(sell_signals) >= 2:
                        consensus_signals.extend(sell_signals)

            return consensus_signals

        except Exception as e:
            logger.error(f"Error checking timeframe consensus: {e}")
            return []

    def _signal_to_text(self, signal: int) -> str:
        """Convert signal number to readable text."""
        signal_map = {
            -2: "STRONG SELL",
            -1: "SELL",
            0: "HOLD",
            1: "BUY",
            2: "STRONG BUY"
        }
        return signal_map.get(signal, "UNKNOWN")

    def _get_relevant_models_for_timeframe(self, target_timeframe: int) -> List[str]:
        """Get models relevant for the target timeframe.

        FIXED: Always include ALL timeframe models for comprehensive analysis.
        Each timeframe group should be able to generate signals independently.
        """
        relevant_models = []

        # SOLUTION: Include ALL models regardless of target_timeframe
        # This allows all timeframe groups (short/medium/long) to participate in signal generation
        for model_name, config in self.model_configs.items():
            model_timeframes = config.get('timeframes', [])

            # Include all models that have valid timeframe configurations
            if model_timeframes:
                relevant_models.append(model_name)

        logger.debug(f"Including ALL {len(relevant_models)} models for comprehensive timeframe analysis (target: {target_timeframe}min)")
        return relevant_models

    def _get_timeframe_weight(self, model_name: str, target_timeframe: int) -> float:
        """Get weight for model based on timeframe relevance."""
        config = self.model_configs.get(model_name, {})
        model_timeframes = config.get('timeframes', [])

        if not model_timeframes:
            return 0.1  # Minimal weight for unknown timeframes

        # Calculate weight based on timeframe proximity
        min_distance = min(abs(tf - target_timeframe) for tf in model_timeframes)

        if min_distance == 0:
            return 1.0  # Perfect match
        elif min_distance <= 5:
            return 0.8  # Close match
        elif min_distance <= 15:
            return 0.5  # Moderate match
        elif min_distance <= 30:
            return 0.3  # Distant match
        else:
            return 0.1  # Very distant match

    def _get_model_timeframe_category(self, model_name: str) -> str:
        """Get timeframe category for a model."""
        config = self.model_configs.get(model_name, {})
        model_timeframes = config.get('timeframes', [])

        if not model_timeframes:
            return "UNKNOWN"

        max_timeframe = max(model_timeframes)

        if max_timeframe <= 15:
            return "short_term"
        elif max_timeframe <= 60:
            return "medium_term"
        else:
            return "long_term"

    def _log_prediction(self, model_name: str, prediction_result: Dict):
        """Log prediction for performance tracking."""
        try:
            if model_name not in self.prediction_logs:
                self.prediction_logs[model_name] = []

            # Keep only last 1000 predictions per model to manage memory
            if len(self.prediction_logs[model_name]) >= 1000:
                self.prediction_logs[model_name] = self.prediction_logs[model_name][-500:]

            self.prediction_logs[model_name].append(prediction_result)

        except Exception as e:
            logger.warning(f"Error logging prediction for {model_name}: {e}")

    def track_prediction_accuracy(self, model_name: str, predicted_signal: int, actual_outcome: int,
                                 time_horizon_minutes: int = 5):
        """Track prediction accuracy by comparing predictions with actual outcomes."""
        try:
            if model_name not in self.accuracy_tracker:
                self.accuracy_tracker[model_name] = {
                    "correct_predictions": 0,
                    "total_predictions": 0,
                    "accuracy_history": [],
                    "last_updated": pd.Timestamp.now()
                }

            tracker = self.accuracy_tracker[model_name]

            # Check if prediction was correct
            is_correct = (predicted_signal == actual_outcome)

            if is_correct:
                tracker["correct_predictions"] += 1

            tracker["total_predictions"] += 1

            # Calculate current accuracy
            current_accuracy = tracker["correct_predictions"] / tracker["total_predictions"]

            # Update accuracy history
            tracker["accuracy_history"].append({
                "timestamp": pd.Timestamp.now(),
                "accuracy": current_accuracy,
                "is_correct": is_correct,
                "predicted": predicted_signal,
                "actual": actual_outcome
            })

            # Keep only last 100 accuracy records
            if len(tracker["accuracy_history"]) > 100:
                tracker["accuracy_history"] = tracker["accuracy_history"][-50:]

            tracker["last_updated"] = pd.Timestamp.now()

            # Update model performance
            self.model_performance[model_name]["live_accuracy"] = current_accuracy
            self.model_performance[model_name]["live_predictions"] = tracker["total_predictions"]

            logger.debug(f"Accuracy tracking for {model_name}: {current_accuracy:.3f} ({tracker['correct_predictions']}/{tracker['total_predictions']})")

        except Exception as e:
            logger.error(f"Error tracking accuracy for {model_name}: {e}")

    def get_performance_report(self) -> Dict:
        """Generate comprehensive performance report for all models."""
        try:
            report = {
                "timestamp": pd.Timestamp.now(),
                "models": {},
                "ensemble_stats": {},
                "summary": {}
            }

            total_accuracy = 0
            total_predictions = 0
            models_with_data = 0

            for model_name in self.model_configs.keys():
                model_report = {
                    "model_name": model_name,
                    "model_type": self.model_configs[model_name]["model_class"],
                    "purpose": self.model_configs[model_name]["purpose"],
                    "loaded": model_name in self.models,
                    "training_performance": self.model_performance.get(model_name, {}),
                    "live_performance": {},
                    "prediction_stats": {}
                }

                # Live accuracy tracking
                if model_name in self.accuracy_tracker:
                    tracker = self.accuracy_tracker[model_name]
                    live_accuracy = tracker["correct_predictions"] / max(tracker["total_predictions"], 1)

                    model_report["live_performance"] = {
                        "accuracy": live_accuracy,
                        "total_predictions": tracker["total_predictions"],
                        "correct_predictions": tracker["correct_predictions"],
                        "last_updated": tracker["last_updated"]
                    }

                    if tracker["total_predictions"] > 0:
                        total_accuracy += live_accuracy
                        total_predictions += tracker["total_predictions"]
                        models_with_data += 1

                # Prediction statistics
                if model_name in self.prediction_logs:
                    predictions = self.prediction_logs[model_name]
                    if predictions:
                        confidences = [p["confidence"] for p in predictions]
                        signals = [p["signal"] for p in predictions]

                        model_report["prediction_stats"] = {
                            "total_predictions": len(predictions),
                            "avg_confidence": np.mean(confidences),
                            "signal_distribution": dict(zip(*np.unique(signals, return_counts=True))),
                            "last_prediction": predictions[-1]["timestamp"]
                        }

                report["models"][model_name] = model_report

            # Summary statistics
            if models_with_data > 0:
                avg_accuracy = total_accuracy / models_with_data
                report["summary"] = {
                    "average_accuracy": avg_accuracy,
                    "total_predictions": total_predictions,
                    "models_with_live_data": models_with_data,
                    "total_models": len(self.model_configs),
                    "models_loaded": len(self.models)
                }

            return report

        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {"error": str(e)}

    def save_performance_data(self):
        """Save performance tracking data to disk."""
        try:
            performance_dir = "models/performance"
            os.makedirs(performance_dir, exist_ok=True)

            # Save accuracy tracker
            with open(f"{performance_dir}/accuracy_tracker.pkl", 'wb') as f:
                pickle.dump(self.accuracy_tracker, f)

            # Save prediction logs (last 100 per model)
            filtered_logs = {}
            for model_name, logs in self.prediction_logs.items():
                filtered_logs[model_name] = logs[-100:] if len(logs) > 100 else logs

            with open(f"{performance_dir}/prediction_logs.pkl", 'wb') as f:
                pickle.dump(filtered_logs, f)

            # Save performance report
            report = self.get_performance_report()
            with open(f"{performance_dir}/performance_report.pkl", 'wb') as f:
                pickle.dump(report, f)

            logger.info("Performance data saved successfully")

        except Exception as e:
            logger.error(f"Error saving performance data: {e}")

    def load_performance_data(self):
        """Load performance tracking data from disk."""
        try:
            performance_dir = "models/performance"

            # Load accuracy tracker
            accuracy_file = f"{performance_dir}/accuracy_tracker.pkl"
            if os.path.exists(accuracy_file):
                with open(accuracy_file, 'rb') as f:
                    self.accuracy_tracker = pickle.load(f)
                logger.info("Accuracy tracker loaded successfully")

            # Load prediction logs
            logs_file = f"{performance_dir}/prediction_logs.pkl"
            if os.path.exists(logs_file):
                with open(logs_file, 'rb') as f:
                    self.prediction_logs = pickle.load(f)
                logger.info("Prediction logs loaded successfully")

        except Exception as e:
            logger.warning(f"Error loading performance data: {e}")

    def train_all_models(self) -> Dict[str, bool]:
        """Train all 9 models."""
        results = {}

        logger.info("Starting training of all 9 AI models...")

        # Ensure we have historical data before training
        logger.info("Ensuring historical data is available...")
        self._ensure_historical_data()

        for model_name in self.model_configs.keys():
            logger.info(f"Training {model_name}...")
            success = self.train_model(model_name)
            results[model_name] = success

            if success:
                logger.info(f"{model_name} trained successfully")
            else:
                logger.error(f"{model_name} training failed")

        # Summary
        successful = sum(results.values())
        total = len(results)

        logger.info(f"Training complete: {successful}/{total} models trained successfully")

        return results

    def _ensure_historical_data(self):
        """Ensure historical data is available for all required timeframes."""
        try:
            from datetime import datetime, timedelta

            # Check if we have recent data for key timeframes
            required_timeframes = [1, 5, 15, 30, 60]  # Minutes

            for timeframe in required_timeframes:
                df = self.data_collector.get_latest_data(timeframe, count=10)

                if df.empty:
                    logger.info(f"No {timeframe}min data found, collecting historical data...")

                    # Collect last 30 days of data
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=30)

                    # Trigger data collection for this timeframe
                    self.data_collector.collect_timeframe_data(timeframe, start_date, end_date)

                    # Verify data was collected
                    df_check = self.data_collector.get_latest_data(timeframe, count=10)
                    if not df_check.empty:
                        logger.info(f"Successfully collected {timeframe}min data")
                    else:
                        logger.warning(f"Failed to collect {timeframe}min data")
                else:
                    logger.info(f"{timeframe}min data available: {len(df)} records")

        except Exception as e:
            logger.error(f"Error ensuring historical data: {e}")

    def get_model_status(self) -> Dict:
        """Get status of all models."""
        status = {
            "loaded_models": list(self.models.keys()),
            "total_configured": len(self.model_configs),
            "model_performance": self.model_performance.copy(),
            "tensorflow_available": TENSORFLOW_AVAILABLE
        }

        # Add model details
        model_details = {}
        for name, config in self.model_configs.items():
            model_details[name] = {
                "type": config["model_class"],
                "purpose": config["purpose"],
                "timeframes": config["timeframes"],
                "loaded": name in self.models,
                "performance": self.model_performance.get(name, {})
            }

        status["model_details"] = model_details

        return status

    def _get_short_term_trend_direction_features(self, df, current_row, config):
        """Extract short-term trend direction features"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # 5-minute trend direction
        ma_5 = df['close'].rolling(5).mean()
        ma_10 = df['close'].rolling(10).mean()
        trend_5m = (ma_5.iloc[current_idx] - ma_10.iloc[current_idx]) / df['close'].iloc[current_idx]
        features.append(trend_5m if not np.isnan(trend_5m) else 0.0)
        
        # 15-minute trend direction
        ma_15 = df['close'].rolling(15).mean()
        ma_30 = df['close'].rolling(30).mean()
        trend_15m = (ma_15.iloc[current_idx] - ma_30.iloc[current_idx]) / df['close'].iloc[current_idx]
        features.append(trend_15m if not np.isnan(trend_15m) else 0.0)
        
        # Trend strength
        price_change = (df['close'].iloc[current_idx] - df['close'].iloc[max(0, current_idx-20)]) / df['close'].iloc[max(0, current_idx-20)]
        features.append(price_change if not np.isnan(price_change) else 0.0)
        
        # Trend consistency (how many of last 10 periods moved in same direction)
        if current_idx >= 10:
            returns = df['close'].pct_change().iloc[current_idx-9:current_idx+1]
            consistency = (returns > 0).sum() / len(returns) if len(returns) > 0 else 0.5
            features.append(consistency)
        else:
            features.append(0.5)
            
        return features

    def _get_medium_term_trend_direction_features(self, df, current_row, config):
        """Extract medium-term trend direction features"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # 1-hour trend direction
        ma_60 = df['close'].rolling(60).mean()
        ma_120 = df['close'].rolling(120).mean()
        trend_1h = (ma_60.iloc[current_idx] - ma_120.iloc[current_idx]) / df['close'].iloc[current_idx]
        features.append(trend_1h if not np.isnan(trend_1h) else 0.0)
        
        # 4-hour trend direction
        ma_240 = df['close'].rolling(240).mean()
        ma_480 = df['close'].rolling(480).mean()
        trend_4h = (ma_240.iloc[current_idx] - ma_480.iloc[current_idx]) / df['close'].iloc[current_idx]
        features.append(trend_4h if not np.isnan(trend_4h) else 0.0)
        
        # Medium-term momentum
        if current_idx >= 100:
            momentum = (df['close'].iloc[current_idx] - df['close'].iloc[current_idx-100]) / df['close'].iloc[current_idx-100]
            features.append(momentum if not np.isnan(momentum) else 0.0)
        else:
            features.append(0.0)
            
        # Trend acceleration
        if current_idx >= 50:
            recent_trend = (df['close'].iloc[current_idx] - df['close'].iloc[current_idx-25]) / df['close'].iloc[current_idx-25]
            older_trend = (df['close'].iloc[current_idx-25] - df['close'].iloc[current_idx-50]) / df['close'].iloc[current_idx-50]
            acceleration = recent_trend - older_trend
            features.append(acceleration if not np.isnan(acceleration) else 0.0)
        else:
            features.append(0.0)
            
        return features

    def _get_long_term_trend_direction_features(self, df, current_row, config):
        """Extract long-term trend direction features"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # Daily trend direction (assuming 1-minute data, 1440 periods = 1 day)
        ma_1440 = df['close'].rolling(1440).mean()
        ma_2880 = df['close'].rolling(2880).mean()
        trend_daily = (ma_1440.iloc[current_idx] - ma_2880.iloc[current_idx]) / df['close'].iloc[current_idx]
        features.append(trend_daily if not np.isnan(trend_daily) else 0.0)
        
        # Weekly trend direction
        ma_weekly = df['close'].rolling(10080).mean()  # 7 days
        ma_biweekly = df['close'].rolling(20160).mean()  # 14 days
        trend_weekly = (ma_weekly.iloc[current_idx] - ma_biweekly.iloc[current_idx]) / df['close'].iloc[current_idx]
        features.append(trend_weekly if not np.isnan(trend_weekly) else 0.0)
        
        # Long-term momentum
        if current_idx >= 1440:
            long_momentum = (df['close'].iloc[current_idx] - df['close'].iloc[current_idx-1440]) / df['close'].iloc[current_idx-1440]
            features.append(long_momentum if not np.isnan(long_momentum) else 0.0)
        else:
            features.append(0.0)
            
        # Macro trend strength
        if current_idx >= 2880:
            macro_strength = abs((df['close'].iloc[current_idx] - df['close'].iloc[current_idx-2880]) / df['close'].iloc[current_idx-2880])
            features.append(macro_strength if not np.isnan(macro_strength) else 0.0)
        else:
            features.append(0.0)
            
        return features

    def _get_trend_alignment_features(self, df, current_row, config):
        """Extract cross-timeframe trend alignment features"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # Calculate multiple timeframe trends
        trends = []
        timeframes = [5, 15, 60, 240, 1440]  # 5min, 15min, 1h, 4h, daily
        
        for tf in timeframes:
            if current_idx >= tf * 2:
                ma_short = df['close'].rolling(tf).mean().iloc[current_idx]
                ma_long = df['close'].rolling(tf * 2).mean().iloc[current_idx]
                trend = 1 if ma_short > ma_long else -1
                trends.append(trend)
            else:
                trends.append(0)
        
        # Alignment score (how many timeframes agree)
        alignment_score = sum(trends) / len(trends) if trends else 0
        features.append(alignment_score)
        
        # Trend strength consistency
        trend_strengths = []
        for tf in timeframes:
            if current_idx >= tf:
                strength = abs((df['close'].iloc[current_idx] - df['close'].iloc[current_idx-tf]) / df['close'].iloc[current_idx-tf])
                trend_strengths.append(strength if not np.isnan(strength) else 0)
            else:
                trend_strengths.append(0)
        
        avg_strength = np.mean(trend_strengths) if trend_strengths else 0
        features.append(avg_strength)
        
        return features

    def _get_momentum_trend_alignment_features(self, df, current_row, config):
        """Extract momentum-trend alignment features"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # Short-term momentum vs medium-term trend
        if current_idx >= 240:
            short_momentum = (df['close'].iloc[current_idx] - df['close'].iloc[current_idx-20]) / df['close'].iloc[current_idx-20]
            medium_trend = (df['close'].rolling(120).mean().iloc[current_idx] - df['close'].rolling(240).mean().iloc[current_idx]) / df['close'].iloc[current_idx]
            
            # Alignment: positive if both same direction, negative if opposite
            alignment = short_momentum * medium_trend
            features.append(alignment if not np.isnan(alignment) else 0.0)
        else:
            features.append(0.0)
            
        # Momentum divergence from trend
        if current_idx >= 100:
            price_momentum = (df['close'].iloc[current_idx] - df['close'].iloc[current_idx-50]) / df['close'].iloc[current_idx-50]
            trend_direction = (df['close'].rolling(50).mean().iloc[current_idx] - df['close'].rolling(100).mean().iloc[current_idx]) / df['close'].iloc[current_idx]
            
            divergence = abs(price_momentum - trend_direction)
            features.append(divergence if not np.isnan(divergence) else 0.0)
        else:
            features.append(0.0)
            
        return features

    def _get_reversion_trend_context_features(self, df, current_row, config):
        """Extract mean reversion features in trend context"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # Distance from trend line
        if current_idx >= 50:
            trend_line = df['close'].rolling(50).mean().iloc[current_idx]
            distance_from_trend = (df['close'].iloc[current_idx] - trend_line) / trend_line
            features.append(distance_from_trend if not np.isnan(distance_from_trend) else 0.0)
        else:
            features.append(0.0)
            
        # Reversion probability based on trend strength
        if current_idx >= 100:
            trend_strength = abs((df['close'].rolling(50).mean().iloc[current_idx] - df['close'].rolling(100).mean().iloc[current_idx]) / df['close'].iloc[current_idx])
            price_deviation = abs((df['close'].iloc[current_idx] - df['close'].rolling(20).mean().iloc[current_idx]) / df['close'].rolling(20).mean().iloc[current_idx])
            
            # Higher trend strength = lower reversion probability
            reversion_prob = price_deviation / (1 + trend_strength) if trend_strength > 0 else price_deviation
            features.append(reversion_prob if not np.isnan(reversion_prob) else 0.0)
        else:
            features.append(0.0)
            
        return features

    def _get_cross_timeframe_alignment_features(self, df, current_row, config):
        """Extract cross-timeframe alignment features"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # Multi-timeframe trend consensus
        timeframes = [15, 60, 240, 1440]  # 15min, 1h, 4h, daily
        trend_directions = []
        
        for tf in timeframes:
            if current_idx >= tf * 2:
                ma_fast = df['close'].rolling(tf).mean().iloc[current_idx]
                ma_slow = df['close'].rolling(tf * 2).mean().iloc[current_idx]
                direction = 1 if ma_fast > ma_slow else -1
                trend_directions.append(direction)
            else:
                trend_directions.append(0)
        
        # Consensus strength
        consensus = sum(trend_directions) / len(trend_directions) if trend_directions else 0
        features.append(consensus)
        
        # Timeframe divergence
        if len(trend_directions) > 1:
            divergence = np.std(trend_directions)
            features.append(divergence)
        else:
            features.append(0.0)
            
        return features

    def _get_breakout_trend_alignment_features(self, df, current_row, config):
        """Extract breakout features aligned with trend"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # Breakout direction vs trend direction
        if current_idx >= 100:
            # Recent high/low breakout
            recent_high = df['high'].rolling(20).max().iloc[current_idx-1]
            recent_low = df['low'].rolling(20).min().iloc[current_idx-1]
            current_price = df['close'].iloc[current_idx]
            
            breakout_up = 1 if current_price > recent_high else 0
            breakout_down = 1 if current_price < recent_low else 0
            
            # Trend direction
            trend_direction = (df['close'].rolling(50).mean().iloc[current_idx] - df['close'].rolling(100).mean().iloc[current_idx]) / df['close'].iloc[current_idx]
            trend_up = 1 if trend_direction > 0 else 0
            
            # Alignment: breakout in trend direction
            aligned_breakout = (breakout_up * trend_up) + (breakout_down * (1 - trend_up))
            features.append(aligned_breakout)
        else:
            features.append(0.0)
            
        # Breakout strength in trend context
        if current_idx >= 50:
            volatility = df['close'].rolling(20).std().iloc[current_idx]
            price_move = abs(df['close'].iloc[current_idx] - df['close'].iloc[current_idx-1])
            breakout_strength = price_move / volatility if volatility > 0 else 0
            features.append(breakout_strength if not np.isnan(breakout_strength) else 0.0)
        else:
            features.append(0.0)
            
        return features

    def _get_volatility_trend_context_features(self, df, current_row, config):
        """Extract volatility features in trend context"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # Volatility regime vs trend strength
        if current_idx >= 100:
            volatility = df['close'].rolling(20).std().iloc[current_idx]
            avg_volatility = df['close'].rolling(100).std().mean()
            vol_regime = volatility / avg_volatility if avg_volatility > 0 else 1
            
            trend_strength = abs((df['close'].rolling(50).mean().iloc[current_idx] - df['close'].rolling(100).mean().iloc[current_idx]) / df['close'].iloc[current_idx])
            
            # High volatility + strong trend = trend continuation
            # High volatility + weak trend = potential reversal
            vol_trend_signal = vol_regime * trend_strength
            features.append(vol_trend_signal if not np.isnan(vol_trend_signal) else 0.0)
        else:
            features.append(0.0)
            
        # Volatility compression in trend
        if current_idx >= 50:
            recent_vol = df['close'].rolling(10).std().iloc[current_idx]
            historical_vol = df['close'].rolling(50).std().iloc[current_idx]
            compression = recent_vol / historical_vol if historical_vol > 0 else 1
            features.append(compression if not np.isnan(compression) else 1.0)
        else:
            features.append(1.0)
            
        return features

    def _get_macro_trend_alignment_features(self, df, current_row, config):
        """Extract macro trend alignment features"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # Long-term trend vs short-term signals
        if current_idx >= 2880:  # 2 days of data
            macro_trend = (df['close'].rolling(1440).mean().iloc[current_idx] - df['close'].rolling(2880).mean().iloc[current_idx]) / df['close'].iloc[current_idx]
            short_signal = (df['close'].iloc[current_idx] - df['close'].rolling(60).mean().iloc[current_idx]) / df['close'].iloc[current_idx]
            
            # Alignment between macro trend and short-term signal
            alignment = macro_trend * short_signal
            features.append(alignment if not np.isnan(alignment) else 0.0)
        else:
            features.append(0.0)
            
        # Macro trend strength
        if current_idx >= 1440:
            macro_strength = abs((df['close'].iloc[current_idx] - df['close'].iloc[current_idx-1440]) / df['close'].iloc[current_idx-1440])
            features.append(macro_strength if not np.isnan(macro_strength) else 0.0)
        else:
            features.append(0.0)
            
        return features

    def _get_level_trend_context_features(self, df, current_row, config):
        """Extract support/resistance features in trend context"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # Support/resistance strength in trending market
        if current_idx >= 100:
            # Find recent support/resistance levels
            lookback = min(100, current_idx)
            highs = df['high'].iloc[current_idx-lookback:current_idx]
            lows = df['low'].iloc[current_idx-lookback:current_idx]
            
            resistance = highs.max()
            support = lows.min()
            current_price = df['close'].iloc[current_idx]
            
            # Distance to key levels
            dist_to_resistance = (resistance - current_price) / current_price
            dist_to_support = (current_price - support) / current_price
            
            features.extend([dist_to_resistance, dist_to_support])
            
            # Level sustainability in trend
            trend_direction = (df['close'].rolling(50).mean().iloc[current_idx] - df['close'].rolling(100).mean().iloc[current_idx]) / df['close'].iloc[current_idx]
            
            # In uptrend, support more reliable; in downtrend, resistance more reliable
            level_sustainability = trend_direction * (dist_to_support - dist_to_resistance)
            features.append(level_sustainability if not np.isnan(level_sustainability) else 0.0)
        else:
            features.extend([0.0, 0.0, 0.0])
            
        return features

    def _get_portfolio_trend_alignment_features(self, df, current_row, config):
        """Extract portfolio allocation features based on trend"""
        features = []
        
        # Get the current index position
        current_idx = len(df) - 1  # Use the last row index
        
        # Risk-adjusted trend strength
        if current_idx >= 100:
            trend_return = (df['close'].iloc[current_idx] - df['close'].iloc[current_idx-50]) / df['close'].iloc[current_idx-50]
            volatility = df['close'].rolling(50).std().iloc[current_idx]
            
            risk_adjusted_trend = trend_return / volatility if volatility > 0 else 0
            features.append(risk_adjusted_trend if not np.isnan(risk_adjusted_trend) else 0.0)
        else:
            features.append(0.0)
            
        # Allocation confidence based on trend consistency
        if current_idx >= 50:
            returns = df['close'].pct_change().iloc[current_idx-49:current_idx+1]
            positive_returns = (returns > 0).sum()
            consistency = positive_returns / len(returns) if len(returns) > 0 else 0.5
            
            # Higher consistency = higher allocation confidence
            allocation_confidence = abs(consistency - 0.5) * 2  # Scale to 0-1
            features.append(allocation_confidence)
        else:
            features.append(0.0)
            
        return features

    def cleanup(self):
        """Clean up resources."""
        logger.info("Cleaning up AI Model Manager...")

        # Clear models from memory
        self.models.clear()
        self.scalers.clear()
        self.label_encoders.clear()
        self.model_performance.clear()

        logger.info("AI Model Manager cleanup completed")
