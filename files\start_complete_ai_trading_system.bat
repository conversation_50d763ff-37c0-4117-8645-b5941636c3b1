@echo off
REM ============================================================================
REM AI TRADING SYSTEM - COMPLETE STARTUP ORCHESTRATOR
REM ============================================================================
REM This batch file orchestrates the complete startup sequence:
REM 1. Environment & MT5 Validation (30s)
REM 2. Smart Data Collection (1-3min)
REM 3. Model Loading/Training (1-5min)
REM 4. Trading System Activation (30s)
REM 5. Dashboard Launch (15s)
REM ============================================================================

title AI Trading System - Complete Startup

echo.
echo ================================================================================
echo                    AI TRADING SYSTEM - COMPLETE STARTUP
echo ================================================================================
echo.
echo STARTING COMPREHENSIVE AI TRADING SYSTEM
echo.
echo STARTUP SEQUENCE:
echo    1. Environment ^& MT5 Validation (30s)
echo    2. Smart Data Collection (1-3min)
echo    3. Model Loading/Training (1-5min)
echo    4. Trading System Activation (30s)
echo    5. Dashboard Launch (15s)
echo.
echo FAIL-FAST MODE: Any failure stops entire startup
echo RETRY LOGIC: 3 attempts per component
echo 100%% FUNCTIONALITY: No partial system starts
echo.
echo ================================================================================
echo.

REM Change to the correct directory
cd /d "%~dp0"

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found!
    echo Please ensure venv is created in the project directory.
    echo Run: python -m venv venv
    echo Then: venv\Scripts\activate.bat
    echo Then: pip install -r requirements.txt
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Check if activation was successful
if "%VIRTUAL_ENV%"=="" (
    echo ERROR: Failed to activate virtual environment!
    pause
    exit /b 1
)

echo SUCCESS: Virtual environment activated: %VIRTUAL_ENV%
echo.

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Start the complete system orchestrator
echo Starting AI Trading System Orchestrator...
echo.
echo This will take 3-15 minutes depending on system state...
echo Progress will be shown in real-time
echo.

python system_orchestrator.py

REM Check if the orchestrator completed successfully
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ================================================================================
    echo SUCCESS: AI TRADING SYSTEM STARTUP COMPLETED SUCCESSFULLY!
    echo ================================================================================
    echo.
    echo System Status: FULLY OPERATIONAL
    echo Dashboard: http://localhost:5000
    echo AI Models: All 9 models loaded and active
    echo Real-time Monitoring: ACTIVE
    echo Trading System: READY
    echo.
    echo IMPORTANT: Keep this window open to maintain system operation
    echo To stop the system: Press Ctrl+C
    echo.
    echo ================================================================================
    echo.

    REM Keep the window open
    echo Press any key to stop the AI Trading System...
    pause >nul

    echo.
    echo Stopping AI Trading System...
    echo.
    
) else (
    echo.
    echo ================================================================================
    echo ERROR: AI TRADING SYSTEM STARTUP FAILED!
    echo ================================================================================
    echo.
    echo Error Level: %ERRORLEVEL%
    echo Check the logs for detailed error information:
    echo    - logs\system_startup.log
    echo    - logs\synthetic_trading_system.log
    echo.
    echo Common Solutions:
    echo    1. Ensure MT5 is running and logged in
    echo    2. Check internet connection
    echo    3. Verify all dependencies are installed
    echo    4. Check disk space for data collection
    echo.
    echo ================================================================================
    echo.
    pause
)

REM Cleanup
echo Cleaning up...
echo.

REM Deactivate virtual environment
deactivate

echo SUCCESS: AI Trading System shutdown completed.
echo.
pause
