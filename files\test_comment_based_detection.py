#!/usr/bin/env python3
"""
Test script to verify the new comment-based timeframe detection function.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_comment_based_detection():
    """Test the new comment-based timeframe detection function."""
    
    print("🧪 TESTING NEW COMMENT-BASED TIMEFRAME DETECTION")
    print("=" * 60)
    
    try:
        # Import the order execution system
        from synthetic_data_collector import Synthetic<PERSON><PERSON><PERSON>ollector
        from order_execution_system import OrderExecutionSystem
        
        print("✅ Components imported successfully")
        
        # Initialize minimal components
        data_collector = SyntheticDataCollector()
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Order execution system initialized")
        
        # Test comment patterns
        test_comments = [
            # NEW TIMEFRAME-SPECIFIC COMMENTS (should work)
            "AI_BOT_SHORT_STRONG_BUY",
            "AI_BOT_SHORT_WEAK_BUY",
            "AI_BOT_SHORT_STRONG_SELL",
            "AI_BOT_SHORT_WEAK_SELL",
            "AI_BOT_MEDIUM_STRONG_BUY",
            "AI_BOT_MEDIUM_WEAK_SELL",
            "AI_BOT_LONG_STRONG_BUY",
            "AI_BOT_LONG_WEAK_BUY",
            "AI_BOT_SHORT_Close_SL",
            "AI_BOT_MEDIUM_Close_TP",
            "AI_BOT_LONG_Close_Manual",

            # OLD COMMENTS (should return None)
            "AI_BOT_STRONG_BUY",
            "AI_BOT_WEAK_SELL",
            "AI_Signal_WEAK_B",
            "Simple_Test_Order",

            # EDGE CASES
            "",
            None,
            "AI_BOT_SHORT_STRONG_BUY_Extra_Text",
            "PREFIX_AI_BOT_MEDIUM_WEAK_SELL",
        ]
        
        print("\n🔍 TESTING COMMENT PATTERNS:")
        print("-" * 60)
        
        success_count = 0
        total_tests = len(test_comments)

        for comment in test_comments:
            try:
                detected_timeframe = order_executor._get_timeframe_from_comment(comment)

                # Expected results (UPDATED: Only check for timeframe part, not the underscore)
                expected = None
                if comment and "AI_BOT_SHORT" in comment:
                    expected = "short_term"
                elif comment and "AI_BOT_MEDIUM" in comment:
                    expected = "medium_term"
                elif comment and "AI_BOT_LONG" in comment:
                    expected = "long_term"

                # Check result
                status = "✅ PASS" if detected_timeframe == expected else "❌ FAIL"
                if detected_timeframe == expected:
                    success_count += 1

                comment_display = f'"{comment}"' if comment else "None"
                expected_display = expected or "None"
                detected_display = detected_timeframe or "None"

                print(f"   {status} Comment: {comment_display:<35} → Expected: {expected_display:<12} | Got: {detected_display}")

            except Exception as e:
                print(f"   ❌ ERROR Comment: {comment} → Exception: {e}")
                total_tests -= 1  # Don't count error cases
        
        print("-" * 60)
        print(f"📊 TEST RESULTS: {success_count}/{total_tests} tests passed ({success_count/total_tests*100:.1f}%)")
        
        if success_count == total_tests:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Comment-based timeframe detection is working correctly")
            print("✅ New trades with timeframe comments will be properly identified")
            print("✅ Old trades without timeframe info will be marked as 'unknown'")
        else:
            print(f"\n⚠️ {total_tests - success_count} tests failed")
            print("❌ Comment-based detection needs adjustment")
        
        return success_count == total_tests
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 AI TRADING SYSTEM - COMMENT-BASED DETECTION TEST")
    print("=" * 70)
    print("This will test the new comment-based timeframe detection function")
    print("=" * 70)
    
    success = test_comment_based_detection()
    
    if success:
        print("\n🎉 TEST COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ Comment-based timeframe detection is working")
        print("✅ System can now identify trades by comment patterns")
        print("✅ New trades will be properly categorized by timeframe")
        print("=" * 50)
        print("\n📋 NEXT STEPS:")
        print("1. ✅ Function added and tested")
        print("2. 🔄 Dashboard can now use comment-based identification")
        print("3. 🎯 New trades will have proper timeframe comments")
        print("4. 📊 Historical analysis will work for new trades")
    else:
        print("\n❌ TEST FAILED!")
        print("Check the error messages above for details")
        
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
