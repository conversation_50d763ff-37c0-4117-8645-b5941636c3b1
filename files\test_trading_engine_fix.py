"""
Test script to verify the trading engine fix.
This will test if the trading signal generator is actually being called.
"""

import sys
import time
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trading_engine_test.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("TradingEngineTest")

def test_signal_generation():
    """Test if the trading signal generator is working."""
    print("🧪 TESTING TRADING SIGNAL GENERATION")
    print("=" * 50)
    
    try:
        # Import components
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        from trading_signal_generator import TradingSignalGenerator
        from order_execution_system import OrderExecutionSystem
        
        print("✅ All components imported successfully")
        
        # Initialize components
        print("🔧 Initializing components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        order_executor = OrderExecutionSystem(data_collector)
        
        print("✅ Components initialized")
        
        # Check MT5 connection
        if not data_collector.mt5_connected:
            print("❌ MT5 not connected!")
            return False
            
        print("✅ MT5 connected")
        
        # Load at least one model
        print("📚 Loading AI models...")
        loaded_count = 0
        for model_name in ai_manager.model_configs.keys():
            if ai_manager.load_model(model_name):
                loaded_count += 1
                print(f"✅ Loaded: {model_name}")
                if loaded_count >= 3:  # Load at least 3 models for testing
                    break
                    
        if loaded_count == 0:
            print("❌ No models loaded! Run train_all_models.py first")
            return False
            
        print(f"✅ {loaded_count} models loaded")
        
        # Get current price
        print("💰 Getting current price...")
        current_price = order_executor._get_current_price()
        if current_price is None:
            print("❌ Could not get current price!")
            return False
            
        print(f"✅ Current price: {current_price:.2f}")
        
        # Test signal generation
        print("🎯 Testing signal generation...")
        print("⏳ This may take 30-60 seconds...")
        
        signal = signal_generator.generate_signal(current_price)
        
        if signal:
            print("🎉 SIGNAL GENERATED SUCCESSFULLY!")
            print(f"   Signal Type: {signal.signal_type.name}")
            print(f"   Confidence: {signal.confidence:.3f}")
            print(f"   Entry Price: {signal.entry_price:.2f}")
            print(f"   Stop Loss: {signal.stop_loss:.2f}")
            print(f"   Take Profit: {signal.take_profit:.2f}")
            print(f"   Risk/Reward: {signal.risk_reward_ratio:.2f}")
            print(f"   Reasoning: {signal.reasoning[:100]}...")
            return True
        else:
            print("ℹ️  No signal generated (this is normal)")
            print("   The AI models may not see a trading opportunity right now")
            print("   This confirms the signal generator is working correctly")
            return True
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        logger.error(f"Test failed: {e}")
        return False

def test_trading_engine_startup():
    """Test if the trading engine can start properly."""
    print("\n🧪 TESTING TRADING ENGINE STARTUP")
    print("=" * 50)
    
    try:
        from trading_engine import TradingEngine
        
        print("🔧 Creating trading engine...")
        engine = TradingEngine()
        
        print("📚 Initializing components...")
        if not engine.initialize_components():
            print("❌ Component initialization failed")
            return False
            
        print("✅ Components initialized successfully")
        
        print("🚀 Testing trading system startup...")
        if not engine.start_trading():
            print("❌ Trading system startup failed")
            return False
            
        print("✅ Trading system started successfully!")
        
        # Let it run for 30 seconds to test
        print("⏳ Running for 30 seconds to test signal generation...")
        time.sleep(30)
        
        # Check if it's still running
        if engine.running:
            print("✅ Trading engine is running and generating signals!")
            
            # Get status
            status = engine.get_system_status()
            print(f"   Cycles completed: {status['trading_stats']['cycles_completed']}")
            print(f"   Signals generated: {status['trading_stats']['signals_generated']}")
            print(f"   Trades executed: {status['trading_stats']['trades_executed']}")
        else:
            print("❌ Trading engine stopped unexpectedly")
            return False
            
        # Stop the engine
        print("🛑 Stopping trading engine...")
        engine.stop_trading("Test completed")
        
        print("✅ Trading engine test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Trading engine test failed: {e}")
        logger.error(f"Trading engine test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 AI TRADING SYSTEM - SIGNAL GENERATION TEST")
    print("=" * 60)
    print("🎯 Testing if the trading signal generator fix works")
    print("=" * 60)
    
    # Test 1: Signal generation
    test1_result = test_signal_generation()
    
    # Test 2: Trading engine startup
    test2_result = test_trading_engine_startup()
    
    # Results
    print("\n📊 TEST RESULTS")
    print("=" * 30)
    print(f"Signal Generation Test: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Trading Engine Test: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The trading signal generator fix is working correctly")
        print("🚀 You can now start the full system with confidence")
    else:
        print("\n❌ SOME TESTS FAILED")
        print("🔧 Check the logs for detailed error information")
        
    return test1_result and test2_result

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
