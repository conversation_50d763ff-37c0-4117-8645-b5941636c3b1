#!/usr/bin/env python3
"""
Test the dashboard's new live price update mechanism.
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dashboard_live_price():
    """Test the dashboard's live price update mechanism."""
    try:
        print("🔍 TESTING DASHBOARD LIVE PRICE UPDATE")
        print("=" * 60)
        
        # Import the dashboard server
        from dashboard_server import DashboardDataManager
        from synthetic_data_collector import SyntheticDataCollector
        import MetaTrader5 as mt5
        
        print("📊 Initializing dashboard data manager...")
        
        # Create data collector
        data_collector = SyntheticDataCollector()
        if not data_collector.mt5_connected:
            print("❌ MT5 not connected!")
            return False
        
        # Create dashboard manager
        dashboard_manager = DashboardDataManager(
            data_collector=data_collector,
            ai_manager=None,
            order_executor=None,
            pattern_detector=None
        )
        
        print("✅ Dashboard manager created")
        
        # Test 1: Get live MT5 price directly
        print("\n🎯 TEST 1: DIRECT MT5 PRICE")
        tick = mt5.symbol_info_tick("DEX 900 DOWN Index")
        if tick:
            live_price = (tick.bid + tick.ask) / 2
            print(f"✅ Live MT5 Price: {live_price:.2f}")
        else:
            print("❌ Could not get live tick!")
            return False
        
        # Test 2: Test dashboard's live price method
        print("\n🎯 TEST 2: DASHBOARD LIVE PRICE METHOD")
        dashboard_live_price = dashboard_manager._get_live_mt5_price()
        if dashboard_live_price:
            print(f"✅ Dashboard Live Price: {dashboard_live_price['price']:.2f}")
            print(f"   Bid: {dashboard_live_price['bid']:.2f}")
            print(f"   Ask: {dashboard_live_price['ask']:.2f}")
            print(f"   Spread: {dashboard_live_price['spread']:.2f}")
            print(f"   Timestamp: {dashboard_live_price['timestamp']}")
            print(f"   Live Update Flag: {dashboard_live_price['live_update']}")
        else:
            print("❌ Dashboard live price method failed!")
            return False
        
        # Test 3: Test full price data update
        print("\n🎯 TEST 3: FULL PRICE DATA UPDATE")
        dashboard_manager._update_price_data()
        
        current_price_data = dashboard_manager.dashboard_data.get("current_price")
        if current_price_data:
            print(f"✅ Dashboard Current Price: {current_price_data['price']:.2f}")
            print(f"   Change: {current_price_data['change']:.2f}")
            print(f"   Change %: {current_price_data['change_pct']:.2f}%")
            print(f"   Timestamp: {current_price_data['timestamp']}")
            
            # Check if it's live data
            if current_price_data.get('live_update'):
                print(f"✅ Using LIVE MT5 data!")
            else:
                print(f"⚠️  Using database data (fallback)")
                
            # Compare with direct MT5 price
            price_diff = abs(current_price_data['price'] - live_price)
            print(f"   Price difference from MT5: {price_diff:.2f} points")
            
            if price_diff < 1.0:  # Less than 1 point difference
                print(f"✅ PRICE UPDATE WORKING! Dashboard shows live prices")
                return True
            else:
                print(f"❌ PRICE DIFFERENCE TOO LARGE! Dashboard still using stale data")
                return False
        else:
            print("❌ No current price data in dashboard!")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 DASHBOARD LIVE PRICE TEST")
    print("=" * 60)
    print("⚠️  Testing the dashboard's live price update fix")
    print("=" * 60)
    
    success = test_dashboard_live_price()
    
    if success:
        print("\n🎉 DASHBOARD LIVE PRICE TEST PASSED!")
        print("✅ Dashboard is now showing live MT5 prices!")
        print("🔄 The dashboard should now update with real-time prices")
    else:
        print("\n❌ DASHBOARD LIVE PRICE TEST FAILED!")
        print("🔧 Dashboard still needs fixing.")
        
    sys.exit(0 if success else 1)
