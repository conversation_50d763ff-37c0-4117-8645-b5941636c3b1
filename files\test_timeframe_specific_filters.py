#!/usr/bin/env python3
"""
Test Timeframe-Specific EMA/SMA Filters
Verifies that each term (short/medium/long) has its own independent filter.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from synthetic_data_collector import SyntheticDataCollector
    from ema_sma_distance_filter import EMASMADistanceFilter
    from trading_signal_generator import TradingSignalGenerator
    from ai_model_manager import AIModelManager
    from synthetic_pattern_detector import SyntheticPatternDetector
    import config
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_timeframe_specific_filters():
    """Test that each timeframe term has its own independent filter."""
    print("🔍 TESTING TIMEFRAME-SPECIFIC EMA/SMA FILTERS")
    print("=" * 60)
    
    try:
        # Initialize components
        print("📊 Initializing components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        
        print("✅ Components initialized")
        
        # Check that timeframe-specific filters are initialized
        print(f"\n🎯 Checking timeframe-specific filter initialization:")
        print(f"   Timeframe filters: {list(signal_generator.timeframe_filters.keys())}")
        print(f"   Term timeframes: {signal_generator.term_timeframes}")
        
        for term, filter_obj in signal_generator.timeframe_filters.items():
            print(f"   {term}: enabled={filter_obj.enabled}, threshold={filter_obj.min_distance_pct}%")
        
        # Test the timeframe-specific filter check method
        print(f"\n🧪 Testing timeframe-specific filter checks...")
        filter_results = signal_generator._check_timeframe_specific_filters()
        
        print(f"\n📊 Filter Results Summary:")
        print(f"{'Term':<12} {'Timeframe':<10} {'Status':<8} {'Distance':<10} {'Reason'}")
        print("-" * 60)
        
        for term, result in filter_results.items():
            timeframe = result.get('timeframe', 'N/A')
            allowed = result.get('allowed', False)
            status = "ALLOW" if allowed else "BLOCK"
            distance = result.get('details', {}).get('distance_pct', 0)
            reason = result.get('reason', 'N/A')[:20]
            
            print(f"{term:<12} {timeframe:<10} {status:<8} {distance:<10.3f}% {reason}")
        
        # Test signal filtering
        print(f"\n🧪 Testing signal filtering with mock signals...")
        
        # Create mock signals for each timeframe group
        mock_signals = [
            {
                'model_name': 'short_term_pattern_nn',
                'signal': 2,
                'confidence': 0.8,
                'timeframe_category': 'short_term'
            },
            {
                'model_name': 'medium_term_trend_lstm',
                'signal': -2,
                'confidence': 0.75,
                'timeframe_category': 'medium_term'
            },
            {
                'model_name': 'long_term_macro_dnn',
                'signal': 1,
                'confidence': 0.7,
                'timeframe_category': 'long_term'
            }
        ]
        
        # Store filter results for signal filtering
        signal_generator.current_filter_results = filter_results
        
        # Apply filters to mock signals
        filtered_signals = signal_generator._apply_timeframe_filters_to_signals(mock_signals)
        
        print(f"\n📊 Signal Filtering Results:")
        print(f"   Original signals: {len(mock_signals)}")
        print(f"   Filtered signals: {len(filtered_signals)}")
        
        for signal in mock_signals:
            model_name = signal['model_name']
            term = signal['timeframe_category']
            is_allowed = any(fs['model_name'] == model_name for fs in filtered_signals)
            status = "ALLOWED" if is_allowed else "BLOCKED"
            print(f"   {model_name} ({term}): {status}")
        
        # Test actual signal generation
        print(f"\n🧪 Testing actual signal generation...")
        current_price = 56000.0  # Approximate current price
        
        # This should trigger the new timeframe-specific filter logic
        signal = signal_generator.generate_signal(current_price, timeframe=15)
        
        if signal is None:
            print("Signal generation returned None (likely blocked by filters)")
        else:
            print(f"Signal generated: {signal.signal_type.name} with {signal.confidence:.3f} confidence")
        
        # Summary
        print(f"\n📊 SUMMARY:")
        allowed_terms = [term for term, result in filter_results.items() if result.get('allowed', False)]
        blocked_terms = [term for term, result in filter_results.items() if not result.get('allowed', False)]
        
        print(f"   Terms allowed to trade: {allowed_terms}")
        print(f"   Terms blocked from trading: {blocked_terms}")
        print(f"   Filter independence: {'WORKING' if len(set(filter_results.keys())) == 3 else 'FAILED'}")
        
        # Verify independence
        if len(allowed_terms) > 0 and len(blocked_terms) > 0:
            print(f"   INDEPENDENCE VERIFIED: Some terms allowed, others blocked")
        elif len(allowed_terms) == 3:
            print(f"   ALL TERMS ALLOWED: Check if this is expected based on current market conditions")
        elif len(blocked_terms) == 3:
            print(f"   ALL TERMS BLOCKED: Check if this is expected based on current market conditions")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing timeframe-specific filters: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_timeframe_scenarios():
    """Test how filters behave with different timeframe data."""
    print(f"\n🔍 TESTING DIFFERENT TIMEFRAME SCENARIOS")
    print("=" * 60)
    
    try:
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        
        # Test each timeframe independently
        timeframes_to_test = [5, 30, 60]  # Primary timeframes for each term
        term_names = ['short_term', 'medium_term', 'long_term']
        
        print(f"📊 Testing EMA/SMA convergence across different timeframes:")
        print(f"{'Timeframe':<10} {'Term':<12} {'EMA20':<10} {'SMA50':<10} {'Distance':<10} {'Status'}")
        print("-" * 70)
        
        for i, (tf, term) in enumerate(zip(timeframes_to_test, term_names)):
            # Get market data for this timeframe
            market_data = data_collector.get_latest_data(timeframe=tf, count=100)
            
            if market_data.empty:
                print(f"{tf:<10} {term:<12} {'NO DATA':<10} {'NO DATA':<10} {'N/A':<10} {'❌ ERROR'}")
                continue
            
            # Calculate indicators
            df_indicators = pattern_detector.calculate_synthetic_indicators(market_data)
            df_indicators['ema20'] = df_indicators['close'].ewm(span=20, adjust=False).mean()
            df_indicators['sma50'] = df_indicators['close'].rolling(window=50).mean()
            
            # Get latest values
            latest = df_indicators.iloc[-1]
            ema20 = latest.get('ema20', np.nan)
            sma50 = latest.get('sma50', np.nan)
            
            if pd.isna(ema20) or pd.isna(sma50):
                print(f"{tf:<10} {term:<12} {'NaN':<10} {'NaN':<10} {'N/A':<10} {'❌ ERROR'}")
                continue
            
            # Calculate distance
            distance_pct = abs((ema20 - sma50) / sma50) * 100
            
            # Check filter
            filter_obj = EMASMADistanceFilter(
                min_distance_pct=config.EMA_SMA_FILTER.get('min_distance_pct', 0.35),
                enabled=True
            )
            
            is_allowed, reason, details = filter_obj.is_distance_sufficient(ema20, sma50)
            status = "✅ ALLOW" if is_allowed else "🚫 BLOCK"
            
            print(f"{tf:<10} {term:<12} {ema20:<10.2f} {sma50:<10.2f} {distance_pct:<10.3f}% {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing timeframe scenarios: {e}")
        return False

def main():
    """Run comprehensive timeframe-specific filter tests."""
    print("🔍 COMPREHENSIVE TIMEFRAME-SPECIFIC FILTER TEST")
    print("=" * 70)
    
    results = {
        "timeframe_specific_filters": False,
        "timeframe_scenarios": False
    }
    
    # Run tests
    results["timeframe_specific_filters"] = test_timeframe_specific_filters()
    results["timeframe_scenarios"] = test_different_timeframe_scenarios()
    
    # Summary
    print(f"\n📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 70)
    
    for test, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test.replace('_', ' ').title()}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        print(f"\n✅ ALL TESTS PASSED - Timeframe-specific filters are working correctly!")
    else:
        print(f"\n⚠️  SOME TESTS FAILED - Review results above")
    
    return all_passed

if __name__ == "__main__":
    main()
