#!/usr/bin/env python3
"""
Quick Model Performance Checker
Loads all models and displays their training quality metrics.
"""

import sys
import os
import logging
from ai_model_manager import AIModelManager
from synthetic_data_collector import SyntheticDataCollector
from synthetic_pattern_detector import SyntheticPatternDetector

# Set up logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise

def main():
    """Check model performance."""
    try:
        print("🔍 CHECKING AI MODEL TRAINING QUALITY")
        print("=" * 60)
        
        # Initialize components
        print("Initializing components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        
        # Load all models
        print("Loading models...")
        loaded_models = []
        failed_models = []
        
        for model_name in ai_manager.model_configs.keys():
            try:
                success = ai_manager.load_model(model_name)
                if success:
                    loaded_models.append(model_name)
                    print(f"✅ {model_name}")
                else:
                    failed_models.append(model_name)
                    print(f"❌ {model_name}")
            except Exception as e:
                failed_models.append(model_name)
                print(f"❌ {model_name}: {e}")
        
        print(f"\nLoaded: {len(loaded_models)}/{len(ai_manager.model_configs)} models")
        
        # Get model status
        status = ai_manager.get_model_status()
        
        print("\n" + "=" * 60)
        print("MODEL TRAINING QUALITY ASSESSMENT")
        print("=" * 60)
        
        print(f"Total Models Configured: {status['total_configured']}")
        print(f"Models Successfully Loaded: {len(status['loaded_models'])}")
        print(f"TensorFlow Available: {status['tensorflow_available']}")
        
        print("\nINDIVIDUAL MODEL PERFORMANCE:")
        print("-" * 60)
        
        all_accuracies = []
        all_samples = []
        
        for model_name, details in status['model_details'].items():
            performance = details.get('performance', {})
            accuracy = performance.get('accuracy', 0)
            total_samples = performance.get('total_samples', 0)
            model_type = details.get('type', 'unknown')
            purpose = details.get('purpose', 'unknown')
            loaded = details.get('loaded', False)
            
            status_icon = '✅' if loaded else '❌'
            
            # Truncate purpose for display
            purpose_short = purpose[:30] + "..." if len(purpose) > 30 else purpose
            
            print(f"{status_icon} {model_name:25} | {model_type:12} | {accuracy:.3f} | {total_samples:,} | {purpose_short}")
            
            if accuracy > 0:
                all_accuracies.append(accuracy)
                all_samples.append(total_samples)
        
        # Calculate overall statistics
        if all_accuracies:
            avg_accuracy = sum(all_accuracies) / len(all_accuracies)
            total_training_samples = sum(all_samples)
            min_accuracy = min(all_accuracies)
            max_accuracy = max(all_accuracies)
            
            print("\nOVERALL TRAINING STATISTICS:")
            print("-" * 60)
            print(f"Average Accuracy: {avg_accuracy:.3f} ({avg_accuracy*100:.1f}%)")
            print(f"Minimum Accuracy: {min_accuracy:.3f} ({min_accuracy*100:.1f}%)")
            print(f"Maximum Accuracy: {max_accuracy:.3f} ({max_accuracy*100:.1f}%)")
            print(f"Total Training Samples: {total_training_samples:,}")
            print(f"Models with >50% Accuracy: {sum(1 for acc in all_accuracies if acc > 0.5)}/{len(all_accuracies)}")
            print(f"Models with >60% Accuracy: {sum(1 for acc in all_accuracies if acc > 0.6)}/{len(all_accuracies)}")
            print(f"Models with >70% Accuracy: {sum(1 for acc in all_accuracies if acc > 0.7)}/{len(all_accuracies)}")
            
            # Training quality assessment
            print("\nTRAINING QUALITY ASSESSMENT:")
            print("-" * 60)
            
            if avg_accuracy >= 0.7:
                quality = "EXCELLENT"
                emoji = "🎉"
            elif avg_accuracy >= 0.6:
                quality = "GOOD"
                emoji = "✅"
            elif avg_accuracy >= 0.5:
                quality = "ACCEPTABLE"
                emoji = "⚠️"
            else:
                quality = "NEEDS IMPROVEMENT"
                emoji = "❌"
                
            print(f"{emoji} Overall Quality: {quality}")
            print(f"Average Performance: {avg_accuracy*100:.1f}%")
            
            if total_training_samples >= 10000:
                data_quality = "EXCELLENT"
            elif total_training_samples >= 5000:
                data_quality = "GOOD"
            elif total_training_samples >= 1000:
                data_quality = "ACCEPTABLE"
            else:
                data_quality = "INSUFFICIENT"
                
            print(f"Training Data Quality: {data_quality} ({total_training_samples:,} samples)")
            
        else:
            print("\n❌ No performance data available - models may need retraining")
        
        print("\n" + "=" * 60)
        
        # Cleanup
        data_collector.cleanup()
        
    except Exception as e:
        print(f"❌ Error checking model performance: {e}")
        return False
        
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
