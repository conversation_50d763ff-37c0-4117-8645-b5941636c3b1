#!/usr/bin/env python3
"""
Test script to verify the new strong signal threshold is working.
Tests that signals with ±1 and confidence ≥ 0.7 are now treated as strong signals.
"""

import sys
import os
import logging
import numpy as np
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_strong_signal_threshold():
    """Test the new strong signal threshold logic."""
    try:
        print("🧪 TESTING NEW STRONG SIGNAL THRESHOLD")
        print("=" * 50)
        
        # Import components
        from ai_model_manager import AIModelManager
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        
        # Initialize components
        print("📊 Initializing AI components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector()
        ai_manager = AIModelManager(data_collector, pattern_detector)
        
        # Load models
        print("🤖 Loading AI models...")
        ai_manager.load_models()
        
        # Get current data
        print("📈 Getting current market data...")
        current_data = data_collector.get_latest_data(5, 100)  # 5-minute, 100 bars
        
        if current_data is None or current_data.empty:
            print("❌ No market data available")
            return False
        
        # Get ensemble prediction
        print("🧠 Getting ensemble prediction...")
        ensemble_result = ai_manager.get_ensemble_prediction(5)  # 5-minute timeframe
        
        if not ensemble_result:
            print("❌ No ensemble prediction available")
            return False
        
        print(f"\n📊 ENSEMBLE RESULTS:")
        print(f"   Signal: {ensemble_result.get('ensemble_signal', 'N/A')}")
        print(f"   Confidence: {ensemble_result.get('confidence', 0):.3f}")
        print(f"   Consensus: {ensemble_result.get('consensus', 'N/A')}")
        print(f"   Consensus Strength: {ensemble_result.get('consensus_strength', 0):.3f}")
        
        # Check individual model predictions
        individual_predictions = ensemble_result.get('individual_predictions', [])
        print(f"\n🔍 INDIVIDUAL MODEL PREDICTIONS:")
        
        strong_signals_old_criteria = []
        strong_signals_new_criteria = []
        
        for pred in individual_predictions:
            model_name = pred.get('model_name', 'Unknown')
            signal = pred.get('signal', 0)
            confidence = pred.get('confidence', 0)
            
            print(f"   📈 {model_name}:")
            print(f"      Signal: {signal}")
            print(f"      Confidence: {confidence:.3f}")
            
            # Test old criteria (only ±2 with confidence ≥ 0.6)
            if abs(signal) == 2 and confidence >= 0.6:
                strong_signals_old_criteria.append(pred)
                print(f"      ✅ OLD CRITERIA: Strong Signal")
            else:
                print(f"      ❌ OLD CRITERIA: Not Strong")
            
            # Test new criteria (±2 with confidence ≥ 0.6, OR ±1 with confidence ≥ 0.7)
            if (abs(signal) == 2 and confidence >= 0.6) or (abs(signal) == 1 and confidence >= 0.7):
                strong_signals_new_criteria.append(pred)
                print(f"      ✅ NEW CRITERIA: Strong Signal")
            else:
                print(f"      ❌ NEW CRITERIA: Not Strong")
            
            print()
        
        # Check strong signals from ensemble result
        ensemble_strong_signals = ensemble_result.get('strong_signals', [])
        
        print(f"\n⚡ STRONG SIGNALS COMPARISON:")
        print(f"   Old Criteria (±2, conf≥0.6): {len(strong_signals_old_criteria)} signals")
        print(f"   New Criteria (±2,conf≥0.6 OR ±1,conf≥0.7): {len(strong_signals_new_criteria)} signals")
        print(f"   Ensemble Reported: {len(ensemble_strong_signals)} signals")
        
        # Verify the ensemble is using new criteria
        if len(ensemble_strong_signals) == len(strong_signals_new_criteria):
            print(f"   ✅ Ensemble is using NEW criteria correctly!")
        elif len(ensemble_strong_signals) == len(strong_signals_old_criteria):
            print(f"   ❌ Ensemble is still using OLD criteria")
        else:
            print(f"   ⚠️  Ensemble count doesn't match either criteria")
        
        # Show specific examples of signals that would now trigger
        print(f"\n🎯 SIGNALS THAT NOW TRIGGER (±1 with conf≥0.7):")
        for pred in individual_predictions:
            signal = pred.get('signal', 0)
            confidence = pred.get('confidence', 0)
            model_name = pred.get('model_name', 'Unknown')
            
            # This would be new strong signals under new criteria
            if abs(signal) == 1 and confidence >= 0.7:
                print(f"   🚀 {model_name}: Signal {signal} (Confidence: {confidence:.3f})")
                print(f"      This would now trigger immediate trade!")
        
        # Test specific case from your dashboard
        print(f"\n🔍 TESTING YOUR SPECIFIC CASE:")
        print(f"   Medium Term BUY 1 signals with 66.7% and 71.8% confidence")
        
        test_cases = [
            {"signal": 1, "confidence": 0.667, "model": "medium_term_trend_lstm"},
            {"signal": 1, "confidence": 0.718, "model": "medium_term_breakout_rf"}
        ]
        
        for case in test_cases:
            signal = case["signal"]
            confidence = case["confidence"]
            model = case["model"]
            
            old_strong = abs(signal) == 2 and confidence >= 0.6
            new_strong = (abs(signal) == 2 and confidence >= 0.6) or (abs(signal) == 1 and confidence >= 0.7)
            
            print(f"   📊 {model}:")
            print(f"      Signal: {signal}, Confidence: {confidence:.3f}")
            print(f"      Old Criteria: {'✅ Strong' if old_strong else '❌ Not Strong'}")
            print(f"      New Criteria: {'✅ Strong' if new_strong else '❌ Not Strong'}")
            
            if new_strong and not old_strong:
                print(f"      🎯 This signal will NOW trigger trades!")
        
        print(f"\n✅ STRONG SIGNAL THRESHOLD TEST COMPLETE")
        print(f"💡 With the new criteria, more high-confidence signals should trigger trades")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing strong signal threshold: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        try:
            data_collector.cleanup()
        except:
            pass

if __name__ == "__main__":
    test_strong_signal_threshold()
