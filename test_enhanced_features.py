#!/usr/bin/env python3
"""
Comprehensive test suite for enhanced trading system features:
1. Position Scaling Logic
2. Time-Based Exit Logic  
3. Meta-Model Ensemble Stacking
"""

import sys
import os
import time
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock

# Add current directory to path
sys.path.append('.')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_position_scaling():
    """Test position scaling functionality."""
    logger.info("🔄 Testing Position Scaling Logic...")
    
    try:
        from order_execution_system import OrderExecutionSystem
        from trading_signal_generator import TradingSignal, SignalType
        
        # Create mock data collector
        mock_data_collector = Mock()
        
        # Create order execution system
        execution_system = OrderExecutionSystem(mock_data_collector)
        
        # Test 1: Check if scaling should be used for high confidence signal
        high_confidence_signal = TradingSignal(
            signal_type=SignalType.STRONG_BUY,
            confidence=0.8,
            entry_price=100.0,
            stop_loss=95.0,
            take_profit=110.0,
            position_size=0.01,
            risk_reward_ratio=2.0,
            timeframe=5,
            timestamp=datetime.now(),
            ai_predictions={},
            market_regime='NORMAL',
            reasoning='Test signal'
        )
        
        should_scale = execution_system._should_use_position_scaling(high_confidence_signal)
        assert should_scale == True, "High confidence signal should use scaling"
        logger.info("✅ High confidence scaling check passed")
        
        # Test 2: Check scaling parameters calculation
        scaling_params = execution_system._calculate_scaling_parameters(high_confidence_signal, 1.0)
        assert scaling_params is not None, "Scaling parameters should be calculated"
        assert 'initial_volume' in scaling_params, "Should have initial volume"
        assert 'remaining_volume' in scaling_params, "Should have remaining volume"
        assert 'scale_in_levels' in scaling_params, "Should have scale-in levels"
        assert 'scale_out_levels' in scaling_params, "Should have scale-out levels"
        
        initial_volume = scaling_params['initial_volume']
        remaining_volume = scaling_params['remaining_volume']
        assert abs(initial_volume + remaining_volume - 1.0) < 0.01, "Volumes should sum to total"
        logger.info("✅ Scaling parameters calculation passed")
        
        # Test 3: ATR distance estimation
        atr_distance = execution_system._estimate_atr_distance(high_confidence_signal)
        assert atr_distance > 0, "ATR distance should be positive"
        logger.info("✅ ATR distance estimation passed")
        
        logger.info("✅ Position Scaling Logic tests PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Position Scaling Logic tests FAILED: {e}")
        return False

def test_time_based_exits():
    """Test time-based exit functionality."""
    logger.info("🔄 Testing Time-Based Exit Logic...")
    
    try:
        from order_execution_system import OrderExecutionSystem, Position, Order, OrderType, OrderStatus
        from trading_signal_generator import TradingSignal, SignalType
        
        # Create mock data collector
        mock_data_collector = Mock()
        
        # Create order execution system
        execution_system = OrderExecutionSystem(mock_data_collector)
        
        # Create a test position
        test_signal = TradingSignal(
            signal_type=SignalType.STRONG_BUY,
            confidence=0.7,
            entry_price=100.0,
            stop_loss=95.0,
            take_profit=110.0,
            position_size=0.01,
            risk_reward_ratio=2.0,
            timeframe=1,
            timestamp=datetime.now(),
            ai_predictions={},
            market_regime='NORMAL',
            reasoning='Test signal'
        )
        
        test_order = Order(
            order_id=12345,
            signal=test_signal,
            order_type=OrderType.BUY,
            volume=0.01,
            price=100.0,
            stop_loss=95.0,
            take_profit=110.0,
            status=OrderStatus.FILLED,
            timestamp=datetime.now()
        )
        
        # Test position that's been open for 2 minutes (should trigger time profit)
        old_time = datetime.now() - timedelta(minutes=2)
        profitable_position = Position(
            position_id=12345,
            symbol="DEX900DOWN",
            volume=0.01,
            price_open=100.0,
            price_current=105.0,
            stop_loss=95.0,
            take_profit=110.0,
            profit=50.0,  # Profitable
            swap=0.0,
            commission=0.0,
            time_open=old_time,
            order=test_order
        )
        
        # Mock the close_position method
        execution_system.close_position = Mock(return_value=True)
        
        # Test time-based exit logic
        execution_system._handle_time_based_exits(profitable_position)
        
        # Should have called close_position for time profit
        execution_system.close_position.assert_called_with(12345, "Time_Profit")
        logger.info("✅ Time-based profit exit test passed")
        
        # Test maximum hold time
        very_old_position = Position(
            position_id=12346,
            symbol="DEX900DOWN",
            volume=0.01,
            price_open=100.0,
            price_current=98.0,
            stop_loss=95.0,
            take_profit=110.0,
            profit=-20.0,  # Losing
            swap=0.0,
            commission=0.0,
            time_open=datetime.now() - timedelta(minutes=6),  # 6 minutes old
            order=test_order
        )
        
        execution_system.close_position.reset_mock()
        execution_system._handle_time_based_exits(very_old_position)
        
        # Should have called close_position for max hold time
        execution_system.close_position.assert_called_with(12346, "Max_Hold_Time")
        logger.info("✅ Maximum hold time exit test passed")
        
        logger.info("✅ Time-Based Exit Logic tests PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Time-Based Exit Logic tests FAILED: {e}")
        return False

def test_meta_model_ensemble():
    """Test meta-model ensemble functionality."""
    logger.info("🔄 Testing Meta-Model Ensemble Stacking...")
    
    try:
        from meta_model_ensemble import MetaModelEnsemble
        
        # Test 1: Meta-model initialization
        model_names = [
            "short_term_pattern_nn", "short_term_momentum_rf", "short_term_reversion_gb",
            "medium_term_trend_lstm", "medium_term_breakout_rf", "medium_term_volatility_xgb",
            "long_term_macro_dnn", "long_term_levels_rf", "long_term_portfolio_gb"
        ]
        
        meta_model = MetaModelEnsemble(model_names)
        assert meta_model.model_names == model_names, "Model names should be stored correctly"
        logger.info("✅ Meta-model initialization passed")
        
        # Test 2: Feature vector creation
        base_predictions = {
            "short_term_pattern_nn": {"signal": 1, "confidence": 0.8},
            "medium_term_trend_lstm": {"signal": 1, "confidence": 0.7},
            "long_term_macro_dnn": {"signal": 0, "confidence": 0.5}
        }
        
        market_features = {
            "volatility": 0.02,
            "trend_strength": 0.5,
            "volume_ratio": 1.2,
            "price_momentum": 0.1
        }
        
        features = meta_model._create_feature_vector(base_predictions, market_features)
        assert len(features) > 0, "Feature vector should not be empty"
        logger.info("✅ Feature vector creation passed")
        
        # Test 3: Prediction without training (should use fallback)
        prediction = meta_model.predict(base_predictions, market_features)
        assert 'signal' in prediction, "Prediction should contain signal"
        assert 'confidence' in prediction, "Prediction should contain confidence"
        assert prediction['meta_model_used'] == False, "Should use fallback when not trained"
        logger.info("✅ Fallback prediction passed")
        
        # Test 4: Training data collection
        meta_model.collect_training_data(
            base_predictions=base_predictions,
            market_features=market_features,
            actual_outcome=1,
            prediction_time=datetime.now()
        )
        
        assert len(meta_model.training_data) > 0, "Training data should be collected"
        logger.info("✅ Training data collection passed")
        
        # Test 5: Performance report
        report = meta_model.get_performance_report()
        logger.info(f"Performance report keys: {list(report.keys())}")
        assert 'is_trained' in report, f"Report should contain training status. Got: {report}"
        assert 'training_samples_collected' in report, f"Report should contain sample count. Got: {report}"
        logger.info("✅ Performance report generation passed")
        
        logger.info("✅ Meta-Model Ensemble Stacking tests PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Meta-Model Ensemble Stacking tests FAILED: {e}")
        return False

def test_ai_model_manager_integration():
    """Test AI Model Manager integration with meta-model."""
    logger.info("🔄 Testing AI Model Manager Integration...")
    
    try:
        # Mock the required dependencies
        mock_data_collector = Mock()
        mock_pattern_detector = Mock()
        
        from ai_model_manager import AIModelManager
        
        # Create AI Model Manager
        ai_manager = AIModelManager(mock_data_collector, mock_pattern_detector)
        
        # Test 1: Meta-model should be initialized
        assert hasattr(ai_manager, 'meta_model'), "AI Manager should have meta-model"
        assert ai_manager.meta_model is not None, "Meta-model should be initialized"
        logger.info("✅ Meta-model integration passed")
        
        # Test 2: Market features extraction
        test_features = np.array([100.0, 101.0, 99.0, 102.0, 98.0])
        market_features = ai_manager._extract_market_features(test_features)
        
        required_keys = ['volatility', 'trend_strength', 'volume_ratio', 'price_momentum']
        for key in required_keys:
            assert key in market_features, f"Market features should contain {key}"
        logger.info("✅ Market features extraction passed")
        
        # Test 3: Meta-model outcome update
        base_predictions = {
            "short_term_pattern_nn": {"signal": 1, "confidence": 0.8}
        }
        
        ai_manager.update_meta_model_with_outcome(
            base_predictions=base_predictions,
            market_features=market_features,
            actual_outcome=1
        )
        logger.info("✅ Meta-model outcome update passed")
        
        logger.info("✅ AI Model Manager Integration tests PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ AI Model Manager Integration tests FAILED: {e}")
        return False

def main():
    """Run all enhanced feature tests."""
    logger.info("=" * 60)
    logger.info("ENHANCED TRADING SYSTEM FEATURES TEST SUITE")
    logger.info("=" * 60)
    
    test_results = []
    
    # Test 1: Position Scaling
    logger.info("\n1. Testing Position Scaling Logic...")
    test_results.append(test_position_scaling())
    
    # Test 2: Time-Based Exits
    logger.info("\n2. Testing Time-Based Exit Logic...")
    test_results.append(test_time_based_exits())
    
    # Test 3: Meta-Model Ensemble
    logger.info("\n3. Testing Meta-Model Ensemble Stacking...")
    test_results.append(test_meta_model_ensemble())
    
    # Test 4: AI Model Manager Integration
    logger.info("\n4. Testing AI Model Manager Integration...")
    test_results.append(test_ai_model_manager_integration())
    
    # Results Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    test_names = [
        "Position Scaling Logic",
        "Time-Based Exit Logic", 
        "Meta-Model Ensemble Stacking",
        "AI Model Manager Integration"
    ]
    
    passed_tests = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{i+1}. {name}: {status}")
        if result:
            passed_tests += 1
    
    logger.info(f"\nOverall: {passed_tests}/{len(test_results)} tests passed")
    
    if passed_tests == len(test_results):
        logger.info("\n🎉 ALL ENHANCED FEATURES TESTS PASSED!")
        logger.info("The enhanced trading system features are working correctly.")
        return True
    else:
        logger.error(f"\n💥 {len(test_results) - passed_tests} tests failed!")
        logger.error("Some enhanced features need attention.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
