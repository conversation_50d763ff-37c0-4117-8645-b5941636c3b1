"""
Synthetic Data Collector for DEX 900 DOWN Index AI Trading System.
Specialized for collecting and processing Deriv's synthetic algorithmic data.
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import MetaTrader5 as mt5
import sqlite3
import logging
from typing import Dict, List, Tuple, Optional, Union
import time
import threading
from queue import Queue
import json

import config

# Set up logging
logging.basicConfig(
    level=getattr(logging, config.LOGGING_CONFIG["level"]),
    format=config.LOGGING_CONFIG["format"],
    handlers=[
        logging.FileHandler("logs/synthetic_data_collector.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SyntheticDataCollector")

class SyntheticDataCollector:
    """
    Specialized data collector for Deriv's synthetic DEX 900 DOWN Index.
    Focuses on pattern recognition and algorithmic behavior analysis.
    """
    
    def __init__(self):
        """Initialize the Synthetic Data Collector."""
        self.symbol = config.SYMBOL
        self.ensure_directories()
        self.init_database()
        self.mt5_connected = False
        self.real_time_queue = Queue()
        self.is_collecting = False
        
        # Connect to MT5
        self.connect_mt5()
        
        # Initialize data storage
        self.tick_buffer = []
        self.pattern_cache = {}
        
    def ensure_directories(self):
        """Ensure all necessary directories exist."""
        for directory in config.DIRECTORIES:
            os.makedirs(directory, exist_ok=True)
            
    def init_database(self):
        """Initialize SQLite database for efficient data storage."""
        self.db_path = os.path.join(config.DATA_CACHE_DIR, "synthetic_data.db")
        self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
        
        # Create tables for different data types
        self.create_tables()
        
    def create_tables(self):
        """Create database tables for synthetic data storage."""
        cursor = self.conn.cursor()
        
        # Tick data table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tick_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp REAL,
                bid REAL,
                ask REAL,
                last REAL,
                volume INTEGER,
                time_msc INTEGER,
                flags INTEGER,
                volume_real REAL
            )
        """)
        
        # OHLCV data table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ohlcv_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp REAL,
                timeframe INTEGER,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume INTEGER,
                spread INTEGER
            )
        """)
        
        # Pattern events table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS pattern_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp REAL,
                event_type TEXT,
                magnitude REAL,
                duration INTEGER,
                pre_pattern TEXT,
                post_pattern TEXT,
                confidence REAL
            )
        """)
        
        # Synthetic indicators table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS synthetic_indicators (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp REAL,
                timeframe INTEGER,
                jumpiness_score REAL,
                volatility_compression REAL,
                price_acceleration REAL,
                tick_velocity REAL,
                regime_state TEXT,
                pattern_similarity REAL
            )
        """)
        
        self.conn.commit()
        logger.info("Database tables created successfully")
        
    def connect_mt5(self):
        """Establish connection to MetaTrader 5."""
        try:
            if not mt5.initialize():
                logger.error("MetaTrader5 initialization failed")
                mt5_error = mt5.last_error()
                logger.error(f"Error code: {mt5_error[0]}, Error: {mt5_error[1]}")
                return False
                
            # Check connection
            if not mt5.terminal_info().connected:
                logger.warning("MetaTrader5 is not connected to broker")
                return False
                
            # Verify symbol exists
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                logger.error(f"Symbol {self.symbol} not found")
                # Try to find similar symbols
                self.find_similar_symbols()
                return False
                
            # Enable symbol
            if not mt5.symbol_select(self.symbol, True):
                logger.error(f"Failed to select symbol {self.symbol}")
                return False
                
            self.mt5_connected = True
            logger.info(f"Successfully connected to MT5 and selected {self.symbol}")
            
            # Log symbol information
            self.log_symbol_info(symbol_info)
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to MT5: {e}")
            return False
            
    def find_similar_symbols(self):
        """Find symbols similar to DEX 900 DOWN Index."""
        try:
            symbols = mt5.symbols_get()
            if symbols:
                # Look for DEX or DOWN in symbol names
                similar = [s.name for s in symbols if 'DEX' in s.name or 'DOWN' in s.name]
                if similar:
                    logger.info(f"Similar symbols found: {similar}")
                else:
                    # Show first 20 available symbols
                    available = [s.name for s in symbols[:20]]
                    logger.info(f"Available symbols (first 20): {available}")
        except Exception as e:
            logger.error(f"Error finding similar symbols: {e}")
            
    def log_symbol_info(self, symbol_info):
        """Log detailed symbol information."""
        info_dict = symbol_info._asdict()
        logger.info(f"Symbol: {info_dict.get('name')}")
        logger.info(f"Description: {info_dict.get('description')}")
        logger.info(f"Point: {info_dict.get('point')}")
        logger.info(f"Digits: {info_dict.get('digits')}")
        logger.info(f"Spread: {info_dict.get('spread')}")
        logger.info(f"Trade mode: {info_dict.get('trade_mode')}")
        
    def collect_historical_data(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None):
        """
        Collect complete historical data for pattern analysis.
        This is crucial for understanding Deriv's algorithmic behavior.
        Uses timeframe-specific historical ranges for optimal data availability.
        """
        if not self.mt5_connected:
            logger.error("MT5 not connected. Cannot collect historical data.")
            return False

        if end_date is None:
            end_date = datetime.now()

        # Use default start_date only for longer timeframes
        default_start_date = config.START_DATE if start_date is None else start_date

        logger.info(f"Collecting historical data with timeframe-specific ranges ending at {end_date}")

        # Collect data for all timeframes with optimized date ranges
        for timeframe_name, timeframe_config in config.SYNTHETIC_TIMEFRAMES.items():
            if timeframe_name == "tick":
                continue  # Skip tick data for historical collection

            intervals = timeframe_config.get("intervals", [])
            for interval in intervals:
                # Calculate optimal start date for this specific timeframe
                optimal_start_date = self._get_optimal_start_date(interval, end_date, default_start_date)

                logger.info(f"Collecting {interval}min data for {timeframe_name} from {optimal_start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
                self.collect_timeframe_data(interval, optimal_start_date, end_date)

        logger.info("Historical data collection completed")
        return True

    def _get_optimal_start_date(self, interval: int, end_date: datetime, default_start_date: datetime) -> datetime:
        """
        Calculate optimal start date for historical data collection based on timeframe.
        Synthetic instruments have limited historical depth for high-frequency data.
        """
        # Define optimal historical ranges for different timeframes
        if interval == 1:  # 1-minute data
            # Synthetic instruments typically have 30-60 days of 1-minute data
            optimal_days = 30
            logger.debug(f"Using {optimal_days} days for 1-minute data (synthetic limitation)")
        elif interval == 5:  # 5-minute data
            # 5-minute data usually available for 90 days
            optimal_days = 90
            logger.debug(f"Using {optimal_days} days for 5-minute data")
        elif interval <= 30:  # 15-minute and 30-minute data
            # Medium frequency data available for longer periods
            optimal_days = 180  # 6 months
            logger.debug(f"Using {optimal_days} days for {interval}-minute data")
        else:  # 60+ minute data (hourly, 4-hour, daily)
            # Long-term data - use full historical range
            logger.debug(f"Using full historical range for {interval}-minute data")
            return default_start_date

        # Calculate start date based on optimal days
        optimal_start_date = end_date - timedelta(days=optimal_days)

        # Don't go earlier than the default start date
        return max(optimal_start_date, default_start_date)
        
    def collect_timeframe_data(self, interval: int, start_date: datetime, end_date: datetime):
        """Collect data for a specific timeframe."""
        try:
            # Convert interval to MT5 timeframe
            mt5_timeframe = self.get_mt5_timeframe(interval)
            
            # Fetch rates
            rates = mt5.copy_rates_range(self.symbol, mt5_timeframe, start_date, end_date)
            
            if rates is None or len(rates) == 0:
                logger.warning(f"No data received for {interval}min timeframe")
                return
                
            # Convert to DataFrame
            df = pd.DataFrame(rates)
            df['timestamp'] = pd.to_datetime(df['time'], unit='s')
            
            # Store in database
            self.store_ohlcv_data(df, interval)
            
            # Cache recent data for quick access
            cache_file = os.path.join(
                config.SYNTHETIC_CACHE_DIR, 
                f"{self.symbol}_{interval}min_latest.csv"
            )
            df.to_csv(cache_file, index=False)
            
            logger.info(f"Collected {len(df)} records for {interval}min timeframe")
            
        except Exception as e:
            logger.error(f"Error collecting {interval}min data: {e}")
            
    def get_mt5_timeframe(self, interval: int) -> int:
        """Convert interval in minutes to MT5 timeframe constant."""
        timeframe_map = {
            1: mt5.TIMEFRAME_M1,
            5: mt5.TIMEFRAME_M5,
            15: mt5.TIMEFRAME_M15,
            30: mt5.TIMEFRAME_M30,
            60: mt5.TIMEFRAME_H1,
            240: mt5.TIMEFRAME_H4,
            1440: mt5.TIMEFRAME_D1
        }
        
        return timeframe_map.get(interval, mt5.TIMEFRAME_M1)
        
    def store_ohlcv_data(self, df: pd.DataFrame, timeframe: int):
        """Store OHLCV data in database."""
        try:
            cursor = self.conn.cursor()
            
            for _, row in df.iterrows():
                cursor.execute("""
                    INSERT OR REPLACE INTO ohlcv_data 
                    (timestamp, timeframe, open, high, low, close, volume, spread)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row['time'],
                    timeframe,
                    row['open'],
                    row['high'], 
                    row['low'],
                    row['close'],
                    row['tick_volume'],
                    row.get('spread', 0)
                ))
                
            self.conn.commit()
            logger.debug(f"Stored {len(df)} OHLCV records for {timeframe}min")
            
        except Exception as e:
            logger.error(f"Error storing OHLCV data: {e}")
            
    def start_real_time_collection(self):
        """Start real-time data collection in a separate thread."""
        if self.is_collecting:
            logger.warning("Real-time collection already running")
            return
            
        self.is_collecting = True
        self.real_time_thread = threading.Thread(target=self._real_time_collector)
        self.real_time_thread.daemon = True
        self.real_time_thread.start()
        
        logger.info("Real-time data collection started")
        
    def _real_time_collector(self):
        """Real-time data collection worker."""
        last_ohlcv_update = {}  # Track last update time for each timeframe

        while self.is_collecting:
            try:
                # Get current tick
                tick = mt5.symbol_info_tick(self.symbol)
                if tick is not None:
                    # Store tick data
                    self.store_tick_data(tick)

                    # Add to real-time queue for processing
                    self.real_time_queue.put({
                        'timestamp': tick.time,
                        'bid': tick.bid,
                        'ask': tick.ask,
                        'last': tick.last,
                        'volume': tick.volume
                    })

                    # Update OHLCV bars from tick data periodically
                    current_time = datetime.now()
                    for timeframe in [1, 5, 15, 30, 60, 240, 1440]:  # All timeframes
                        last_update = last_ohlcv_update.get(timeframe, datetime.min)

                        # Update frequency based on timeframe
                        if timeframe == 1:  # 1-minute: update every 10 seconds
                            update_interval = 10
                        elif timeframe == 5:  # 5-minute: update every 30 seconds
                            update_interval = 30
                        elif timeframe <= 30:  # 15-30 minute: update every 60 seconds
                            update_interval = 60
                        else:  # Longer timeframes: update every 5 minutes
                            update_interval = 300

                        if (current_time - last_update).total_seconds() >= update_interval:
                            self._update_ohlcv_from_ticks(timeframe)
                            last_ohlcv_update[timeframe] = current_time

                # Sleep for a short interval (collect every 100ms)
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error in real-time collection: {e}")
                time.sleep(1)  # Wait before retrying
                
    def store_tick_data(self, tick):
        """Store individual tick data."""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO tick_data
                (timestamp, bid, ask, last, volume, time_msc, flags, volume_real)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                tick.time,
                tick.bid,
                tick.ask,
                tick.last,
                tick.volume,
                tick.time_msc,
                tick.flags,
                tick.volume_real
            ))
            self.conn.commit()

        except Exception as e:
            logger.error(f"Error storing tick data: {e}")

    def _update_ohlcv_from_ticks(self, timeframe_minutes: int):
        """Update OHLCV data by aggregating tick data for the specified timeframe."""
        try:
            cursor = self.conn.cursor()

            # Calculate timeframe boundaries
            now = datetime.now()

            # Round down to the nearest timeframe boundary
            if timeframe_minutes == 1:
                bar_start = now.replace(second=0, microsecond=0)
            elif timeframe_minutes == 5:
                bar_start = now.replace(minute=(now.minute // 5) * 5, second=0, microsecond=0)
            elif timeframe_minutes == 15:
                bar_start = now.replace(minute=(now.minute // 15) * 15, second=0, microsecond=0)
            elif timeframe_minutes == 30:
                bar_start = now.replace(minute=(now.minute // 30) * 30, second=0, microsecond=0)
            elif timeframe_minutes == 60:
                bar_start = now.replace(minute=0, second=0, microsecond=0)
            elif timeframe_minutes == 240:  # 4 hours
                bar_start = now.replace(hour=(now.hour // 4) * 4, minute=0, second=0, microsecond=0)
            elif timeframe_minutes == 1440:  # 1 day
                bar_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                return  # Unsupported timeframe

            bar_end = bar_start + timedelta(minutes=timeframe_minutes)

            # Don't update current incomplete bar for longer timeframes
            if timeframe_minutes > 5 and now < bar_end:
                bar_end = bar_start
                bar_start = bar_start - timedelta(minutes=timeframe_minutes)

            # Get tick data for this timeframe
            start_timestamp = bar_start.timestamp()
            end_timestamp = bar_end.timestamp()

            cursor.execute("""
                SELECT timestamp, (bid + ask) / 2 as price, volume
                FROM tick_data
                WHERE timestamp >= ? AND timestamp < ?
                ORDER BY timestamp
            """, (start_timestamp, end_timestamp))

            ticks = cursor.fetchall()

            if not ticks:
                return  # No tick data for this period

            # Calculate OHLCV from ticks
            prices = [tick[1] for tick in ticks]
            volumes = [tick[2] for tick in ticks]

            open_price = prices[0]
            high_price = max(prices)
            low_price = min(prices)
            close_price = prices[-1]
            total_volume = sum(volumes)

            # Insert or update OHLCV data
            cursor.execute("""
                INSERT OR REPLACE INTO ohlcv_data
                (timestamp, timeframe, open, high, low, close, volume, spread)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                start_timestamp,
                timeframe_minutes,
                open_price,
                high_price,
                low_price,
                close_price,
                total_volume,
                0  # spread
            ))

            self.conn.commit()
            logger.debug(f"Updated {timeframe_minutes}min OHLCV bar: O={open_price:.2f} H={high_price:.2f} L={low_price:.2f} C={close_price:.2f}")

        except Exception as e:
            logger.error(f"Error updating OHLCV from ticks for {timeframe_minutes}min: {e}")
            
    def stop_real_time_collection(self):
        """Stop real-time data collection."""
        self.is_collecting = False
        if hasattr(self, 'real_time_thread'):
            self.real_time_thread.join(timeout=5)
        logger.info("Real-time data collection stopped")
        
    def get_latest_data(self, timeframe: int, count: int = 100) -> pd.DataFrame:
        """Get latest data for a specific timeframe."""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT * FROM ohlcv_data 
                WHERE timeframe = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            """, (timeframe, count))
            
            rows = cursor.fetchall()
            if not rows:
                return pd.DataFrame()
                
            columns = ['id', 'timestamp', 'timeframe', 'open', 'high', 'low', 'close', 'volume', 'spread']
            df = pd.DataFrame(rows, columns=columns)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            
            return df.sort_values('timestamp')
            
        except Exception as e:
            logger.error(f"Error getting latest data: {e}")
            return pd.DataFrame()
            
    def __del__(self):
        """Cleanup when object is destroyed."""
        self.stop_real_time_collection()
        if hasattr(self, 'conn'):
            self.conn.close()
        if self.mt5_connected:
            mt5.shutdown()
        logger.info("SyntheticDataCollector cleanup completed")
