#!/usr/bin/env python3
"""
Test the complete dashboard startup sequence to identify timing issues.
"""

import sys
import time
import subprocess
import logging
import requests
import os

# Add current directory to path
sys.path.append('.')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dashboard_running():
    """Check if dashboard is currently running."""
    try:
        response = requests.get('http://localhost:5000', timeout=2)
        return response.status_code == 200
    except:
        return False

def kill_existing_dashboard():
    """Kill any existing dashboard processes."""
    try:
        # Kill any existing Python processes running dashboard_server.py
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                      capture_output=True, shell=True)
        time.sleep(2)
        logger.info("Killed existing Python processes")
    except:
        pass

def start_dashboard_server():
    """Start dashboard server (simulating SystemOrchestrator logic)."""
    try:
        logger.info("Starting dashboard server...")

        # Check if dashboard is already running
        try:
            response = requests.get('http://localhost:5000', timeout=2)
            if response.status_code == 200:
                logger.info("Dashboard server is already running")
                return True, None
        except:
            pass  # Dashboard not running, continue with startup

        # Start dashboard in background
        result = subprocess.Popen([
            sys.executable, 'dashboard_server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        logger.info(f"Dashboard process started with PID: {result.pid}")

        # Give it more time to start and check periodically
        for i in range(6):  # Check 6 times over 30 seconds
            time.sleep(5)
            
            if result.poll() is not None:
                # Process terminated
                stdout, stderr = result.communicate()
                logger.error("ERROR: Dashboard server process terminated")
                if stdout:
                    logger.error(f"STDOUT: {stdout.decode()}")
                if stderr:
                    logger.error(f"STDERR: {stderr.decode()}")
                return False, None
            
            logger.info(f"Dashboard startup check {i+1}/6 - process still running")

        # Final check - process should still be running
        if result.poll() is None:
            logger.info("SUCCESS: Dashboard server started and running")
            return True, result
        else:
            logger.error("ERROR: Dashboard server failed to start")
            return False, None

    except Exception as e:
        logger.error(f"Dashboard server startup failed: {e}")
        return False, None

def validate_dashboard_connection():
    """Validate dashboard connectivity (simulating SystemOrchestrator logic)."""
    try:
        logger.info("Validating dashboard connection...")

        # Try to connect to dashboard
        for attempt in range(1, 6):  # 1-5 attempts
            try:
                logger.info(f"Dashboard connection attempt {attempt}/5...")
                response = requests.get('http://localhost:5000', timeout=10)
                if response.status_code == 200:
                    logger.info(f"SUCCESS: Dashboard connection validated (status: {response.status_code})")
                    logger.info(f"Dashboard response length: {len(response.text)} characters")
                    return True
                else:
                    logger.warning(f"Dashboard responded with status: {response.status_code}")
            except requests.exceptions.ConnectionError as e:
                logger.warning(f"Connection error on attempt {attempt}: {e}")
            except requests.exceptions.Timeout as e:
                logger.warning(f"Timeout error on attempt {attempt}: {e}")
            except Exception as e:
                logger.warning(f"Unexpected error on attempt {attempt}: {e}")
            
            if attempt < 5:
                logger.info("Waiting 3 seconds before next attempt...")
                time.sleep(3)

        logger.error("ERROR: Dashboard connection failed after 5 attempts")
        return False

    except Exception as e:
        logger.error(f"Dashboard connection validation failed: {e}")
        return False

def simulate_phase6_dashboard_launch():
    """Simulate Phase 6: Dashboard Launch from SystemOrchestrator."""
    logger.info("Phase 6: Dashboard Launch (SIMULATION)")
    
    try:
        # Check if dashboard is already running
        logger.info("Checking if dashboard is already running...")
        if validate_dashboard_connection():
            logger.info("Dashboard is already running and accessible")
        else:
            # Start dashboard server
            logger.info("Dashboard not running, starting server...")
            success, process = start_dashboard_server()
            if not success:
                raise Exception("Dashboard server startup failed")

        # Give additional time for dashboard to fully initialize
        logger.info("Allowing additional time for dashboard initialization...")
        time.sleep(10)
            
        # Validate dashboard connectivity
        logger.info("Performing final dashboard connectivity validation...")
        if not validate_dashboard_connection():
            raise Exception("Dashboard connection validation failed")
            
        logger.info("SUCCESS: Dashboard launch completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Phase 6 failed: {e}")
        return False

def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("DASHBOARD STARTUP SEQUENCE TEST")
    logger.info("=" * 60)
    
    # Step 1: Check initial state
    logger.info("\n1. Checking initial dashboard state...")
    initially_running = check_dashboard_running()
    logger.info(f"Dashboard initially running: {initially_running}")
    
    # Step 2: Clean slate test (kill existing processes)
    if initially_running:
        logger.info("\n2. Testing with clean slate (killing existing processes)...")
        kill_existing_dashboard()
        time.sleep(3)
        
        after_kill = check_dashboard_running()
        logger.info(f"Dashboard running after kill: {after_kill}")
        
        # Step 3: Test startup sequence
        logger.info("\n3. Testing startup sequence from clean state...")
        startup_success = simulate_phase6_dashboard_launch()
    else:
        # Step 3: Test startup sequence
        logger.info("\n2. Testing startup sequence...")
        startup_success = simulate_phase6_dashboard_launch()
    
    # Step 4: Final verification
    logger.info("\n4. Final verification...")
    final_running = check_dashboard_running()
    logger.info(f"Dashboard running after test: {final_running}")
    
    # Results
    logger.info("\n" + "=" * 40)
    logger.info("TEST RESULTS")
    logger.info("=" * 40)
    
    logger.info(f"Startup sequence: {'✅ PASSED' if startup_success else '❌ FAILED'}")
    logger.info(f"Final dashboard state: {'✅ RUNNING' if final_running else '❌ NOT RUNNING'}")
    
    if startup_success and final_running:
        logger.info("\n🎉 Dashboard startup sequence test PASSED!")
        logger.info("The dashboard should start correctly during system orchestration.")
        return True
    else:
        logger.error("\n💥 Dashboard startup sequence test FAILED!")
        logger.error("There may be timing or startup issues.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
