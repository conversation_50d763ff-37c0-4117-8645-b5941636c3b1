#!/usr/bin/env python3
"""
Check the database structure and estimate trade frequency from available data.
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

def check_database_structure():
    """Check what tables and data we have in the database."""
    try:
        print("🔍 CHECKING DATABASE STRUCTURE")
        print("=" * 50)
        
        # Try different possible database paths
        db_paths = [
            "data/synthetic_cache/dex_900_down_data.db",
            "data/dex_900_down_data.db",
            "synthetic_data.db",
            "dex_900_down_data.db"
        ]
        
        conn = None
        for db_path in db_paths:
            try:
                conn = sqlite3.connect(db_path)
                print(f"✅ Connected to: {db_path}")
                break
            except Exception as e:
                print(f"❌ Could not connect to {db_path}: {e}")
        
        if not conn:
            print("❌ No database found!")
            return False
        
        # Get table names
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"\n📊 Available tables: {[table[0] for table in tables]}")
        
        # Check each table
        for table_name in [table[0] for table in tables]:
            print(f"\n🔍 Table: {table_name}")
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"   Columns: {[col[1] for col in columns]}")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   Rows: {count:,}")
            
            # Get sample data
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                sample = cursor.fetchall()
                print(f"   Sample data:")
                for i, row in enumerate(sample):
                    print(f"      Row {i+1}: {row}")
                
                # If this looks like price data, analyze it
                if any(col in [col[1] for col in columns] for col in ['close', 'price', 'high', 'low']):
                    analyze_price_data(cursor, table_name, columns)
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_price_data(cursor, table_name, columns):
    """Analyze price data to estimate trading opportunities."""
    try:
        print(f"\n   📈 ANALYZING PRICE DATA IN {table_name}:")
        
        # Get column names
        col_names = [col[1] for col in columns]
        
        # Try to find price and time columns
        price_col = None
        time_col = None
        
        for col in col_names:
            if col.lower() in ['close', 'price']:
                price_col = col
            elif col.lower() in ['timestamp', 'time', 'datetime']:
                time_col = col
        
        if not price_col:
            print("      ❌ No price column found")
            return
        
        # Get data range
        if time_col:
            cursor.execute(f"SELECT MIN({time_col}), MAX({time_col}), COUNT(*) FROM {table_name}")
            min_time, max_time, count = cursor.fetchone()
            
            try:
                start_date = datetime.fromtimestamp(min_time) if isinstance(min_time, (int, float)) else min_time
                end_date = datetime.fromtimestamp(max_time) if isinstance(max_time, (int, float)) else max_time
                print(f"      📅 Period: {start_date} to {end_date}")
                
                if isinstance(start_date, datetime) and isinstance(end_date, datetime):
                    duration = end_date - start_date
                    print(f"      ⏱️ Duration: {duration.days} days")
            except:
                print(f"      📅 Time range: {min_time} to {max_time}")
        
        # Get price statistics
        cursor.execute(f"SELECT MIN({price_col}), MAX({price_col}), AVG({price_col}) FROM {table_name}")
        min_price, max_price, avg_price = cursor.fetchone()
        print(f"      💰 Price range: {min_price:.2f} - {max_price:.2f} (avg: {avg_price:.2f})")
        
        # Calculate basic volatility if we have enough data
        cursor.execute(f"SELECT {price_col} FROM {table_name} ORDER BY {time_col if time_col else 'rowid'} LIMIT 1000")
        prices = [row[0] for row in cursor.fetchall()]
        
        if len(prices) > 10:
            returns = [abs((prices[i] - prices[i-1]) / prices[i-1]) for i in range(1, len(prices))]
            avg_return = np.mean(returns)
            max_return = max(returns)
            
            print(f"      📊 Avg move: {avg_return*100:.3f}%")
            print(f"      📊 Max move: {max_return*100:.3f}%")
            
            # Estimate trading opportunities
            significant_moves = sum(1 for r in returns if r > 0.001)  # 0.1% moves
            large_moves = sum(1 for r in returns if r > 0.002)  # 0.2% moves
            
            print(f"      🎯 Moves >0.1%: {significant_moves}/{len(returns)} ({significant_moves/len(returns)*100:.1f}%)")
            print(f"      🎯 Moves >0.2%: {large_moves}/{len(returns)} ({large_moves/len(returns)*100:.1f}%)")
            
            # Estimate daily opportunities
            if time_col and duration.days > 0:
                opportunities_per_day = significant_moves / duration.days * (len(returns) / count)
                print(f"      📈 Est. opportunities/day: {opportunities_per_day:.2f}")
                
                if opportunities_per_day < 0.5:
                    print(f"      ⚠️ LOW opportunity frequency - consider relaxing criteria")
                elif opportunities_per_day > 10:
                    print(f"      ⚠️ HIGH opportunity frequency - criteria may be too loose")
                else:
                    print(f"      ✅ Reasonable opportunity frequency")
    
    except Exception as e:
        print(f"      ❌ Error analyzing price data: {e}")

if __name__ == "__main__":
    print("🔍 DATABASE STRUCTURE AND TRADE FREQUENCY CHECK")
    print("=" * 60)
    
    success = check_database_structure()
    
    if success:
        print("\n✅ Database analysis completed!")
    else:
        print("\n❌ Database analysis failed!")
