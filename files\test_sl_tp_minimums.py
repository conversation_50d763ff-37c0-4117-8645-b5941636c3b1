#!/usr/bin/env python3
"""
Test script to check the minimum Stop Loss and Take Profit requirements for DEX 900 DOWN Index.
This is critical for order execution success.
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_sl_tp_minimums():
    """Test and discover the minimum SL/TP requirements for DEX 900 DOWN Index."""
    try:
        print("🧪 TESTING STOP LOSS & TAKE PROFIT MINIMUMS")
        print("=" * 60)
        
        # Connect to MT5
        print("🔌 Connecting to MT5...")
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ MT5 initialization failed!")
            return False
            
        # Get symbol information
        print("📊 Getting DEX 900 DOWN Index symbol information...")
        symbol_info = mt5.symbol_info("DEX 900 DOWN Index")
        
        if not symbol_info:
            print("❌ Could not get symbol information!")
            return False
            
        print(f"✅ Symbol Information:")
        print(f"   Symbol: {symbol_info.name}")
        print(f"   Point: {symbol_info.point}")
        print(f"   Digits: {symbol_info.digits}")
        print(f"   Spread: {symbol_info.spread}")
        print(f"   Trade Mode: {symbol_info.trade_mode}")
        print(f"   Min Volume: {symbol_info.volume_min}")
        print(f"   Max Volume: {symbol_info.volume_max}")
        print(f"   Volume Step: {symbol_info.volume_step}")
        
        # Critical SL/TP information
        print(f"\n🎯 CRITICAL SL/TP INFORMATION:")
        print(f"   Stops Level: {symbol_info.trade_stops_level} points")
        print(f"   Freeze Level: {symbol_info.trade_freeze_level} points")
        
        # Calculate minimum distances
        current_price = mt5.symbol_info_tick("DEX 900 DOWN Index").bid
        point_value = symbol_info.point
        stops_level = symbol_info.trade_stops_level
        
        min_sl_tp_distance = stops_level * point_value
        
        print(f"\n💰 CURRENT MARKET DATA:")
        print(f"   Current Price: {current_price:.2f}")
        print(f"   Point Value: {point_value}")
        print(f"   Minimum SL/TP Distance: {min_sl_tp_distance:.4f} ({stops_level} points)")
        
        # Calculate example SL/TP levels
        print(f"\n📏 EXAMPLE SL/TP LEVELS:")
        
        # For BUY order
        buy_sl = current_price - min_sl_tp_distance
        buy_tp = current_price + min_sl_tp_distance
        
        print(f"   BUY Order at {current_price:.2f}:")
        print(f"     Minimum Stop Loss: {buy_sl:.2f}")
        print(f"     Minimum Take Profit: {buy_tp:.2f}")
        
        # For SELL order
        sell_sl = current_price + min_sl_tp_distance
        sell_tp = current_price - min_sl_tp_distance
        
        print(f"   SELL Order at {current_price:.2f}:")
        print(f"     Minimum Stop Loss: {sell_sl:.2f}")
        print(f"     Minimum Take Profit: {sell_tp:.2f}")
        
        # Test with a small order to see what happens
        print(f"\n🧪 TESTING MINIMUM DISTANCE VALIDATION:")
        
        # Test order with insufficient SL distance
        test_request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": "DEX 900 DOWN Index",
            "volume": 0.01,
            "type": mt5.ORDER_TYPE_BUY,
            "sl": current_price - (min_sl_tp_distance * 0.5),  # Too close!
            "tp": current_price + min_sl_tp_distance,
            "deviation": 20,
            "magic": 123456,
            "comment": "SL_TP_Test",
        }
        
        print(f"   Testing SL too close: {test_request['sl']:.2f} (should fail)")
        result = mt5.order_check(test_request)
        
        if result:
            print(f"   Result: {result.retcode} - {result.comment}")
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                print(f"   ✅ CORRECTLY REJECTED: {result.comment}")
            else:
                print(f"   ⚠️  UNEXPECTEDLY ACCEPTED")
        else:
            print(f"   ❌ No result from order_check")
            
        # Test order with proper SL distance
        test_request["sl"] = current_price - min_sl_tp_distance
        print(f"   Testing proper SL: {test_request['sl']:.2f} (should pass)")
        result = mt5.order_check(test_request)
        
        if result:
            print(f"   Result: {result.retcode} - {result.comment}")
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                print(f"   ✅ CORRECTLY ACCEPTED")
            else:
                print(f"   ❌ UNEXPECTEDLY REJECTED: {result.comment}")
        else:
            print(f"   ❌ No result from order_check")
        
        # Summary
        print(f"\n📋 SUMMARY FOR DEX 900 DOWN INDEX:")
        print(f"   ✅ Minimum SL/TP Distance: {min_sl_tp_distance:.4f} ({stops_level} points)")
        print(f"   ✅ Point Value: {point_value}")
        print(f"   ✅ Current Spread: {symbol_info.spread} points")
        
        # Cleanup
        mt5.shutdown()
        
        return {
            "min_distance": min_sl_tp_distance,
            "stops_level": stops_level,
            "point_value": point_value,
            "spread": symbol_info.spread
        }
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 DEX 900 DOWN INDEX - SL/TP MINIMUM REQUIREMENTS TEST")
    print("=" * 60)
    print("⚠️  This test discovers the minimum Stop Loss and Take Profit distances")
    print("=" * 60)
    
    result = test_sl_tp_minimums()
    
    if result:
        print("\n🎉 SL/TP MINIMUM REQUIREMENTS DISCOVERED!")
        print("✅ Use these values to ensure order execution success!")
    else:
        print("\n❌ SL/TP MINIMUM REQUIREMENTS TEST FAILED!")
        print("🔧 Please check MT5 connection and symbol availability.")
        
    sys.exit(0 if result else 1)
