#!/usr/bin/env python3
"""
Debug script to check actual ensemble prediction values
and understand why trades aren't being generated.
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import required modules
from data_collection_system import DataCollectionSystem
from pattern_detection_system import PatternDetectionSystem
from ai_model_manager import AIModelManager
from trading_signal_generator import TradingSignalGenerator
import config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_ensemble_prediction():
    """Debug the ensemble prediction to see actual values."""
    print("🔍 DEBUGGING ENSEMBLE PREDICTION VALUES")
    print("=" * 60)
    
    try:
        # Initialize components
        print("📊 Initializing components...")
        data_collector = DataCollectionSystem()
        pattern_detector = PatternDetectionSystem()
        ai_manager = AIModelManager()
        
        # Load models
        print("🤖 Loading AI models...")
        ai_manager.load_models()
        loaded_models = len(ai_manager.models)
        print(f"   ✅ Loaded {loaded_models} models")
        
        if loaded_models == 0:
            print("❌ No models loaded! Cannot generate predictions.")
            return False
        
        # Get latest data
        print("📈 Getting latest market data...")
        market_data = data_collector.get_latest_data(5, 100)  # 5-minute, 100 periods
        
        if market_data is None or market_data.empty:
            print("❌ No market data available!")
            return False
        
        print(f"   ✅ Got {len(market_data)} data points")
        current_price = market_data['close'].iloc[-1]
        print(f"   💰 Current price: {current_price:.2f}")
        
        # Calculate indicators
        print("🔧 Calculating synthetic indicators...")
        df_indicators = pattern_detector.calculate_synthetic_indicators(market_data)
        
        # Extract features for AI
        print("🧠 Extracting features for AI models...")
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        model_features = signal_generator._extract_features_for_ai(df_indicators)
        
        if not model_features:
            print("❌ Could not extract features!")
            return False
        
        print(f"   ✅ Extracted features for {len(model_features)} models")
        
        # Get ensemble prediction
        print("\n🎯 GETTING ENSEMBLE PREDICTION...")
        print("-" * 40)
        
        ensemble_result = ai_manager.get_ensemble_prediction_with_features(model_features, target_timeframe=5)
        
        if not ensemble_result:
            print("❌ No ensemble prediction returned!")
            return False
        
        # Display detailed results
        print("📋 ENSEMBLE PREDICTION DETAILS:")
        print(f"   🎯 Ensemble Signal: {ensemble_result.get('ensemble_signal', 'MISSING')}")
        print(f"   🎯 Confidence: {ensemble_result.get('confidence', 'MISSING'):.3f}")
        print(f"   🎯 Consensus: {ensemble_result.get('consensus', 'MISSING')}")
        print(f"   🎯 Consensus Strength: {ensemble_result.get('consensus_strength', 'MISSING'):.3f}")
        print(f"   🎯 Contributing Models: {ensemble_result.get('contributing_models', 'MISSING')}")
        
        # Check strong signals
        strong_signals = ensemble_result.get('strong_signals', [])
        print(f"\n⚡ STRONG SIGNALS: {len(strong_signals)}")
        if strong_signals:
            for i, signal in enumerate(strong_signals, 1):
                print(f"   {i}. {signal['model_name']}: Signal {signal['signal']} (Conf: {signal['confidence']:.3f})")
        else:
            print("   ❌ No strong signals detected")
        
        # Check individual model predictions
        print(f"\n🤖 INDIVIDUAL MODEL PREDICTIONS:")
        model_predictions = ensemble_result.get('model_predictions', {})
        for model_name, pred in model_predictions.items():
            signal = pred.get('signal', 0)
            confidence = pred.get('confidence', 0)
            print(f"   {model_name}: Signal {signal} (Conf: {confidence:.3f})")
        
        # Check trading criteria
        print(f"\n✅ TRADING CRITERIA CHECK:")
        min_confidence = config.MIN_SIGNAL_CONFIDENCE
        min_consensus = 0.4  # From signal generator
        min_risk_reward = config.MIN_RISK_REWARD_RATIO
        
        ai_confidence = ensemble_result.get('confidence', 0)
        consensus_strength = ensemble_result.get('consensus_strength', 0)
        ensemble_signal = ensemble_result.get('ensemble_signal', 0)
        
        print(f"   📊 AI Confidence: {ai_confidence:.3f} (min: {min_confidence}) {'✅' if ai_confidence >= min_confidence else '❌'}")
        print(f"   📊 Consensus Strength: {consensus_strength:.3f} (min: {min_consensus}) {'✅' if consensus_strength >= min_consensus else '❌'}")
        print(f"   📊 Ensemble Signal: {ensemble_signal} {'✅' if ensemble_signal != 0 else '❌'}")
        
        # Try to generate actual trading signal
        print(f"\n🚀 ATTEMPTING TO GENERATE TRADING SIGNAL...")
        print("-" * 40)
        
        trading_signal = signal_generator.generate_signal(timeframe=5)
        
        if trading_signal:
            print("✅ TRADING SIGNAL GENERATED!")
            print(f"   Type: {trading_signal.signal_type.name}")
            print(f"   Confidence: {trading_signal.confidence:.3f}")
            print(f"   Entry: {trading_signal.entry_price:.2f}")
            print(f"   Stop Loss: {trading_signal.stop_loss:.2f}")
            print(f"   Take Profit: {trading_signal.take_profit:.2f}")
            print(f"   Risk/Reward: {trading_signal.risk_reward_ratio:.2f}")
            print(f"   Reasoning: {trading_signal.reasoning}")
        else:
            print("❌ NO TRADING SIGNAL GENERATED")
            print("   This explains why no trade is opening!")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in debug: {e}")
        return False

if __name__ == "__main__":
    print("🔍 AI Trading System - Ensemble Prediction Debug")
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = debug_ensemble_prediction()
    
    if success:
        print("\n✅ Debug completed successfully!")
    else:
        print("\n❌ Debug failed!")
    
    print("\n💡 This debug shows exactly why trades aren't opening.")
    print("   Check the ensemble signal value and trading criteria above.")
