#!/usr/bin/env python3
"""
Test script to check and fix volume calculation issues.
"""

import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mt5_symbol_info():
    """Test MT5 symbol information for DEX 900 DOWN Index."""
    try:
        import MetaTrader5 as mt5
        
        # Initialize MT5
        if not mt5.initialize():
            logger.error("MT5 initialization failed")
            return False
        
        symbol = "DEX 900 DOWN Index"
        
        # Get symbol info
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            logger.error(f"Could not get symbol info for {symbol}")
            return False
        
        logger.info("=== MT5 Symbol Information ===")
        logger.info(f"Symbol: {symbol}")
        logger.info(f"Minimum volume: {symbol_info.volume_min}")
        logger.info(f"Maximum volume: {symbol_info.volume_max}")
        logger.info(f"Volume step: {symbol_info.volume_step}")
        logger.info(f"Contract size: {symbol_info.trade_contract_size}")
        logger.info(f"Digits: {symbol_info.digits}")
        logger.info(f"Point: {symbol_info.point}")
        
        # Get current tick
        tick = mt5.symbol_info_tick(symbol)
        if tick:
            logger.info(f"Current bid: {tick.bid}")
            logger.info(f"Current ask: {tick.ask}")
            logger.info(f"Current spread: {tick.ask - tick.bid}")
        
        # Get account info
        account_info = mt5.account_info()
        if account_info:
            logger.info(f"Account balance: {account_info.balance}")
            logger.info(f"Account equity: {account_info.equity}")
            logger.info(f"Account margin: {account_info.margin}")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        logger.error(f"Error testing MT5 symbol info: {e}")
        return False

def test_volume_calculation():
    """Test volume calculation with different position sizes."""
    try:
        import MetaTrader5 as mt5
        
        # Initialize MT5
        if not mt5.initialize():
            logger.error("MT5 initialization failed")
            return False
        
        symbol = "DEX 900 DOWN Index"
        symbol_info = mt5.symbol_info(symbol)
        account_info = mt5.account_info()
        
        if not symbol_info or not account_info:
            logger.error("Could not get symbol or account info")
            return False
        
        logger.info("\n=== Volume Calculation Tests ===")
        
        # Test different position sizes
        test_position_sizes = [0.001, 0.005, 0.01, 0.02, 0.05]
        test_entry_price = 65000.0  # Example price
        
        for pos_size in test_position_sizes:
            logger.info(f"\nTesting position size: {pos_size} ({pos_size*100:.1f}%)")
            
            # Calculate volume using the new method
            balance = account_info.balance
            position_value = balance * pos_size
            
            # Use simplified calculation
            base_volume = 0.01
            if pos_size > 0.01:
                volume_multiplier = min(pos_size * 100, 10)
                calculated_volume = base_volume * volume_multiplier
            else:
                calculated_volume = base_volume
            
            # Round to symbol's volume step
            volume_step = symbol_info.volume_step
            if volume_step > 0:
                calculated_volume = round(calculated_volume / volume_step) * volume_step
            
            # Apply constraints
            min_volume = symbol_info.volume_min
            max_volume = symbol_info.volume_max
            final_volume = max(min_volume, min(calculated_volume, max_volume))
            
            logger.info(f"  Balance: {balance:.2f}")
            logger.info(f"  Position value: {position_value:.2f}")
            logger.info(f"  Calculated volume: {calculated_volume:.4f}")
            logger.info(f"  Final volume: {final_volume:.4f}")
            logger.info(f"  Valid: {min_volume <= final_volume <= max_volume}")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        logger.error(f"Error testing volume calculation: {e}")
        return False

def test_order_validation():
    """Test if we can validate an order before sending."""
    try:
        import MetaTrader5 as mt5
        
        # Initialize MT5
        if not mt5.initialize():
            logger.error("MT5 initialization failed")
            return False
        
        symbol = "DEX 900 DOWN Index"
        symbol_info = mt5.symbol_info(symbol)
        tick = mt5.symbol_info_tick(symbol)
        
        if not symbol_info or not tick:
            logger.error("Could not get symbol info or tick")
            return False
        
        logger.info("\n=== Order Validation Test ===")
        
        # Test order parameters
        test_volume = symbol_info.volume_min
        test_price = tick.ask
        
        # Create a test order request (but don't send it)
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": test_volume,
            "type": mt5.ORDER_TYPE_BUY,
            "price": test_price,
            "deviation": 20,
            "magic": 12345,
            "comment": "Volume test order",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }
        
        logger.info(f"Test order request:")
        for key, value in request.items():
            logger.info(f"  {key}: {value}")
        
        # Check order (but don't send)
        result = mt5.order_check(request)
        if result:
            logger.info(f"Order check result:")
            logger.info(f"  Return code: {result.retcode}")
            logger.info(f"  Comment: {result.comment}")
            logger.info(f"  Balance: {result.balance}")
            logger.info(f"  Equity: {result.equity}")
            logger.info(f"  Margin: {result.margin}")
            logger.info(f"  Margin free: {result.margin_free}")
            
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                logger.info("✅ Order validation PASSED")
            else:
                logger.warning(f"⚠️ Order validation failed: {result.retcode} - {result.comment}")
        else:
            logger.error("❌ Order check failed")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        logger.error(f"Error testing order validation: {e}")
        return False

def main():
    """Run all volume-related tests."""
    logger.info("🔧 VOLUME CALCULATION FIX TESTING")
    logger.info("=" * 50)
    
    tests = [
        ("MT5 Symbol Information", test_mt5_symbol_info),
        ("Volume Calculation", test_volume_calculation),
        ("Order Validation", test_order_validation),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"Result: {status}")
        except Exception as e:
            logger.error(f"Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("🎉 All tests passed! Volume calculation should work correctly.")
    else:
        logger.warning("⚠️ Some tests failed. Check the logs above for details.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
