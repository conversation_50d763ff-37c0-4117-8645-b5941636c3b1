#!/usr/bin/env python3
"""
Test script to verify the new SELL trade SL/TP settings.
Validates that SELL trades use the custom SL/TP values per user request.
"""

import sys
import os
import pandas as pd
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trading_signal_generator import TradingSignalGenerator, SignalType
from ai_model_manager import AIModelManager
from synthetic_data_collector import SyntheticDataCollector
from synthetic_pattern_detector import SyntheticPatternDetector

def test_sell_trade_sl_tp():
    """Test the new SELL trade SL/TP settings."""
    print("🧪 TESTING SELL TRADE SL/TP SETTINGS")
    print("=" * 60)
    
    try:
        # Initialize components
        print("📊 Initializing trading components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        
        # Test current price
        current_price = 36500.0  # Example price
        print(f"💰 Test Price: {current_price:.2f}")
        
        # Test regime analysis
        test_regime = {
            'regime_name': 'QUIET',
            'volatility': 0.01,
            'jump_probability': 0.3,
            'volatility_expansion': False,
            'jumpiness': 0.4
        }
        
        # Create dummy market data
        dummy_data = pd.DataFrame({'close': [current_price]})
        
        print("\n🎯 TESTING SELL TRADE SL/TP VALUES:")
        print("-" * 50)
        
        # Test all timeframe groups for SELL trades
        timeframe_groups = ['short_term', 'medium_term', 'long_term']
        sell_signal_types = [SignalType.WEAK_SELL, SignalType.STRONG_SELL]
        
        for timeframe_group in timeframe_groups:
            print(f"\n📈 {timeframe_group.upper()} TIMEFRAME:")
            
            for signal_type in sell_signal_types:
                try:
                    # Calculate SL/TP for SELL signal
                    stop_loss, take_profit = signal_generator._calculate_stop_loss_take_profit(
                        signal_type, current_price, test_regime, dummy_data, timeframe_group
                    )
                    
                    # Calculate distances in points
                    sl_distance = stop_loss - current_price  # For SELL: SL is above current price
                    tp_distance = current_price - take_profit  # For SELL: TP is below current price
                    
                    # Convert to dollars (1 point = $0.0001)
                    sl_dollars = sl_distance * 0.0001
                    tp_dollars = tp_distance * 0.0001
                    
                    print(f"   {signal_type.name}:")
                    print(f"     Current Price: {current_price:.2f}")
                    print(f"     Stop Loss: {stop_loss:.2f} (distance: {sl_distance:.0f} pts, ${sl_dollars:.2f})")
                    print(f"     Take Profit: {take_profit:.2f} (distance: {tp_distance:.0f} pts, ${tp_dollars:.2f})")
                    
                    # Verify expected values
                    expected_values = {
                        'short_term': {'sl': 100.0, 'tp': 50.0},   # Updated: TP changed to 50 points
                        'medium_term': {'sl': 150.0, 'tp': 100.0},
                        'long_term': {'sl': 300.0, 'tp': 100.0}
                    }
                    
                    expected = expected_values[timeframe_group]
                    
                    # Check if values are close to expected (allowing for volatility adjustments)
                    sl_ok = abs(sl_distance - expected['sl']) <= expected['sl'] * 0.5  # Allow 50% variance for adjustments
                    tp_ok = abs(tp_distance - expected['tp']) <= expected['tp'] * 0.5
                    
                    if sl_ok and tp_ok:
                        print(f"     ✅ SL/TP values are correct for {timeframe_group}")
                    else:
                        print(f"     ⚠️  SL/TP values may be adjusted by volatility/regime")
                        print(f"     Expected: SL={expected['sl']}pts, TP={expected['tp']}pts")
                    
                except Exception as e:
                    print(f"     ❌ Error testing {signal_type.name}: {e}")
        
        print("\n🔄 COMPARING BUY vs SELL TRADE SL/TP:")
        print("-" * 50)
        
        # Compare BUY vs SELL for medium term
        try:
            buy_sl, buy_tp = signal_generator._calculate_stop_loss_take_profit(
                SignalType.WEAK_BUY, current_price, test_regime, dummy_data, 'medium_term'
            )
            
            sell_sl, sell_tp = signal_generator._calculate_stop_loss_take_profit(
                SignalType.WEAK_SELL, current_price, test_regime, dummy_data, 'medium_term'
            )
            
            # Calculate distances
            buy_sl_dist = current_price - buy_sl
            buy_tp_dist = buy_tp - current_price
            sell_sl_dist = sell_sl - current_price
            sell_tp_dist = current_price - sell_tp
            
            print(f"📊 MEDIUM TERM COMPARISON:")
            print(f"   BUY Trade:  SL={buy_sl_dist:.0f}pts, TP={buy_tp_dist:.0f}pts")
            print(f"   SELL Trade: SL={sell_sl_dist:.0f}pts, TP={sell_tp_dist:.0f}pts")
            
            # Verify SELL trades use custom values
            if sell_sl_dist < buy_sl_dist and sell_tp_dist < buy_tp_dist:
                print("   ✅ SELL trades use tighter SL/TP as requested")
            else:
                print("   ⚠️  SELL trade SL/TP values may need verification")
                
        except Exception as e:
            print(f"   ❌ Error comparing BUY vs SELL: {e}")
        
        print("\n💡 EXPECTED SELL TRADE VALUES:")
        print("-" * 30)
        print("   Short Term:  SL=100pts ($1.00), TP=50pts ($0.50)")
        print("   Medium Term: SL=150pts ($1.50), TP=100pts ($1.00)")
        print("   Long Term:   SL=300pts ($3.00), TP=100pts ($1.00)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in test: {e}")
        return False

if __name__ == "__main__":
    success = test_sell_trade_sl_tp()
    if success:
        print("\n✅ SELL TRADE SL/TP TEST COMPLETED")
    else:
        print("\n❌ SELL TRADE SL/TP TEST FAILED")
        sys.exit(1)
