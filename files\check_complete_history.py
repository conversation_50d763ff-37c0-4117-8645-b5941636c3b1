#!/usr/bin/env python3
"""
Check complete trade history for today to verify all 27 trades.
"""

import MetaTrader5 as mt5
from datetime import datetime, <PERSON><PERSON><PERSON>

def check_complete_history():
    """Check all trades for today."""
    print("🔍 CHECKING COMPLETE TRADE HISTORY...")
    print("=" * 60)
    
    # Initialize MT5
    if not mt5.initialize():
        print('❌ MT5 initialization failed')
        return
    
    # Get today's date range
    today = datetime.now().date()
    start_time = datetime.combine(today, datetime.min.time())
    end_time = datetime.now()
    
    print(f"📅 Checking ALL trades from {start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%H:%M')}")
    
    # Get ALL deals from MT5 (both opening and closing)
    deals = mt5.history_deals_get(start_time, end_time, group="*")
    
    if deals is None:
        print("❌ No deals found")
        mt5.shutdown()
        return
    
    # Separate opening and closing trades
    opening_trades = []
    closing_trades = []
    
    for deal in deals:
        if (hasattr(deal, 'magic') and deal.magic == 54321 and
            hasattr(deal, 'symbol') and deal.symbol == 'DEX 900 DOWN Index'):
            
            comment = getattr(deal, 'comment', '')
            time_str = datetime.fromtimestamp(deal.time).strftime('%H:%M:%S')
            entry_type = getattr(deal, 'entry', -1)
            
            trade_info = {
                'ticket': deal.ticket,
                'time': time_str,
                'comment': comment,
                'type': 'BUY' if deal.type == 0 else 'SELL',
                'volume': deal.volume,
                'price': deal.price,
                'profit': getattr(deal, 'profit', 0)
            }
            
            if entry_type == 0:  # Opening trade
                opening_trades.append(trade_info)
            elif entry_type == 1:  # Closing trade
                closing_trades.append(trade_info)
    
    print(f"\n📊 COMPLETE TRADE SUMMARY:")
    print(f"   Opening Trades: {len(opening_trades)}")
    print(f"   Closing Trades: {len(closing_trades)}")
    print(f"   Total Deals: {len(opening_trades) + len(closing_trades)}")
    
    # Count by timeframe
    timeframe_counts = {'short_term': 0, 'medium_term': 0, 'long_term': 0, 'unknown': 0}
    
    print(f"\n📋 ALL OPENING TRADES TODAY:")
    print(f"{'Time':>8} | {'Type':>4} | {'Ticket':>10} | {'Timeframe':>10} | Comment")
    print("-" * 70)
    
    for i, trade in enumerate(opening_trades, 1):
        comment_upper = trade['comment'].upper()
        timeframe = 'unknown'
        
        if 'AI_BOT_SHORT' in comment_upper:
            timeframe = 'short_term'
            timeframe_counts['short_term'] += 1
        elif 'AI_BOT_MEDIUM' in comment_upper:
            timeframe = 'medium_term'
            timeframe_counts['medium_term'] += 1
        elif 'AI_BOT_LONG' in comment_upper:
            timeframe = 'long_term'
            timeframe_counts['long_term'] += 1
        else:
            timeframe_counts['unknown'] += 1
        
        print(f"{trade['time']:>8} | {trade['type']:>4} | {trade['ticket']:>10} | {timeframe:>10} | {trade['comment']}")
    
    print(f"\n📊 TIMEFRAME BREAKDOWN:")
    print(f"   Short Term: {timeframe_counts['short_term']}")
    print(f"   Medium Term: {timeframe_counts['medium_term']}")
    print(f"   Long Term: {timeframe_counts['long_term']}")
    print(f"   Unknown: {timeframe_counts['unknown']}")
    print(f"   Total: {sum(timeframe_counts.values())}")
    
    # Show some closing trades if any
    if closing_trades:
        print(f"\n📋 RECENT CLOSING TRADES:")
        print(f"{'Time':>8} | {'Type':>4} | {'Ticket':>10} | {'Profit':>8} | Comment")
        print("-" * 60)
        for trade in closing_trades[-5:]:  # Show last 5 closings
            print(f"{trade['time']:>8} | {trade['type']:>4} | {trade['ticket']:>10} | ${trade['profit']:>6.2f} | {trade['comment']}")
    
    mt5.shutdown()
    print(f"\n✅ Complete history check finished!")

if __name__ == "__main__":
    check_complete_history()
