{"timestamp": "2025-06-29T10:05:26.892359", "ensemble_signal": 0, "confidence": 0.7611009449613404, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.53907310962677, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7867552492284705, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5768088102340698, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.36743597785691284, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999605417251587, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999994158744812, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T10:23:36.645633", "ensemble_signal": 0, "confidence": 0.8121058864998217, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5036274194717407, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8907601842601841, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9588975310325623, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.26084822165428956, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999456405639648, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999933242797852, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6948809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T10:59:41.207366", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T11:03:22.129438", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T11:11:01.709530", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T11:17:49.891073", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T11:20:48.426279", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T11:23:49.463937", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T12:26:22.656972", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T13:11:54.058098", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T13:14:50.151975", "ensemble_signal": 0, "confidence": 0.7381196044436777, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.48003172874450684, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7871834967970596, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.49406230449676514, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.30195198894784137, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999674558639526, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999988079071045, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T13:25:51.870020", "ensemble_signal": 0, "confidence": 0.7381196044436777, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.48003172874450684, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7871834967970596, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.49406230449676514, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.30195198894784137, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999674558639526, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999988079071045, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T13:48:44.294091", "ensemble_signal": 0, "confidence": 0.7200068835904214, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.3661978840827942, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.5268920557014527, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.637872040271759, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.36915677260611696, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999516010284424, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999918937683105, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.58, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T13:56:13.503594", "ensemble_signal": 0, "confidence": 0.8579526770623319, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.4780167043209076, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9417071912892016, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998047947883606, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7320669175611408, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999983549118042, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.57, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:04:41.572172", "ensemble_signal": 0, "confidence": 0.7676351799549193, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.3095545768737793, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.5761231407849445, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998494386672974, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.46820549403955675, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999868869781494, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999973773956299, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:21:40.710051", "ensemble_signal": 0, "confidence": 0.7771202444958607, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.2460981011390686, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8360730436486786, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": -1, "confidence": 0.834588885307312, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5724922962891662, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999805688858032, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999686479568481, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5048809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:24:35.884616", "ensemble_signal": 0, "confidence": 0.7861442549594208, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.4129871428012848, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7080100015694749, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.6382809281349182, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.8009514183474667, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999977707862854, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999723434448242, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.5151190476190476, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:27:35.821291", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:30:35.774616", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:33:35.741391", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:36:36.044135", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:39:46.654910", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:45:59.845959", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:49:22.748897", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:50:37.687664", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:52:41.349726", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:55:28.312923", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:58:36.768752", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:01:50.087332", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:04:48.823225", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:07:38.996703", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:10:28.147950", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:14:59.600374", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:18:51.083222", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:19:40.366966", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:28:33.536779", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:30:59.840383", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:31:41.623429", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:34:43.299518", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:37:51.648217", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:42:51.940382", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:50:41.095995", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:51:55.768237", "ensemble_signal": 0, "confidence": 0.8202376964713541, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.3640727698802948, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.728941533289579, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9881775975227356, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.7810119165017284, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999481439590454, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999876022338867, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.52, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:53:59.161412", "ensemble_signal": 0, "confidence": 0.8202376964713541, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.3640727698802948, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.728941533289579, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9881775975227356, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.7810119165017284, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999481439590454, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999876022338867, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.52, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:13:24.811311", "ensemble_signal": 0, "confidence": 0.8202376964713541, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.3640727698802948, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.728941533289579, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9881775975227356, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.7810119165017284, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999481439590454, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999876022338867, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.52, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:16:11.662462", "ensemble_signal": 0, "confidence": 0.7572324806070693, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.2927377223968506, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8927561356103277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6600503325462341, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.3945708414017297, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999829530715942, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999946355819702, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:19:11.600218", "ensemble_signal": 0, "confidence": 0.7586906749507041, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.2652581036090851, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.5252576175742807, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.7828579545021057, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6798720195011776, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999732971191406, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999973773956299, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:45:16.144380", "ensemble_signal": 0, "confidence": 0.7586906749507041, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.2652581036090851, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.5252576175742807, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.7828579545021057, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6798720195011775, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999732971191406, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999973773956299, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
