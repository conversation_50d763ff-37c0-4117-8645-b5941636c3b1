{"timestamp": "2025-06-29T10:05:26.892359", "ensemble_signal": 0, "confidence": 0.7611009449613404, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.53907310962677, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7867552492284705, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5768088102340698, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.36743597785691284, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999605417251587, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999994158744812, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T10:23:36.645633", "ensemble_signal": 0, "confidence": 0.8121058864998217, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5036274194717407, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8907601842601841, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9588975310325623, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.26084822165428956, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999456405639648, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999933242797852, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6948809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T10:59:41.207366", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T11:03:22.129438", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T11:11:01.709530", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T11:17:49.891073", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T11:20:48.426279", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T11:23:49.463937", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T12:26:22.656972", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T13:11:54.058098", "ensemble_signal": 0, "confidence": 0.8088938584820042, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.263392835855484, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9937400793650794, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5921031832695007, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8509946086049633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999845027923584, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T13:14:50.151975", "ensemble_signal": 0, "confidence": 0.7381196044436777, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.48003172874450684, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7871834967970596, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.49406230449676514, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.30195198894784137, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999674558639526, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999988079071045, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T13:25:51.870020", "ensemble_signal": 0, "confidence": 0.7381196044436777, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.48003172874450684, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7871834967970596, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.49406230449676514, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.30195198894784137, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999674558639526, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999988079071045, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5798809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T13:48:44.294091", "ensemble_signal": 0, "confidence": 0.7200068835904214, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.3661978840827942, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.5268920557014527, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.637872040271759, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.36915677260611696, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999516010284424, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999918937683105, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.58, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T13:56:13.503594", "ensemble_signal": 0, "confidence": 0.8579526770623319, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.4780167043209076, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9417071912892016, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998047947883606, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7320669175611408, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999983549118042, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999995231628418, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.57, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:04:41.572172", "ensemble_signal": 0, "confidence": 0.7676351799549193, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.3095545768737793, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.5761231407849445, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998494386672974, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.46820549403955675, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999868869781494, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999973773956299, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:21:40.710051", "ensemble_signal": 0, "confidence": 0.7771202444958607, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.2460981011390686, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8360730436486786, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": -1, "confidence": 0.834588885307312, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5724922962891662, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999805688858032, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999686479568481, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5048809523809524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:24:35.884616", "ensemble_signal": 0, "confidence": 0.7861442549594208, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.4129871428012848, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7080100015694749, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.6382809281349182, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.8009514183474667, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999977707862854, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999723434448242, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.5151190476190476, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:27:35.821291", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:30:35.774616", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:33:35.741391", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:36:36.044135", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:39:46.654910", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:45:59.845959", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:49:22.748897", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:50:37.687664", "ensemble_signal": 0, "confidence": 0.8640161293621125, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9033753275871277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8928259698207236, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.653270959854126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7918268204551352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999936819076538, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.5348809523809525, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:52:41.349726", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:55:28.312923", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T14:58:36.768752", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:01:50.087332", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:04:48.823225", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:07:38.996703", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:10:28.147950", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:14:59.600374", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:18:51.083222", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:19:40.366966", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:28:33.536779", "ensemble_signal": 0, "confidence": 0.8988364426374638, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812575697898865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9006327761636491, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9778667092323303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8948092514597569, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999942779541016, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:30:59.840383", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:31:41.623429", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:34:43.299518", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:37:51.648217", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:42:51.940382", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:50:41.095995", "ensemble_signal": 0, "confidence": 0.8462588774873473, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7134295105934143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7120415764790765, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9865937232971191, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6493168805746851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999744892120361, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:51:55.768237", "ensemble_signal": 0, "confidence": 0.8202376964713541, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.3640727698802948, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.728941533289579, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9881775975227356, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.7810119165017284, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999481439590454, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999876022338867, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.52, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T15:53:59.161412", "ensemble_signal": 0, "confidence": 0.8202376964713541, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.3640727698802948, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.728941533289579, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9881775975227356, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.7810119165017284, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999481439590454, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999876022338867, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.52, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:13:24.811311", "ensemble_signal": 0, "confidence": 0.8202376964713541, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.3640727698802948, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.728941533289579, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9881775975227356, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.7810119165017284, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999481439590454, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999876022338867, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.52, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:16:11.662462", "ensemble_signal": 0, "confidence": 0.7572324806070693, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.2927377223968506, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8927561356103277, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6600503325462341, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.3945708414017297, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999829530715942, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999946355819702, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:19:11.600218", "ensemble_signal": 0, "confidence": 0.7586906749507041, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.2652581036090851, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.5252576175742807, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.7828579545021057, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6798720195011776, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999732971191406, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999973773956299, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:45:16.144380", "ensemble_signal": 0, "confidence": 0.7586906749507041, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.2652581036090851, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.5252576175742807, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.7828579545021057, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6798720195011775, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999732971191406, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999973773956299, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:53:54.760508", "ensemble_signal": 0, "confidence": 0.8024173968010468, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.531389594078064, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": -1, "confidence": 0.7413045167159311, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9814937114715576, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.43260075375997253, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999715089797974, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999967813491821, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:56:50.650660", "ensemble_signal": 0, "confidence": 0.7660586670271394, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.46791818737983704, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.4543846200900126, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998974800109863, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.3823449386276189, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999833106994629, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999997615814209, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T16:59:50.802715", "ensemble_signal": 1, "confidence": 0.7692861899710194, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.25776785612106323, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.39626912542903336, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9999393224716187, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.5646231850925835, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999768733978271, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999996423721313, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T17:08:04.970875", "ensemble_signal": 1, "confidence": 0.7692861899710194, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.25776785612106323, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 1, "confidence": 0.39626912542903336, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9999393224716187, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.5646231850925835, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999768733978271, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999996423721313, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T17:10:59.640779", "ensemble_signal": 0, "confidence": 0.8492500200146477, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6234438419342041, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8763256827493163, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9997586607933044, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.4387847554678117, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999943733215332, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999938011169434, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T17:13:59.626808", "ensemble_signal": 0, "confidence": 0.8712750792272196, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7762830853462219, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8987937494642303, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9999297857284546, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.46152458155221426, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999485015869141, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999963045120239, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T17:17:00.067927", "ensemble_signal": 0, "confidence": 0.8712750792272196, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7762830853462219, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8987937494642303, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9999297857284546, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.46152458155221426, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999485015869141, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999963045120239, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T17:59:03.379407", "ensemble_signal": 0, "confidence": 0.7309057345286959, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.4874117076396942, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 2, "confidence": 0.4266517041500974, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.5094422101974487, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.5646866958652638, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999711513519287, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999884366989136, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T18:01:58.528130", "ensemble_signal": 0, "confidence": 0.8070007228670646, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5897655487060547, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": -1, "confidence": 0.6209232637507612, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9875281453132629, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.47482465229113474, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999761581420898, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999890327453613, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T18:04:58.664012", "ensemble_signal": 0, "confidence": 0.8070007228670646, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5897655487060547, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": -1, "confidence": 0.6209232637507612, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9875281453132629, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.47482465229113463, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999761581420898, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999890327453613, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T18:07:58.547593", "ensemble_signal": 0, "confidence": 0.8070007228670646, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5897655487060547, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": -1, "confidence": 0.6209232637507612, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9875281453132629, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.47482465229113463, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999761581420898, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999890327453613, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T18:13:16.482302", "ensemble_signal": 0, "confidence": 0.8746846462399808, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6210857033729553, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8718366418175225, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9997472167015076, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8595368952686383, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999624490737915, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999932050704956, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.52, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T18:16:14.465014", "ensemble_signal": 0, "confidence": 0.8746846462399809, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6210857033729553, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8718366418175225, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9997472167015076, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8595368952686384, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999624490737915, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999932050704956, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.52, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T18:32:22.757114", "ensemble_signal": 0, "confidence": 0.8300107366069346, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8200814127922058, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": -1, "confidence": 0.5851342944464615, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.993528425693512, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5363687657201144, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999855756759644, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999984502792358, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.535, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T18:35:17.915769", "ensemble_signal": 0, "confidence": 0.8032890958292797, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -1, "confidence": 0.27050527930259705, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9341690537020446, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998205304145813, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5051192151183321, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999909400939941, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999971389770508, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.52, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T18:38:12.408610", "ensemble_signal": 0, "confidence": 0.8032890958292797, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -1, "confidence": 0.27050527930259705, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9341690537020446, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998205304145813, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5051192151183322, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999909400939941, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999971389770508, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 1, "confidence": 0.52, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T18:48:24.970227", "ensemble_signal": 0, "confidence": 0.8242594576311676, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -1, "confidence": 0.31821325421333313, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9186446177383674, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998416900634766, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5916729258994649, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999968409538269, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999945163726807, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T18:51:20.265595", "ensemble_signal": 0, "confidence": 0.8976161646941464, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.924522340297699, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9303534530321447, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9997380375862122, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6139703318675802, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999968409538269, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999932050704956, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.61, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T19:03:22.825358", "ensemble_signal": 0, "confidence": 0.8976161646941464, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.924522340297699, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9303534530321447, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9997380375862122, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6139703318675802, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999968409538269, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999932050704956, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.61, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T19:06:17.515423", "ensemble_signal": 0, "confidence": 0.7173633761832783, "consensus": "weak", "consensus_strength": 0.4444444444444444, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.35276785492897034, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": -2, "confidence": 0.45278681767959517, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.41573387384414673, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6350153937336607, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999709129333496, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999958276748657, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T19:14:20.861951", "ensemble_signal": 0, "confidence": 0.7101335849302302, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 0, "confidence": 0.3050873577594757, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": -2, "confidence": 0.4560693822512465, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 1, "confidence": 0.44262388348579407, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5874517383430262, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999717473983765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999984502792358, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T19:17:15.625239", "ensemble_signal": 0, "confidence": 0.9135350552735346, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9227360486984253, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.930066260358436, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9990054965019226, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7800265836972801, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999984622001648, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999967813491821, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T19:20:15.855154", "ensemble_signal": 0, "confidence": 0.9135350552735346, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9227360486984253, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.930066260358436, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9990054965019226, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7800265836972801, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999984622001648, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999967813491821, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T19:23:16.200202", "ensemble_signal": 0, "confidence": 0.8611456882823865, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5349273681640625, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9347282023633885, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998112320899963, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6908580385095433, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999990701675415, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999959468841553, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T19:26:15.946478", "ensemble_signal": 0, "confidence": 0.8611456882823864, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5349273681640625, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9347282023633884, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998112320899963, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6908580385095432, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999990701675415, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999959468841553, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-06-29T19:29:15.683769", "ensemble_signal": 0, "confidence": 0.8611456882823864, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5349273681640625, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9347282023633884, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.9998112320899963, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6908580385095432, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999990701675415, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999959468841553, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999998524274585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
