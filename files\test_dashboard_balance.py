#!/usr/bin/env python3
"""
Test script to verify that the dashboard correctly retrieves account balance from MT5.
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dashboard_balance():
    """Test that the dashboard gets the correct account balance."""
    try:
        print("🧪 TESTING DASHBOARD BALANCE RETRIEVAL")
        print("=" * 60)
        
        # Test direct MT5 account info
        print("🔌 Testing direct MT5 account info...")
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ MT5 initialization failed!")
            return False
            
        account_info = mt5.account_info()
        if not account_info:
            print("❌ Could not get MT5 account info!")
            return False
            
        print(f"✅ Direct MT5 Account Info:")
        print(f"   Balance: ${account_info.balance:.2f}")
        print(f"   Equity: ${account_info.equity:.2f}")
        print(f"   Free Margin: ${account_info.margin_free:.2f}")
        print(f"   Margin: ${account_info.margin:.2f}")
        
        # Test dashboard data manager
        print("\n📊 Testing Dashboard Data Manager...")
        from dashboard_server import DashboardDataManager
        
        dashboard_manager = DashboardDataManager()
        
        # Initialize trading system
        print("🔄 Initializing trading system...")
        if not dashboard_manager.initialize_trading_system():
            print("❌ Trading system initialization failed!")
            return False
            
        print("✅ Trading system initialized")
        
        # Update trade metrics (this should get real balance)
        print("💰 Updating trade metrics...")
        dashboard_manager._update_trade_metrics()
        
        # Get dashboard data
        dashboard_data = dashboard_manager.get_dashboard_data()
        trade_metrics = dashboard_data.get("trade_metrics", {})
        
        print(f"✅ Dashboard Trade Metrics:")
        print(f"   Account Balance: ${trade_metrics.get('account_balance', 0):.2f}")
        print(f"   Equity: ${trade_metrics.get('equity', 0):.2f}")
        print(f"   Free Margin: ${trade_metrics.get('free_margin', 0):.2f}")
        print(f"   Daily P&L: ${trade_metrics.get('daily_pnl', 0):.2f}")
        print(f"   Active Positions: {trade_metrics.get('active_positions', 0)}")
        
        # Compare values
        print("\n🔍 COMPARISON:")
        mt5_balance = float(account_info.balance)
        dashboard_balance = float(trade_metrics.get('account_balance', 0))
        
        if abs(mt5_balance - dashboard_balance) < 0.01:  # Allow for small floating point differences
            print(f"✅ BALANCE MATCH: MT5=${mt5_balance:.2f} == Dashboard=${dashboard_balance:.2f}")
            success = True
        else:
            print(f"❌ BALANCE MISMATCH: MT5=${mt5_balance:.2f} != Dashboard=${dashboard_balance:.2f}")
            success = False
            
        # Cleanup
        mt5.shutdown()
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 DASHBOARD BALANCE TEST")
    print("=" * 60)
    print("⚠️  This test verifies that the dashboard shows correct account balance")
    print("=" * 60)
    
    success = test_dashboard_balance()
    
    if success:
        print("\n🎉 DASHBOARD BALANCE TEST PASSED!")
        print("✅ Dashboard correctly retrieves account balance from MT5!")
    else:
        print("\n❌ DASHBOARD BALANCE TEST FAILED!")
        print("🔧 Dashboard may still show incorrect balance.")
        
    sys.exit(0 if success else 1)
