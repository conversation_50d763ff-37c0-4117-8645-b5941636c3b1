#!/usr/bin/env python3
"""
Diagnose trade count mismatch between dashboard and MT5 history.
"""

import MetaTrader5 as mt5
import json
import os
from datetime import datetime, <PERSON><PERSON><PERSON>

def diagnose_trade_count_mismatch():
    """Diagnose the mismatch between dashboard counters and actual MT5 trades."""
    print("🔍 DIAGNOSING TRADE COUNT MISMATCH...")
    print("=" * 60)
    
    # Initialize MT5
    if not mt5.initialize():
        print('❌ MT5 initialization failed')
        return
    
    # 1. Check the counter file
    print("\n📊 1. CHECKING COUNTER FILE:")
    counter_file = "data/timeframe_counters.json"
    if os.path.exists(counter_file):
        with open(counter_file, 'r') as f:
            counter_data = json.load(f)
        
        print(f"   📁 Counter file contents:")
        print(f"      Daily Short: {counter_data.get('daily', {}).get('short_term', 0)}")
        print(f"      Daily Medium: {counter_data.get('daily', {}).get('medium_term', 0)}")
        print(f"      Daily Long: {counter_data.get('daily', {}).get('long_term', 0)}")
        print(f"      Last Update: {counter_data.get('last_update', 'N/A')}")
        print(f"      Last Daily Reset: {counter_data.get('last_daily_reset', 'N/A')}")
    else:
        print("   ❌ Counter file not found!")
    
    # 2. Check actual MT5 trades TODAY
    print("\n📊 2. CHECKING ACTUAL MT5 TRADES TODAY:")
    today = datetime.now().date()
    start_time = datetime.combine(today, datetime.min.time())
    end_time = datetime.now()
    
    print(f"   📅 Checking trades from {start_time} to {end_time}")
    
    # Get deals (closed trades) from MT5
    deals = mt5.history_deals_get(start_time, end_time, group="*")
    
    if deals is None:
        print("   ❌ No deals found in MT5")
        mt5.shutdown()
        return
    
    print(f"   📊 Total deals found: {len(deals)}")
    
    # 3. Filter AI bot trades by magic number
    print("\n📊 3. FILTERING AI BOT TRADES:")

    # Check different magic numbers
    magic_numbers = [12345, 54321]  # Both old and new magic numbers

    for magic in magic_numbers:
        print(f"\n   🔍 Checking Magic Number: {magic}")

        opening_trades = []
        closing_trades = []
        opening_timeframe_counts = {'short_term': 0, 'medium_term': 0, 'long_term': 0, 'unknown': 0}
        closing_timeframe_counts = {'short_term': 0, 'medium_term': 0, 'long_term': 0, 'unknown': 0}

        for deal in deals:
            if (hasattr(deal, 'magic') and deal.magic == magic and
                hasattr(deal, 'symbol') and deal.symbol == 'DEX 900 DOWN Index'):

                comment = getattr(deal, 'comment', '')
                time_str = datetime.fromtimestamp(deal.time).strftime('%H:%M:%S')
                entry_type = "OPEN" if deal.entry == 0 else "CLOSE"

                # Determine timeframe from comment
                timeframe = 'unknown'
                comment_upper = comment.upper()
                if 'AI_BOT_SHORT' in comment_upper:
                    timeframe = 'short_term'
                elif 'AI_BOT_MEDIUM' in comment_upper:
                    timeframe = 'medium_term'
                elif 'AI_BOT_LONG' in comment_upper:
                    timeframe = 'long_term'

                trade_data = {
                    'time': time_str,
                    'profit': deal.profit,
                    'comment': comment,
                    'timeframe': timeframe,
                    'entry_type': entry_type
                }

                if deal.entry == 0:  # Opening trade
                    opening_trades.append(trade_data)
                    opening_timeframe_counts[timeframe] += 1
                else:  # Closing trade
                    closing_trades.append(trade_data)
                    closing_timeframe_counts[timeframe] += 1

        print(f"      📈 OPENING TRADES: {len(opening_trades)}")
        print(f"         Short term: {opening_timeframe_counts['short_term']}")
        print(f"         Medium term: {opening_timeframe_counts['medium_term']}")
        print(f"         Long term: {opening_timeframe_counts['long_term']}")
        print(f"         Unknown: {opening_timeframe_counts['unknown']}")

        print(f"      📉 CLOSING TRADES: {len(closing_trades)}")
        print(f"         Short term: {closing_timeframe_counts['short_term']}")
        print(f"         Medium term: {closing_timeframe_counts['medium_term']}")
        print(f"         Long term: {closing_timeframe_counts['long_term']}")
        print(f"         Unknown: {closing_timeframe_counts['unknown']}")

        if opening_trades:
            print(f"\n      📝 OPENING Trade Details:")
            for trade in opening_trades[-10:]:  # Show last 10 only
                print(f"         {trade['time']} | {trade['timeframe']:>10} | {trade['entry_type']} | {trade['comment']}")

        if closing_trades:
            print(f"\n      📝 CLOSING Trade Details:")
            for trade in closing_trades[-10:]:  # Show last 10 only
                print(f"         {trade['time']} | {trade['timeframe']:>10} | {trade['entry_type']} | ${trade['profit']:>6.2f} | {trade['comment']}")
    
    # 4. Check for open positions
    print("\n📊 4. CHECKING OPEN POSITIONS:")
    positions = mt5.positions_get(symbol='DEX 900 DOWN Index')
    
    if positions:
        print(f"   📊 Open positions: {len(positions)}")
        for pos in positions:
            comment = getattr(pos, 'comment', '')
            magic = getattr(pos, 'magic', 0)
            print(f"      Magic: {magic} | Comment: {comment} | Profit: ${pos.profit:.2f}")
    else:
        print("   ✅ No open positions")
    
    # 5. Check for potential double counting
    print("\n📊 5. POTENTIAL ISSUES:")
    
    # Check if counter file was updated today
    if os.path.exists(counter_file):
        file_mod_time = datetime.fromtimestamp(os.path.getmtime(counter_file))
        if file_mod_time.date() == today:
            print(f"   ✅ Counter file was updated today at {file_mod_time.strftime('%H:%M:%S')}")
        else:
            print(f"   ⚠️  Counter file last modified: {file_mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check if there are multiple instances running
    print(f"   💡 Possible causes of mismatch:")
    print(f"      - Multiple system instances incrementing counters")
    print(f"      - Failed trades being counted as successful")
    print(f"      - Counter file not being reset properly")
    print(f"      - Different magic numbers being used")
    
    mt5.shutdown()
    print("\n✅ Diagnosis complete!")

if __name__ == "__main__":
    diagnose_trade_count_mismatch()
