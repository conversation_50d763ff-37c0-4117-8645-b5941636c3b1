#!/usr/bin/env python3
"""
Model Decision Logger
Comprehensive logging system to track every AI model decision.
"""

import os
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

class ModelDecisionLogger:
    """Logs and tracks every AI model decision for analysis."""
    
    def __init__(self):
        self.log_dir = "logs/model_decisions"
        self.setup_logging_directory()
        
        # In-memory storage for current session
        self.current_session = {
            "session_start": datetime.now(),
            "decisions": [],
            "ensemble_results": [],
            "trade_triggers": []
        }
        
    def setup_logging_directory(self):
        """Create logging directory structure."""
        os.makedirs(self.log_dir, exist_ok=True)
        os.makedirs(f"{self.log_dir}/daily", exist_ok=True)
        os.makedirs(f"{self.log_dir}/sessions", exist_ok=True)
        
    def log_model_decision(self, model_name: str, decision_data: Dict):
        """Log individual model decision."""
        timestamp = datetime.now()
        
        decision_entry = {
            "timestamp": timestamp.isoformat(),
            "model_name": model_name,
            "signal": decision_data.get("signal", 0),
            "confidence": decision_data.get("confidence", 0.0),
            "timeframe_category": decision_data.get("timeframe_category", "UNKNOWN"),
            "weight": decision_data.get("weight", 1.0),
            "features_used": decision_data.get("features_count", 0),
            "model_purpose": decision_data.get("purpose", "Unknown")
        }
        
        # Add to current session
        self.current_session["decisions"].append(decision_entry)
        
        # Real-time console output
        signal_text = self._signal_to_text(decision_entry["signal"])
        print(f"🧠 {model_name}: {signal_text} (conf: {decision_entry['confidence']:.3f}, weight: {decision_entry['weight']:.2f})")
        
        # Save to daily log
        self._save_to_daily_log(decision_entry)
        
    def log_ensemble_result(self, ensemble_data: Dict):
        """Log ensemble voting result."""
        timestamp = datetime.now()
        
        ensemble_entry = {
            "timestamp": timestamp.isoformat(),
            "ensemble_signal": ensemble_data.get("ensemble_signal", 0),
            "confidence": ensemble_data.get("confidence", 0.0),
            "consensus": ensemble_data.get("consensus", "unknown"),
            "consensus_strength": ensemble_data.get("consensus_strength", 0.0),
            "total_models": ensemble_data.get("total_models", 0),
            "relevant_models": ensemble_data.get("relevant_models", 0),
            "target_timeframe": ensemble_data.get("target_timeframe", 5),
            "predictions": ensemble_data.get("predictions", {})
        }
        
        # Add to current session
        self.current_session["ensemble_results"].append(ensemble_entry)
        
        # Real-time console output
        signal_text = self._signal_to_text(ensemble_entry["ensemble_signal"])
        print(f"🎯 ENSEMBLE: {signal_text} | Consensus: {ensemble_entry['consensus']} ({ensemble_entry['consensus_strength']:.1%}) | Models: {ensemble_entry['relevant_models']}/{ensemble_entry['total_models']}")
        
        # Show individual model contributions
        predictions = ensemble_entry["predictions"]
        if predictions:
            print("   📋 Model Votes:")
            for model_name, pred_data in predictions.items():
                signal = pred_data.get("prediction", 0)
                confidence = pred_data.get("confidence", 0)
                weight = pred_data.get("weight", 1.0)
                category = pred_data.get("timeframe_category", "")
                signal_text = self._signal_to_text(signal)
                print(f"      • {model_name} ({category}): {signal_text} (conf: {confidence:.3f}, weight: {weight:.2f})")
        
        # Save to daily log
        self._save_to_daily_log(ensemble_entry, log_type="ensemble")
        
    def log_trade_trigger(self, trigger_data: Dict):
        """Log when a trade is triggered."""
        timestamp = datetime.now()
        
        trigger_entry = {
            "timestamp": timestamp.isoformat(),
            "trigger_type": trigger_data.get("trigger_type", "unknown"),
            "triggering_model": trigger_data.get("triggering_model", "ensemble"),
            "signal": trigger_data.get("signal", 0),
            "confidence": trigger_data.get("confidence", 0.0),
            "entry_price": trigger_data.get("entry_price", 0.0),
            "stop_loss": trigger_data.get("stop_loss", 0.0),
            "take_profit": trigger_data.get("take_profit", 0.0),
            "position_size": trigger_data.get("position_size", 0.0),
            "reason": trigger_data.get("reason", "")
        }
        
        # Add to current session
        self.current_session["trade_triggers"].append(trigger_entry)
        
        # Real-time console output
        signal_text = self._signal_to_text(trigger_entry["signal"])
        print(f"🚀 TRADE TRIGGERED: {signal_text} | {trigger_entry['trigger_type']} | Model: {trigger_entry['triggering_model']}")
        print(f"   💰 Entry: {trigger_entry['entry_price']:.2f} | SL: {trigger_entry['stop_loss']:.2f} | TP: {trigger_entry['take_profit']:.2f}")
        print(f"   📊 Size: {trigger_entry['position_size']:.4f} | Reason: {trigger_entry['reason']}")
        
        # Save to daily log
        self._save_to_daily_log(trigger_entry, log_type="trade_trigger")
        
    def get_session_summary(self) -> Dict:
        """Get summary of current session decisions."""
        decisions = self.current_session["decisions"]
        ensemble_results = self.current_session["ensemble_results"]
        trade_triggers = self.current_session["trade_triggers"]
        
        if not decisions:
            return {"message": "No decisions logged in current session"}
            
        # Analyze decision patterns
        model_stats = {}
        for decision in decisions:
            model_name = decision["model_name"]
            if model_name not in model_stats:
                model_stats[model_name] = {
                    "total_decisions": 0,
                    "signal_distribution": {},
                    "avg_confidence": 0.0,
                    "timeframe_category": decision["timeframe_category"]
                }
            
            stats = model_stats[model_name]
            stats["total_decisions"] += 1
            
            signal = decision["signal"]
            if signal not in stats["signal_distribution"]:
                stats["signal_distribution"][signal] = 0
            stats["signal_distribution"][signal] += 1
            
            # Update average confidence
            stats["avg_confidence"] = (stats["avg_confidence"] * (stats["total_decisions"] - 1) + decision["confidence"]) / stats["total_decisions"]
        
        summary = {
            "session_duration": str(datetime.now() - self.current_session["session_start"]),
            "total_decisions": len(decisions),
            "total_ensemble_results": len(ensemble_results),
            "total_trade_triggers": len(trade_triggers),
            "model_statistics": model_stats,
            "recent_decisions": decisions[-10:] if len(decisions) > 10 else decisions
        }
        
        return summary
        
    def _signal_to_text(self, signal: int) -> str:
        """Convert signal to readable text."""
        signal_map = {
            -2: "STRONG_SELL",
            -1: "WEAK_SELL", 
            0: "HOLD",
            1: "WEAK_BUY",
            2: "STRONG_BUY"
        }
        return signal_map.get(signal, f"SIGNAL_{signal}")
        
    def _save_to_daily_log(self, entry: Dict, log_type: str = "decision"):
        """Save entry to daily log file."""
        try:
            today = datetime.now().strftime("%Y-%m-%d")
            log_file = f"{self.log_dir}/daily/{today}_{log_type}.jsonl"
            
            with open(log_file, 'a') as f:
                f.write(json.dumps(entry) + '\n')
                
        except Exception as e:
            logger.error(f"Error saving to daily log: {e}")
            
    def save_session(self):
        """Save current session to file."""
        try:
            session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_file = f"{self.log_dir}/sessions/session_{session_id}.json"
            
            with open(session_file, 'w') as f:
                # Convert datetime objects to strings for JSON serialization
                session_data = self.current_session.copy()
                session_data["session_start"] = session_data["session_start"].isoformat()
                json.dump(session_data, f, indent=2)
                
            logger.info(f"Session saved to {session_file}")
            
        except Exception as e:
            logger.error(f"Error saving session: {e}")
            
    def load_recent_decisions(self, hours: int = 24) -> List[Dict]:
        """Load recent decisions from daily logs."""
        try:
            recent_decisions = []
            
            # Get today's and yesterday's logs
            dates = [
                datetime.now().strftime("%Y-%m-%d"),
                (datetime.now() - pd.Timedelta(days=1)).strftime("%Y-%m-%d")
            ]
            
            for date in dates:
                log_file = f"{self.log_dir}/daily/{date}_decision.jsonl"
                if os.path.exists(log_file):
                    with open(log_file, 'r') as f:
                        for line in f:
                            try:
                                decision = json.loads(line.strip())
                                decision_time = pd.to_datetime(decision["timestamp"])
                                
                                # Only include decisions from last N hours
                                if (datetime.now() - decision_time.to_pydatetime()).total_seconds() < hours * 3600:
                                    recent_decisions.append(decision)
                                    
                            except Exception as e:
                                logger.warning(f"Error parsing decision line: {e}")
                                
            return sorted(recent_decisions, key=lambda x: x["timestamp"])
            
        except Exception as e:
            logger.error(f"Error loading recent decisions: {e}")
            return []

# Global instance
decision_logger = ModelDecisionLogger()
