#!/usr/bin/env python3
"""
Analyze MT5 trade history to get win rates by timeframe.
"""

import sys
import os
import MetaTrader5 as mt5
from datetime import datetime, timedelta
import pandas as pd

def connect_to_mt5():
    """Connect to MT5."""
    print("🔌 Connecting to MT5...")
    
    if not mt5.initialize():
        print("❌ MT5 initialization failed")
        return False
    
    account_info = mt5.account_info()
    if account_info is None:
        print("❌ Could not get account information")
        return False
    
    print(f"✅ Connected to MT5 Account: {account_info.login}")
    return True

def get_ai_bot_trades():
    """Get all AI bot trades from MT5 history."""
    print("\n📊 Retrieving AI bot trade history...")
    
    try:
        # Get trades from the last 30 days
        from_date = datetime.now() - timedelta(days=30)
        to_date = datetime.now()
        
        # Get all deals (completed trades)
        deals = mt5.history_deals_get(from_date, to_date)
        
        if deals is None or len(deals) == 0:
            print("❌ No trade history found")
            return []
        
        print(f"📈 Found {len(deals)} total deals in last 30 days")
        
        # Filter for AI bot trades only
        ai_bot_trades = []
        
        for deal in deals:
            comment = deal.comment if hasattr(deal, 'comment') else ""
            
            # Check if this is an AI bot trade
            if any(pattern in comment for pattern in ["AI_BOT_SHORT_", "AI_BOT_MEDIUM_", "AI_BOT_LONG_"]):
                ai_bot_trades.append(deal)
        
        print(f"🤖 Found {len(ai_bot_trades)} AI bot trades")
        return ai_bot_trades
        
    except Exception as e:
        print(f"❌ Error retrieving trades: {e}")
        return []

def analyze_trades_by_timeframe(trades):
    """Analyze trades by timeframe."""
    print("\n🔍 ANALYZING TRADES BY TIMEFRAME...")
    print("=" * 60)
    
    timeframe_stats = {
        "short_term": {"wins": 0, "losses": 0, "total_profit": 0, "trades": []},
        "medium_term": {"wins": 0, "losses": 0, "total_profit": 0, "trades": []},
        "long_term": {"wins": 0, "losses": 0, "total_profit": 0, "trades": []}
    }
    
    for deal in trades:
        comment = deal.comment if hasattr(deal, 'comment') else ""
        profit = deal.profit if hasattr(deal, 'profit') else 0
        
        # Determine timeframe
        timeframe = None
        if "AI_BOT_SHORT_" in comment:
            timeframe = "short_term"
        elif "AI_BOT_MEDIUM_" in comment:
            timeframe = "medium_term"
        elif "AI_BOT_LONG_" in comment:
            timeframe = "long_term"
        
        if timeframe:
            stats = timeframe_stats[timeframe]
            stats["trades"].append(deal)
            stats["total_profit"] += profit
            
            if profit > 0:
                stats["wins"] += 1
            else:
                stats["losses"] += 1
    
    # Display results
    for timeframe, stats in timeframe_stats.items():
        total_trades = stats["wins"] + stats["losses"]
        win_rate = (stats["wins"] / total_trades * 100) if total_trades > 0 else 0
        
        print(f"\n🎯 {timeframe.upper().replace('_', ' ')} TIMEFRAME:")
        print("-" * 40)
        print(f"   📊 Total Trades: {total_trades}")
        print(f"   ✅ Wins: {stats['wins']}")
        print(f"   ❌ Losses: {stats['losses']}")
        print(f"   📈 Win Rate: {win_rate:.1f}%")
        print(f"   💰 Total P&L: ${stats['total_profit']:.2f}")
        
        if total_trades > 0:
            avg_profit_per_trade = stats['total_profit'] / total_trades
            print(f"   📊 Avg P&L per trade: ${avg_profit_per_trade:.2f}")
        
        # Show recent trades
        if stats["trades"]:
            print(f"\n   📋 Recent Trades:")
            recent_trades = sorted(stats["trades"], key=lambda x: x.time, reverse=True)[:5]
            for i, trade in enumerate(recent_trades):
                trade_time = datetime.fromtimestamp(trade.time)
                profit_str = f"${trade.profit:.2f}"
                result = "✅ WIN" if trade.profit > 0 else "❌ LOSS"
                print(f"      {i+1}. {trade_time.strftime('%Y-%m-%d %H:%M')} | {profit_str} | {result}")

def get_detailed_trade_analysis():
    """Get detailed analysis of recent trades."""
    print(f"\n📈 DETAILED TRADE ANALYSIS:")
    print("=" * 50)
    
    try:
        # Get more recent history for detailed analysis
        from_date = datetime.now() - timedelta(days=7)  # Last 7 days
        to_date = datetime.now()
        
        deals = mt5.history_deals_get(from_date, to_date)
        
        if deals is None or len(deals) == 0:
            print("❌ No recent trades found")
            return
        
        # Filter AI bot trades
        ai_trades = [deal for deal in deals if any(pattern in (deal.comment or "") 
                    for pattern in ["AI_BOT_SHORT_", "AI_BOT_MEDIUM_", "AI_BOT_LONG_"])]
        
        if not ai_trades:
            print("❌ No AI bot trades in last 7 days")
            return
        
        print(f"🤖 Found {len(ai_trades)} AI bot trades in last 7 days")
        
        # Group by day
        daily_stats = {}
        for deal in ai_trades:
            trade_date = datetime.fromtimestamp(deal.time).date()
            if trade_date not in daily_stats:
                daily_stats[trade_date] = {"trades": 0, "profit": 0, "wins": 0}
            
            daily_stats[trade_date]["trades"] += 1
            daily_stats[trade_date]["profit"] += deal.profit
            if deal.profit > 0:
                daily_stats[trade_date]["wins"] += 1
        
        print(f"\n📅 DAILY BREAKDOWN:")
        for date in sorted(daily_stats.keys(), reverse=True):
            stats = daily_stats[date]
            win_rate = (stats["wins"] / stats["trades"] * 100) if stats["trades"] > 0 else 0
            print(f"   {date}: {stats['trades']} trades | ${stats['profit']:.2f} P&L | {win_rate:.1f}% win rate")
        
    except Exception as e:
        print(f"❌ Error in detailed analysis: {e}")

def main():
    """Main analysis function."""
    print("🔍 MT5 TRADE HISTORY ANALYSIS")
    print("=" * 50)
    print("Analyzing AI bot trades by timeframe...")
    
    # Connect to MT5
    if not connect_to_mt5():
        return False
    
    try:
        # Get AI bot trades
        ai_trades = get_ai_bot_trades()
        
        if not ai_trades:
            print("❌ No AI bot trades found")
            return False
        
        # Analyze by timeframe
        analyze_trades_by_timeframe(ai_trades)
        
        # Get detailed analysis
        get_detailed_trade_analysis()
        
        print(f"\n✅ ANALYSIS COMPLETE")
        return True
        
    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return False
    
    finally:
        # Cleanup
        try:
            mt5.shutdown()
        except:
            pass

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
