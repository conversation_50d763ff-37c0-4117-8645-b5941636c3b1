{"timestamp": "2025-06-29T10:05:26.353423", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.53907310962677, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T10:05:26.417816", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7867552492284705, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T10:05:26.422839", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T10:05:26.635689", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5768088102340698, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T10:05:26.698641", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.36743597785691284, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T10:05:26.708956", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999605417251587, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T10:05:26.822213", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999994158744812, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T10:05:26.883237", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T10:05:26.888340", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T10:23:36.056400", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5036274194717407, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T10:23:36.128214", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8907601842601841, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T10:23:36.135023", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T10:23:36.342158", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9588975310325623, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T10:23:36.440946", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.26084822165428956, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T10:23:36.450425", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999456405639648, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T10:23:36.563572", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999933242797852, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T10:23:36.636693", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6948809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T10:23:36.642120", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T10:59:40.347849", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T10:59:40.418891", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T10:59:40.455596", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T10:59:40.691966", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T10:59:40.815878", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T10:59:40.876006", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T10:59:41.029462", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T10:59:41.131218", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T10:59:41.175387", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T11:03:21.289153", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T11:03:21.375728", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T11:03:21.411122", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T11:03:21.671651", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T11:03:21.770604", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T11:03:21.805990", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T11:03:21.952114", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T11:03:22.059742", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T11:03:22.076871", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T11:11:00.878678", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T11:11:00.961354", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T11:11:00.998778", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T11:11:01.232509", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T11:11:01.328889", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T11:11:01.376060", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T11:11:01.527522", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T11:11:01.623823", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T11:11:01.677335", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T11:17:49.027206", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T11:17:49.096213", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T11:17:49.144949", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T11:17:49.415286", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T11:17:49.501759", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T11:17:49.538017", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T11:17:49.688016", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T11:17:49.792700", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T11:17:49.823124", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T11:20:47.701515", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T11:20:47.785229", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T11:20:47.837494", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T11:20:47.926473", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T11:20:48.057908", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T11:20:48.085254", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T11:20:48.215642", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T11:20:48.324208", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T11:20:48.390349", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T11:23:48.720432", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T11:23:48.801357", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T11:23:48.855949", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T11:23:48.967760", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T11:23:49.060883", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T11:23:49.121564", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T11:23:49.246663", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T11:23:49.355645", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T11:23:49.406896", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T12:26:21.189467", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T12:26:21.488146", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T12:26:21.546451", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T12:26:21.841229", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T12:26:21.961124", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T12:26:22.321166", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T12:26:22.451516", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T12:26:22.544001", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T12:26:22.590507", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T13:11:53.209203", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T13:11:53.315757", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T13:11:53.321958", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T13:11:53.543846", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T13:11:53.637072", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T13:11:53.660648", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T13:11:53.806202", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T13:11:53.955722", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T13:11:54.050591", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T13:14:49.793988", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.48003172874450684, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T13:14:49.859427", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7871834967970596, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T13:14:49.865206", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T13:14:49.925584", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.49406230449676514, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T13:14:49.990521", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.30195198894784137, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T13:14:49.999787", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999674558639526, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T13:14:50.073483", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999988079071045, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T13:14:50.141965", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T13:14:50.147972", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T13:25:51.337630", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.48003172874450684, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T13:25:51.400471", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7871834967970596, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T13:25:51.406529", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T13:25:51.615338", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.49406230449676514, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T13:25:51.678486", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.30195198894784137, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T13:25:51.688528", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999674558639526, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T13:25:51.800684", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999988079071045, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T13:25:51.860949", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T13:25:51.866005", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T13:48:43.344513", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.3661978840827942, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T13:48:43.618970", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.5268920557014527, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T13:48:43.628372", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T13:48:43.832345", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.637872040271759, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T13:48:43.984310", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.36915677260611696, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T13:48:43.993344", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999516010284424, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T13:48:44.102695", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999918937683105, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T13:48:44.172204", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.58, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T13:48:44.177308", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T13:56:12.528091", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4780167043209076, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T13:56:12.759441", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9417071912892016, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T13:56:12.768922", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T13:56:13.024982", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998047947883606, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T13:56:13.135268", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7320669175611408, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T13:56:13.149919", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999983549118042, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T13:56:13.331152", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T13:56:13.485908", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.57, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T13:56:13.493605", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:04:40.708586", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.3095545768737793, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:04:40.835160", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.5761231407849445, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:04:40.861831", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:04:41.096637", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998494386672974, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:04:41.227065", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.46820549403955675, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:04:41.239364", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999868869781494, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:04:41.379386", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999973773956299, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:04:41.469142", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:04:41.554368", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:21:39.911420", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.2460981011390686, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:21:40.000125", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8360730436486786, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:21:40.013206", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:21:40.287985", "model_name": "medium_term_trend_lstm", "signal": -1, "confidence": 0.834588885307312, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:21:40.406725", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5724922962891662, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:21:40.419247", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999805688858032, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:21:40.549496", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999686479568481, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:21:40.616861", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5048809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:21:40.661918", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:24:35.525566", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4129871428012848, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:24:35.589954", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7080100015694749, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:24:35.596041", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:24:35.656443", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.6382809281349182, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:24:35.718958", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.8009514183474667, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:24:35.729149", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999977707862854, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:24:35.809797", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999723434448242, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:24:35.875514", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.5151190476190476, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:24:35.880540", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
