{"timestamp": "2025-06-29T10:05:26.353423", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.53907310962677, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T10:05:26.417816", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7867552492284705, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T10:05:26.422839", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T10:05:26.635689", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5768088102340698, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T10:05:26.698641", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.36743597785691284, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T10:05:26.708956", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999605417251587, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T10:05:26.822213", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999994158744812, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T10:05:26.883237", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T10:05:26.888340", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T10:23:36.056400", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5036274194717407, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T10:23:36.128214", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8907601842601841, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T10:23:36.135023", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T10:23:36.342158", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9588975310325623, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T10:23:36.440946", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.26084822165428956, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T10:23:36.450425", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999456405639648, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T10:23:36.563572", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999933242797852, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T10:23:36.636693", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6948809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T10:23:36.642120", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T10:59:40.347849", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T10:59:40.418891", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T10:59:40.455596", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T10:59:40.691966", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T10:59:40.815878", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T10:59:40.876006", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T10:59:41.029462", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T10:59:41.131218", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T10:59:41.175387", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T11:03:21.289153", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T11:03:21.375728", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T11:03:21.411122", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T11:03:21.671651", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T11:03:21.770604", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T11:03:21.805990", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T11:03:21.952114", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T11:03:22.059742", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T11:03:22.076871", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T11:11:00.878678", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T11:11:00.961354", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T11:11:00.998778", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T11:11:01.232509", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T11:11:01.328889", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T11:11:01.376060", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T11:11:01.527522", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T11:11:01.623823", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T11:11:01.677335", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T11:17:49.027206", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T11:17:49.096213", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T11:17:49.144949", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T11:17:49.415286", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T11:17:49.501759", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T11:17:49.538017", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T11:17:49.688016", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T11:17:49.792700", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T11:17:49.823124", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T11:20:47.701515", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T11:20:47.785229", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T11:20:47.837494", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T11:20:47.926473", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T11:20:48.057908", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T11:20:48.085254", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T11:20:48.215642", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T11:20:48.324208", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T11:20:48.390349", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T11:23:48.720432", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T11:23:48.801357", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T11:23:48.855949", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T11:23:48.967760", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T11:23:49.060883", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T11:23:49.121564", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T11:23:49.246663", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T11:23:49.355645", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T11:23:49.406896", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T12:26:21.189467", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T12:26:21.488146", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T12:26:21.546451", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T12:26:21.841229", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T12:26:21.961124", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T12:26:22.321166", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T12:26:22.451516", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T12:26:22.544001", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T12:26:22.590507", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T13:11:53.209203", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.263392835855484, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T13:11:53.315757", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937400793650794, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T13:11:53.321958", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T13:11:53.543846", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5921031832695007, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T13:11:53.637072", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8509946086049633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T13:11:53.660648", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T13:11:53.806202", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999845027923584, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T13:11:53.955722", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T13:11:54.050591", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T13:14:49.793988", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.48003172874450684, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T13:14:49.859427", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7871834967970596, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T13:14:49.865206", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T13:14:49.925584", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.49406230449676514, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T13:14:49.990521", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.30195198894784137, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T13:14:49.999787", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999674558639526, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T13:14:50.073483", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999988079071045, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T13:14:50.141965", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T13:14:50.147972", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T13:25:51.337630", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.48003172874450684, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T13:25:51.400471", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7871834967970596, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T13:25:51.406529", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T13:25:51.615338", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.49406230449676514, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T13:25:51.678486", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.30195198894784137, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T13:25:51.688528", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999674558639526, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T13:25:51.800684", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999988079071045, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T13:25:51.860949", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5798809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T13:25:51.866005", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T13:48:43.344513", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.3661978840827942, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T13:48:43.618970", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.5268920557014527, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T13:48:43.628372", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T13:48:43.832345", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.637872040271759, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T13:48:43.984310", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.36915677260611696, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T13:48:43.993344", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999516010284424, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T13:48:44.102695", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999918937683105, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T13:48:44.172204", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.58, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T13:48:44.177308", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T13:56:12.528091", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4780167043209076, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T13:56:12.759441", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9417071912892016, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T13:56:12.768922", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T13:56:13.024982", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998047947883606, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T13:56:13.135268", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7320669175611408, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T13:56:13.149919", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999983549118042, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T13:56:13.331152", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T13:56:13.485908", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.57, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T13:56:13.493605", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:04:40.708586", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.3095545768737793, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:04:40.835160", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.5761231407849445, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:04:40.861831", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:04:41.096637", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998494386672974, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:04:41.227065", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.46820549403955675, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:04:41.239364", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999868869781494, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:04:41.379386", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999973773956299, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:04:41.469142", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:04:41.554368", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:21:39.911420", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.2460981011390686, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:21:40.000125", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8360730436486786, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:21:40.013206", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:21:40.287985", "model_name": "medium_term_trend_lstm", "signal": -1, "confidence": 0.834588885307312, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:21:40.406725", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5724922962891662, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:21:40.419247", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999805688858032, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:21:40.549496", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999686479568481, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:21:40.616861", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5048809523809524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:21:40.661918", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:24:35.525566", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4129871428012848, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:24:35.589954", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7080100015694749, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:24:35.596041", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:24:35.656443", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.6382809281349182, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:24:35.718958", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.8009514183474667, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:24:35.729149", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999977707862854, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:24:35.809797", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999723434448242, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:24:35.875514", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.5151190476190476, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:24:35.880540", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:27:35.466227", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9033753275871277, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:27:35.534206", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8928259698207236, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:27:35.539430", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:27:35.598108", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.653270959854126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:27:35.661642", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7918268204551352, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:27:35.670800", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999717473983765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:27:35.745729", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999936819076538, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:27:35.811071", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5348809523809525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:27:35.816261", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:30:35.416044", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9033753275871277, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:30:35.478035", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8928259698207236, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:30:35.500000", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:30:35.556399", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.653270959854126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:30:35.616934", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7918268204551352, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:30:35.626713", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999717473983765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:30:35.702288", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999936819076538, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:30:35.764824", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5348809523809525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:30:35.770400", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:33:35.400439", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9033753275871277, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:33:35.461634", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8928259698207236, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:33:35.466689", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:33:35.522155", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.653270959854126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:33:35.582179", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7918268204551352, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:33:35.591310", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999717473983765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:33:35.667393", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999936819076538, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:33:35.731873", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5348809523809525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:33:35.737258", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:36:35.525343", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9033753275871277, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:36:35.761671", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8928259698207236, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:36:35.767100", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:36:35.823495", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.653270959854126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:36:35.885022", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7918268204551352, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:36:35.894212", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999717473983765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:36:35.969052", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999936819076538, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:36:36.034672", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5348809523809525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:36:36.040802", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:39:46.177909", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9033753275871277, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:39:46.354679", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8928259698207236, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:39:46.372259", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:39:46.431055", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.653270959854126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:39:46.493965", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7918268204551352, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:39:46.504035", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999717473983765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:39:46.582611", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999936819076538, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:39:46.645832", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5348809523809525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:39:46.650886", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:45:59.014722", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9033753275871277, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:45:59.220665", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8928259698207236, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:45:59.306863", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:45:59.418728", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.653270959854126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:45:59.522999", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7918268204551352, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:45:59.581001", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999717473983765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:45:59.680602", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999936819076538, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:45:59.773519", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5348809523809525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:45:59.808024", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:49:22.108352", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9033753275871277, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:49:22.211661", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8928259698207236, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:49:22.251952", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:49:22.366018", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.653270959854126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:49:22.438907", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7918268204551352, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:49:22.495402", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999717473983765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:49:22.610829", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999936819076538, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:49:22.695424", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5348809523809525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:49:22.712581", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:50:37.119689", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9033753275871277, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:50:37.324874", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8928259698207236, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:50:37.341016", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:50:37.459172", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.653270959854126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:50:37.519374", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7918268204551352, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:50:37.528856", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999717473983765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:50:37.615230", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999936819076538, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:50:37.678560", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.5348809523809525, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:50:37.683645", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:52:40.988052", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:52:41.064342", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:52:41.069425", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:52:41.127027", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:52:41.187837", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:52:41.197149", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:52:41.274342", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:52:41.340352", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:52:41.345684", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:55:27.946840", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:55:28.028079", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:55:28.034098", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:55:28.092109", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:55:28.151299", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:55:28.160668", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:55:28.235985", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:55:28.303374", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:55:28.307388", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T14:58:36.340538", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T14:58:36.474244", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T14:58:36.479640", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T14:58:36.541185", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T14:58:36.605977", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T14:58:36.615235", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T14:58:36.692808", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T14:58:36.759196", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T14:58:36.764749", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:01:49.692657", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:01:49.766097", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:01:49.771466", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:01:49.839224", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:01:49.903590", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:01:49.914568", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:01:50.011184", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:01:50.077803", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:01:50.082822", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:04:48.359749", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:04:48.502703", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:04:48.509932", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:04:48.582181", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:04:48.657125", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:04:48.666281", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:04:48.743854", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:04:48.811500", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:04:48.818912", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:07:38.582715", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:07:38.710115", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:07:38.715620", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:07:38.775257", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:07:38.837629", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:07:38.846920", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:07:38.923420", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:07:38.986836", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:07:38.992699", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:10:27.757611", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:10:27.867502", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:10:27.872716", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:10:27.929882", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:10:27.990294", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:10:28.000189", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:10:28.075143", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:10:28.138785", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:10:28.143938", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:14:59.119644", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:14:59.292642", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:14:59.302300", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:14:59.362585", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:14:59.425785", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:14:59.437338", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:14:59.522643", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:14:59.590036", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:14:59.596371", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:18:50.515456", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:18:50.715921", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:18:50.728976", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:18:50.841974", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:18:50.907071", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:18:50.917475", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:18:51.006068", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:18:51.072509", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:18:51.077782", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:19:39.897145", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:19:40.075269", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:19:40.080446", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:19:40.143666", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:19:40.208079", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:19:40.217202", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:19:40.291987", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:19:40.357578", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:19:40.362867", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:28:31.639365", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7812575697898865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:28:32.055445", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006327761636491, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:28:32.175392", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:28:32.495659", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9778667092323303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:28:32.587413", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8948092514597569, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:28:33.225922", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:28:33.358082", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:28:33.442377", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:28:33.485090", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:30:59.440540", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7134295105934143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:30:59.545925", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7120415764790765, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:30:59.551282", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:30:59.612345", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9865937232971191, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:30:59.674264", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6493168805746851, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:30:59.683788", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999744892120361, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:30:59.763112", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999740123748779, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:30:59.831034", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:30:59.836256", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:31:41.260553", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7134295105934143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:31:41.326333", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7120415764790765, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:31:41.332468", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:31:41.394489", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9865937232971191, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:31:41.456479", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6493168805746851, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:31:41.465558", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999744892120361, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:31:41.545487", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999740123748779, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:31:41.614097", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:31:41.619406", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:34:42.936899", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7134295105934143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:34:43.015475", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7120415764790765, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:34:43.020762", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:34:43.079609", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9865937232971191, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:34:43.140065", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6493168805746851, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:34:43.149976", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999744892120361, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:34:43.223768", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999740123748779, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:34:43.290566", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:34:43.295441", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:37:51.284684", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7134295105934143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:37:51.362825", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7120415764790765, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:37:51.368309", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:37:51.425945", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9865937232971191, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:37:51.489808", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6493168805746851, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:37:51.498294", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999744892120361, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:37:51.572950", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999740123748779, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:37:51.638960", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:37:51.644995", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:42:51.456422", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7134295105934143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:42:51.617986", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7120415764790765, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:42:51.626819", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:42:51.691832", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9865937232971191, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:42:51.765435", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6493168805746851, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:42:51.782903", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999744892120361, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:42:51.862100", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999740123748779, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:42:51.926143", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:42:51.933213", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:50:39.387949", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7134295105934143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:50:39.700248", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7120415764790765, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:50:39.802205", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:50:40.096026", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9865937232971191, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:50:40.183635", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6493168805746851, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:50:40.797412", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999744892120361, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:50:40.927774", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999740123748779, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:50:41.029277", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:50:41.074565", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:51:55.354413", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.3640727698802948, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:51:55.473318", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.728941533289579, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:51:55.478535", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:51:55.539288", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9881775975227356, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:51:55.600582", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.7810119165017284, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:51:55.611195", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999481439590454, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:51:55.691963", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999876022338867, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:51:55.759123", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.52, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:51:55.764200", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T15:53:58.774926", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.3640727698802948, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T15:53:58.863124", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.728941533289579, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T15:53:58.869171", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T15:53:58.929594", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9881775975227356, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T15:53:58.991562", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.7810119165017284, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T15:53:59.002430", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999481439590454, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T15:53:59.084968", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999876022338867, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T15:53:59.152300", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.52, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T15:53:59.157392", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T16:13:23.704537", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.3640727698802948, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T16:13:23.856030", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.728941533289579, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T16:13:23.926962", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T16:13:24.178184", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9881775975227356, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T16:13:24.302433", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.7810119165017284, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T16:13:24.521681", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999481439590454, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T16:13:24.639214", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999876022338867, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T16:13:24.717898", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.52, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T16:13:24.771952", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T16:16:11.287340", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.2927377223968506, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T16:16:11.357707", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8927561356103277, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T16:16:11.362760", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T16:16:11.426264", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6600503325462341, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T16:16:11.492694", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.3945708414017297, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T16:16:11.503483", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999829530715942, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T16:16:11.583557", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999946355819702, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T16:16:11.653162", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T16:16:11.658264", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T16:19:11.238126", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.2652581036090851, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T16:19:11.303582", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.5252576175742807, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T16:19:11.308632", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T16:19:11.372800", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.7828579545021057, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T16:19:11.438520", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6798720195011776, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T16:19:11.448537", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999732971191406, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T16:19:11.524585", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999973773956299, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T16:19:11.591134", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T16:19:11.596176", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T16:45:15.015786", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.2652581036090851, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T16:45:15.076621", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.5252576175742807, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T16:45:15.143528", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T16:45:15.392059", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.7828579545021057, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T16:45:15.822168", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6798720195011775, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T16:45:15.893029", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999732971191406, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T16:45:16.033427", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999973773956299, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T16:45:16.132894", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T16:45:16.139009", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T16:53:53.906145", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.531389594078064, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T16:53:53.999149", "model_name": "short_term_momentum_rf", "signal": -1, "confidence": 0.7413045167159311, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T16:53:54.006427", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T16:53:54.230495", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9814937114715576, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T16:53:54.323845", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.43260075375997253, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T16:53:54.426461", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999715089797974, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T16:53:54.543229", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999967813491821, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T16:53:54.643097", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T16:53:54.742522", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T16:56:50.285427", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.46791818737983704, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T16:56:50.347157", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.4543846200900126, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T16:56:50.353044", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T16:56:50.408197", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998974800109863, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T16:56:50.469429", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.3823449386276189, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T16:56:50.478519", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999833106994629, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T16:56:50.550714", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999997615814209, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T16:56:50.616435", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T16:56:50.620832", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T16:59:50.447437", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.25776785612106323, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T16:59:50.512859", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.39626912542903336, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T16:59:50.518263", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T16:59:50.576887", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9999393224716187, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T16:59:50.641369", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5646231850925835, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T16:59:50.650951", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999768733978271, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T16:59:50.728140", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999996423721313, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T16:59:50.793122", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T16:59:50.798627", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T17:08:03.741641", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.25776785612106323, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T17:08:03.811084", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.39626912542903336, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T17:08:03.891695", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T17:08:04.142442", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9999393224716187, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T17:08:04.208062", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5646231850925835, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T17:08:04.271421", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999768733978271, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T17:08:04.798679", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999996423721313, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T17:08:04.920821", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T17:08:04.966849", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T17:10:59.292337", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6234438419342041, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T17:10:59.352862", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8763256827493163, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T17:10:59.358882", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T17:10:59.415842", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9997586607933044, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T17:10:59.479756", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4387847554678117, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T17:10:59.489823", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999943733215332, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T17:10:59.564336", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999938011169434, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T17:10:59.631400", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T17:10:59.636764", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T17:13:59.284291", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7762830853462219, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T17:13:59.345035", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8987937494642303, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T17:13:59.350538", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T17:13:59.409066", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9999297857284546, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T17:13:59.470436", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.46152458155221426, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T17:13:59.479533", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999485015869141, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T17:13:59.554242", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999963045120239, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T17:13:59.618741", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T17:13:59.623799", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T17:16:59.391922", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7762830853462219, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T17:16:59.524332", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8987937494642303, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T17:16:59.579452", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T17:16:59.641787", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9999297857284546, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T17:16:59.746621", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.46152458155221426, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T17:16:59.803823", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999485015869141, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T17:16:59.881427", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999963045120239, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T17:16:59.983713", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T17:17:00.022974", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T17:59:02.585490", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4874117076396942, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T17:59:02.692287", "model_name": "short_term_momentum_rf", "signal": 2, "confidence": 0.4266517041500974, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T17:59:02.699168", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T17:59:02.913783", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.5094422101974487, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T17:59:03.048149", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.5646866958652638, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T17:59:03.060924", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999711513519287, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T17:59:03.192911", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999884366989136, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T17:59:03.265922", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T17:59:03.349916", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T18:01:58.042292", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5897655487060547, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T18:01:58.105516", "model_name": "short_term_momentum_rf", "signal": -1, "confidence": 0.6209232637507612, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T18:01:58.240273", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T18:01:58.302243", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9875281453132629, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T18:01:58.365814", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.47482465229113474, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T18:01:58.376102", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999761581420898, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T18:01:58.451170", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999890327453613, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T18:01:58.519094", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T18:01:58.524115", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T18:04:58.176701", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5897655487060547, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T18:04:58.237378", "model_name": "short_term_momentum_rf", "signal": -1, "confidence": 0.6209232637507612, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T18:04:58.384268", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T18:04:58.442237", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9875281453132629, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T18:04:58.503901", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.47482465229113463, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T18:04:58.513256", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999761581420898, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T18:04:58.590615", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999890327453613, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T18:04:58.655014", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T18:04:58.659106", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T18:07:58.199534", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5897655487060547, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T18:07:58.261913", "model_name": "short_term_momentum_rf", "signal": -1, "confidence": 0.6209232637507612, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T18:07:58.266988", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T18:07:58.325648", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9875281453132629, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T18:07:58.388968", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.47482465229113463, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T18:07:58.399008", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999761581420898, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T18:07:58.474084", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999890327453613, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T18:07:58.538477", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T18:07:58.543523", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T18:13:15.931090", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6210857033729553, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T18:13:15.994237", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8718366418175225, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T18:13:15.999429", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T18:13:16.218288", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9997472167015076, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T18:13:16.279888", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8595368952686383, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T18:13:16.292113", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999624490737915, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T18:13:16.411543", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999932050704956, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T18:13:16.473110", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.52, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T18:13:16.479281", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T18:16:14.105996", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6210857033729553, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T18:16:14.173120", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8718366418175225, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T18:16:14.178001", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T18:16:14.237344", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9997472167015076, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T18:16:14.300701", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8595368952686384, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T18:16:14.310238", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999624490737915, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T18:16:14.389015", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999932050704956, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T18:16:14.455639", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.52, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T18:16:14.460977", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T18:32:21.993288", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8200814127922058, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T18:32:22.058163", "model_name": "short_term_momentum_rf", "signal": -1, "confidence": 0.5851342944464615, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T18:32:22.083306", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T18:32:22.332062", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.993528425693512, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T18:32:22.440142", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5363687657201144, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T18:32:22.503182", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999855756759644, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T18:32:22.621741", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999984502792358, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T18:32:22.694247", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.535, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T18:32:22.738527", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T18:35:17.429691", "model_name": "short_term_pattern_nn", "signal": -1, "confidence": 0.27050527930259705, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T18:35:17.491422", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9341690537020446, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T18:35:17.626273", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T18:35:17.686571", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998205304145813, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T18:35:17.749411", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5051192151183321, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T18:35:17.758542", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999909400939941, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T18:35:17.838960", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999971389770508, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T18:35:17.906557", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.52, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T18:35:17.911735", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T18:38:11.690977", "model_name": "short_term_pattern_nn", "signal": -1, "confidence": 0.27050527930259705, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T18:38:11.761950", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9341690537020446, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T18:38:11.797020", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T18:38:12.017559", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998205304145813, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T18:38:12.091012", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5051192151183322, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T18:38:12.107948", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999909400939941, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T18:38:12.258497", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999971389770508, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T18:38:12.350603", "model_name": "long_term_levels_rf", "signal": 1, "confidence": 0.52, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T18:38:12.390608", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T18:48:24.169125", "model_name": "short_term_pattern_nn", "signal": -1, "confidence": 0.31821325421333313, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T18:48:24.254731", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9186446177383674, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T18:48:24.263784", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T18:48:24.518495", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998416900634766, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T18:48:24.609057", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5916729258994649, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T18:48:24.623656", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999968409538269, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T18:48:24.799422", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999945163726807, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T18:48:24.955142", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T18:48:24.961233", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T18:51:19.897160", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.924522340297699, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T18:51:19.962001", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9303534530321447, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T18:51:19.967467", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T18:51:20.030437", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9997380375862122, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T18:51:20.096734", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6139703318675802, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T18:51:20.106874", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999968409538269, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T18:51:20.190370", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999932050704956, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T18:51:20.256047", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T18:51:20.261578", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T19:03:21.529191", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.924522340297699, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T19:03:21.942765", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9303534530321447, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T19:03:21.977615", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T19:03:22.299468", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9997380375862122, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T19:03:22.467469", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6139703318675802, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T19:03:22.482992", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999968409538269, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T19:03:22.617279", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999932050704956, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T19:03:22.722410", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T19:03:22.802016", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T19:06:17.121742", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.35276785492897034, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T19:06:17.208936", "model_name": "short_term_momentum_rf", "signal": -2, "confidence": 0.45278681767959517, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T19:06:17.214450", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T19:06:17.280210", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.41573387384414673, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T19:06:17.347588", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6350153937336607, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T19:06:17.358887", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999709129333496, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T19:06:17.438218", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999958276748657, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T19:06:17.504974", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T19:06:17.511405", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T19:14:19.686804", "model_name": "short_term_pattern_nn", "signal": 0, "confidence": 0.3050873577594757, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T19:14:19.818541", "model_name": "short_term_momentum_rf", "signal": -2, "confidence": 0.4560693822512465, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T19:14:19.830718", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T19:14:20.126282", "model_name": "medium_term_trend_lstm", "signal": 1, "confidence": 0.44262388348579407, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T19:14:20.420406", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5874517383430262, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T19:14:20.517767", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999717473983765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T19:14:20.634349", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999984502792358, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T19:14:20.748565", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T19:14:20.833815", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T19:17:15.271922", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9227360486984253, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T19:17:15.336984", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.930066260358436, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T19:17:15.343103", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T19:17:15.399970", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9990054965019226, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T19:17:15.460959", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7800265836972801, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T19:17:15.470124", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999984622001648, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T19:17:15.547857", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999967813491821, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T19:17:15.616129", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T19:17:15.621224", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T19:20:15.494612", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9227360486984253, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T19:20:15.559247", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.930066260358436, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T19:20:15.564270", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T19:20:15.624709", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9990054965019226, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T19:20:15.689599", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7800265836972801, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T19:20:15.698721", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999984622001648, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T19:20:15.777316", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999967813491821, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T19:20:15.845785", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T19:20:15.851080", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T19:23:15.640101", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5349273681640625, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T19:23:15.917219", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9347282023633885, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T19:23:15.922272", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T19:23:15.979199", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998112320899963, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T19:23:16.040254", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6908580385095433, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T19:23:16.049390", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999990701675415, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T19:23:16.125151", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999959468841553, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T19:23:16.191104", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T19:23:16.196122", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T19:26:15.580670", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5349273681640625, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T19:26:15.649164", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9347282023633884, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T19:26:15.654188", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T19:26:15.714203", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998112320899963, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T19:26:15.780480", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6908580385095432, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T19:26:15.789545", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999990701675415, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T19:26:15.870293", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999959468841553, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T19:26:15.937438", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T19:26:15.942462", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-06-29T19:29:15.315880", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5349273681640625, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-06-29T19:29:15.379350", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9347282023633884, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-06-29T19:29:15.385377", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-06-29T19:29:15.445937", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.9998112320899963, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-06-29T19:29:15.510163", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6908580385095432, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-06-29T19:29:15.520281", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999990701675415, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-06-29T19:29:15.605884", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999959468841553, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-06-29T19:29:15.674231", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-06-29T19:29:15.679752", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999998524274585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
