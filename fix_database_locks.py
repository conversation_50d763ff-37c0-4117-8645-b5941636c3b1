#!/usr/bin/env python3
"""
Script to fix database lock issues and clean up any stale connections.
"""

import os
import sys
import sqlite3
import time
import logging
import glob
from pathlib import Path

# Add current directory to path
sys.path.append('.')

import config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_database_files():
    """Find all database files in the project."""
    db_files = []
    
    # Check configured location
    main_db = os.path.join(config.DATA_CACHE_DIR, "synthetic_data.db")
    if os.path.exists(main_db):
        db_files.append(main_db)
    
    # Check root directory
    root_db = "synthetic_data.db"
    if os.path.exists(root_db):
        db_files.append(root_db)
    
    # Search for any other .db files
    for pattern in ["*.db", "data/**/*.db"]:
        for db_file in glob.glob(pattern, recursive=True):
            if db_file not in db_files:
                db_files.append(db_file)
    
    return db_files

def check_database_locks(db_path):
    """Check if a database has lock issues."""
    try:
        logger.info(f"Checking database: {db_path}")
        
        # Try to connect with a short timeout
        conn = sqlite3.connect(db_path, timeout=5.0)
        
        # Check WAL mode
        cursor = conn.cursor()
        cursor.execute("PRAGMA journal_mode;")
        journal_mode = cursor.fetchone()[0]
        logger.info(f"  Journal mode: {journal_mode}")
        
        # Check for active connections
        cursor.execute("PRAGMA wal_checkpoint;")
        checkpoint_result = cursor.fetchone()
        logger.info(f"  WAL checkpoint result: {checkpoint_result}")
        
        # Test a simple operation
        cursor.execute("SELECT COUNT(*) FROM sqlite_master;")
        count = cursor.fetchone()[0]
        logger.info(f"  Tables count: {count}")
        
        conn.close()
        logger.info(f"  ✅ Database {db_path} is accessible")
        return True
        
    except sqlite3.OperationalError as e:
        if "database is locked" in str(e).lower():
            logger.error(f"  ❌ Database {db_path} is LOCKED: {e}")
            return False
        else:
            logger.error(f"  ❌ Database {db_path} error: {e}")
            return False
    except Exception as e:
        logger.error(f"  ❌ Unexpected error with {db_path}: {e}")
        return False

def fix_database_locks(db_path):
    """Attempt to fix database lock issues."""
    try:
        logger.info(f"Attempting to fix locks for: {db_path}")
        
        # Check for WAL and SHM files
        wal_file = db_path + "-wal"
        shm_file = db_path + "-shm"
        
        # Force WAL checkpoint and cleanup
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # Set optimal pragmas
        cursor.execute("PRAGMA journal_mode=WAL;")
        cursor.execute("PRAGMA busy_timeout=30000;")
        cursor.execute("PRAGMA synchronous=NORMAL;")
        
        # Force checkpoint
        cursor.execute("PRAGMA wal_checkpoint(TRUNCATE);")
        result = cursor.fetchone()
        logger.info(f"  WAL checkpoint result: {result}")
        
        # Vacuum if needed
        cursor.execute("PRAGMA integrity_check;")
        integrity = cursor.fetchone()[0]
        if integrity == "ok":
            logger.info("  Database integrity OK")
        else:
            logger.warning(f"  Database integrity issue: {integrity}")
        
        conn.close()
        
        # Small delay to ensure cleanup
        time.sleep(1)
        
        # Test if fix worked
        if check_database_locks(db_path):
            logger.info(f"  ✅ Successfully fixed locks for {db_path}")
            return True
        else:
            logger.error(f"  ❌ Failed to fix locks for {db_path}")
            return False
            
    except Exception as e:
        logger.error(f"  ❌ Error fixing locks for {db_path}: {e}")
        return False

def cleanup_stale_files(db_path):
    """Clean up stale WAL and SHM files if database is not in use."""
    try:
        wal_file = db_path + "-wal"
        shm_file = db_path + "-shm"
        
        # Check if files exist
        wal_exists = os.path.exists(wal_file)
        shm_exists = os.path.exists(shm_file)
        
        if not wal_exists and not shm_exists:
            logger.info(f"  No WAL/SHM files to clean for {db_path}")
            return True
        
        logger.info(f"  Found WAL: {wal_exists}, SHM: {shm_exists}")
        
        # Try to connect and checkpoint
        conn = sqlite3.connect(db_path, timeout=5.0)
        cursor = conn.cursor()
        cursor.execute("PRAGMA wal_checkpoint(TRUNCATE);")
        conn.close()
        
        # Check if files were cleaned up
        wal_after = os.path.exists(wal_file)
        shm_after = os.path.exists(shm_file)
        
        if not wal_after and not shm_after:
            logger.info(f"  ✅ Successfully cleaned up WAL/SHM files for {db_path}")
            return True
        else:
            logger.warning(f"  ⚠️  Some files remain: WAL: {wal_after}, SHM: {shm_after}")
            return False
            
    except Exception as e:
        logger.error(f"  ❌ Error cleaning up files for {db_path}: {e}")
        return False

def main():
    """Main function to fix database locks."""
    logger.info("=" * 60)
    logger.info("DATABASE LOCK FIX UTILITY")
    logger.info("=" * 60)
    
    # Find all database files
    db_files = find_database_files()
    
    if not db_files:
        logger.info("No database files found.")
        return True
    
    logger.info(f"Found {len(db_files)} database file(s):")
    for db_file in db_files:
        logger.info(f"  - {db_file}")
    
    logger.info("\n" + "=" * 40)
    logger.info("CHECKING DATABASE LOCKS")
    logger.info("=" * 40)
    
    locked_databases = []
    
    # Check each database
    for db_file in db_files:
        if not check_database_locks(db_file):
            locked_databases.append(db_file)
    
    if not locked_databases:
        logger.info("\n✅ All databases are accessible - no locks detected!")
        return True
    
    logger.info(f"\n⚠️  Found {len(locked_databases)} locked database(s)")
    
    logger.info("\n" + "=" * 40)
    logger.info("FIXING DATABASE LOCKS")
    logger.info("=" * 40)
    
    fixed_count = 0
    
    # Attempt to fix each locked database
    for db_file in locked_databases:
        if fix_database_locks(db_file):
            fixed_count += 1
        
        # Also cleanup stale files
        cleanup_stale_files(db_file)
    
    logger.info("\n" + "=" * 60)
    if fixed_count == len(locked_databases):
        logger.info("✅ ALL DATABASE LOCKS FIXED SUCCESSFULLY!")
    else:
        logger.warning(f"⚠️  Fixed {fixed_count}/{len(locked_databases)} databases")
        logger.warning("Some databases may still have issues")
    logger.info("=" * 60)
    
    return fixed_count == len(locked_databases)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
